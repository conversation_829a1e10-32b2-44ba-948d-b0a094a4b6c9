#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LIS系统多设备集成模块

该模块提供了将多设备支持集成到现有lis_system.py的功能。
它可以作为可选模块使用，在现有系统的基础上添加多设备支持。

主要功能：
1. 提供多设备消息处理的包装器
2. 与现有LIS系统无缝集成
3. 向后兼容，不影响现有功能
4. 可以通过配置开关启用/禁用
"""

import logging
from typing import Dict, Any, Optional, Callable

# 导入多设备支持组件
try:
    from config_manager import get_config_manager
    from message_router import MessageRouter
    from device_adapter import StandardMessage, UnsupportedProtocolError
    from adapters.yingkai_adapter import YingKaiHL7Adapter
    MULTI_DEVICE_AVAILABLE = True
except ImportError as e:
    MULTI_DEVICE_AVAILABLE = False
    print(f"多设备模块不可用: {e}")

class LISMultiDeviceIntegration:
    """LIS多设备集成类
    
    该类作为现有LIS系统和多设备支持之间的桥梁，
    提供可选的多设备消息处理功能。
    """
    
    def __init__(self, legacy_message_handler: Callable = None, config_file: str = None):
        """初始化多设备集成
        
        Args:
            legacy_message_handler: 现有的消息处理函数
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        self.legacy_message_handler = legacy_message_handler
        self.multi_device_enabled = False
        self.message_router = None
        
        # 检查多设备支持是否可用
        if not MULTI_DEVICE_AVAILABLE:
            self.logger.warning("多设备模块不可用，使用传统单设备模式")
            return
        
        # 尝试加载配置和初始化多设备支持
        try:
            self.config_manager = get_config_manager(config_file)
            self.multi_device_enabled = self.config_manager.is_multi_device_enabled()
            
            if self.multi_device_enabled:
                self._init_multi_device_support()
                
        except Exception as e:
            self.logger.error(f"多设备初始化失败，回退到传统模式: {e}")
            self.multi_device_enabled = False
    
    def _init_multi_device_support(self):
        """初始化多设备支持"""
        try:
            # 构建路由器配置
            router_config = {
                'adapters': self.config_manager.get_adapters_config(),
                'devices': self.config_manager.get_devices_config(),
                'router': self.config_manager.get_router_config()
            }
            
            # 创建消息路由器
            self.message_router = MessageRouter(router_config)
            
            # 手动注册YingKai适配器（确保向后兼容）
            yingkai_config = self.config_manager.get_section('ADAPTER_yingkai')
            if yingkai_config:
                yingkai_adapter = YingKaiHL7Adapter(yingkai_config)
                self.message_router.register_adapter('yingkai', yingkai_adapter)
            
            self.logger.info("多设备支持初始化成功")
            
        except Exception as e:
            self.logger.error(f"多设备支持初始化失败: {e}")
            self.multi_device_enabled = False
            raise
    
    def process_message(self, message_data: bytes, connection_info: Dict[str, Any]) -> bytes:
        """处理设备消息
        
        该方法首先尝试使用多设备路由器处理消息，
        如果失败或不支持，则回退到传统处理方式。
        
        Args:
            message_data: 消息数据
            connection_info: 连接信息
            
        Returns:
            响应消息
        """
        # 如果多设备支持未启用，直接使用传统处理
        if not self.multi_device_enabled or not self.message_router:
            return self._process_with_legacy(message_data, connection_info)
        
        try:
            # 尝试多设备路由处理
            return self._process_with_multi_device(message_data, connection_info)
            
        except UnsupportedProtocolError:
            # 协议不支持，回退到传统处理
            self.logger.info("协议不支持多设备路由，使用传统处理")
            return self._process_with_legacy(message_data, connection_info)
            
        except Exception as e:
            # 其他错误，记录日志并回退到传统处理
            self.logger.error(f"多设备处理失败，回退到传统处理: {e}")
            return self._process_with_legacy(message_data, connection_info)
    
    def _process_with_multi_device(self, message_data: bytes, connection_info: Dict[str, Any]) -> bytes:
        """使用多设备路由器处理消息"""
        # 路由消息到合适的适配器
        standard_message = self.message_router.route_message(message_data, connection_info)
        
        # 如果有传统处理器，调用它来处理标准化消息
        if self.legacy_message_handler:
            # 将标准化消息转换回原始格式，以便传统处理器可以处理
            # 这里可以根据需要进行适配
            self.legacy_message_handler(standard_message.raw_message, connection_info)
        
        # 生成响应
        response = self.message_router.generate_response(standard_message)
        
        self.logger.info(
            f"多设备处理成功 - 设备: {standard_message.device_info.device_type}, "
            f"协议: {standard_message.device_info.protocol}"
        )
        
        return response
    
    def _process_with_legacy(self, message_data: bytes, connection_info: Dict[str, Any]) -> bytes:
        """使用传统方式处理消息"""
        if self.legacy_message_handler:
            # 调用传统消息处理器
            return self.legacy_message_handler(message_data, connection_info)
        else:
            # 没有传统处理器，返回基本响应
            self.logger.warning("没有可用的消息处理器")
            return b"NAK\\r"
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = {
            'multi_device_available': MULTI_DEVICE_AVAILABLE,
            'multi_device_enabled': self.multi_device_enabled,
            'legacy_handler_available': self.legacy_message_handler is not None
        }
        
        if self.multi_device_enabled and self.message_router:
            info['router_stats'] = self.message_router.get_statistics()
            info['available_adapters'] = self.message_router.get_available_adapters()
        
        return info

# =============================================================================
# 便利函数
# =============================================================================

def create_integration_wrapper(legacy_handler: Callable = None, config_file: str = None) -> LISMultiDeviceIntegration:
    """创建多设备集成包装器
    
    这是一个便利函数，用于快速创建多设备集成实例。
    
    Args:
        legacy_handler: 现有的消息处理函数
        config_file: 配置文件路径
        
    Returns:
        多设备集成实例
    """
    return LISMultiDeviceIntegration(legacy_handler, config_file)

def is_multi_device_available() -> bool:
    """检查多设备支持是否可用"""
    return MULTI_DEVICE_AVAILABLE

# =============================================================================
# 使用示例
# =============================================================================

if __name__ == "__main__":
    # 示例：如何在现有LIS系统中集成多设备支持
    
    def example_legacy_handler(message: bytes, connection_info: Dict[str, Any]) -> bytes:
        """示例传统消息处理函数"""
        print(f"传统处理器收到消息: {len(message)} 字节")
        return b"ACK\\r"
    
    # 创建集成包装器
    integration = create_integration_wrapper(
        legacy_handler=example_legacy_handler,
        config_file='config_multi_device.ini'
    )
    
    # 使用集成包装器处理消息
    test_message = b"MSH|^~\\\\&|LIS|Hospital|LIS|Hospital|20231201120000||ORU^R01|12345|P|2.5\\r"
    test_connection = {'address': ('*************', 22010)}
    
    response = integration.process_message(test_message, test_connection)
    print(f"响应: {response}")
    
    # 获取系统信息
    info = integration.get_system_info()
    print(f"系统信息: {info}")