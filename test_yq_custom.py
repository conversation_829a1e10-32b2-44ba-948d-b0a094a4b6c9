#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
YQ自定义功能测试

测试不同YQ配置模式下的数据库访问逻辑
"""

import logging
import sys
from datetime import datetime
from device_adapter import DeviceInfo, StandardMessage, StandardPatient, StandardOrder, StandardTestResult

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_yq_custom_mode():
    """测试自定义YQ模式"""
    print("=== 测试自定义YQ模式 ===")
    
    # 创建设备信息，配置自定义YQ
    device_info = DeviceInfo(
        device_id="yingkai_lab1",
        device_type="YingKai_LIS", 
        manufacturer="YingKai Bio",
        model="LIS_V112",
        protocol="HL7",
        version="2.5",
        ip_address="*************",
        port=22010,
        yq_config={
            'yq_source': 'custom',
            'yq_value': 'I2900'
        }
    )
    
    # 测试YQ处理
    from adapters.yingkai_adapter import YingKaiHL7Adapter
    adapter = YingKaiHL7Adapter({})
    
    # 创建测试消息
    message = StandardMessage(
        message_type="ORU",
        message_id="TEST001",
        timestamp=datetime.now(),
        device_info=device_info,
        patient=StandardPatient(patient_id="P001"),
        orders=[]
    )
    
    # 测试YQ值获取
    raw_yq = "ORIGINAL_YQ"
    effective_yq = adapter.get_yq_value(message, raw_yq)
    
    print(f"原始YQ: {raw_yq}")
    print(f"有效YQ: {effective_yq}")
    assert effective_yq == "I2900", f"预期 I2900，实际 {effective_yq}"
    print("✓ 自定义YQ模式测试通过")

def test_yq_device_id_mode():
    """测试设备ID YQ模式"""
    print("\n=== 测试设备ID YQ模式 ===")
    
    device_info = DeviceInfo(
        device_id="yingkai_lab2",
        device_type="YingKai_LIS",
        manufacturer="YingKai Bio", 
        model="LIS_V111",
        protocol="HL7",
        version="2.5",
        yq_config={
            'yq_source': 'device_id',
            'yq_prefix': 'LAB2_'
        }
    )
    
    from adapters.yingkai_adapter import YingKaiHL7Adapter
    adapter = YingKaiHL7Adapter({})
    
    message = StandardMessage(
        message_type="ORU",
        message_id="TEST002", 
        timestamp=datetime.now(),
        device_info=device_info,
        patient=StandardPatient(patient_id="P002"),
        orders=[]
    )
    
    raw_yq = "ORIGINAL_YQ"
    effective_yq = adapter.get_yq_value(message, raw_yq)
    
    print(f"原始YQ: {raw_yq}")
    print(f"有效YQ: {effective_yq}")
    expected = "LAB2_yingkai_lab2"
    assert effective_yq == expected, f"预期 {expected}，实际 {effective_yq}"
    print("✓ 设备ID YQ模式测试通过")

def test_yq_mapping_mode():
    """测试映射YQ模式"""
    print("\n=== 测试映射YQ模式 ===")
    
    device_info = DeviceInfo(
        device_id="abbott_lab1",
        device_type="Abbott_iSTAT",
        manufacturer="Abbott",
        model="i-STAT_1", 
        protocol="ASTM",
        version="E1394",
        yq_config={
            'yq_source': 'mapping',
            'yq_mapping': {
                'ABBOTT': 'ABT001',
                'iSTAT': 'ABT002'
            }
        }
    )
    
    from adapters.yingkai_adapter import YingKaiHL7Adapter
    adapter = YingKaiHL7Adapter({})
    
    message = StandardMessage(
        message_type="ORU",
        message_id="TEST003",
        timestamp=datetime.now(),
        device_info=device_info,
        patient=StandardPatient(patient_id="P003"),
        orders=[]
    )
    
    # 测试映射
    test_cases = [
        ("ABBOTT", "ABT001"),
        ("iSTAT", "ABT002"),
        ("UNKNOWN", "UNKNOWN")  # 未映射的值应保持原样
    ]
    
    for raw_yq, expected in test_cases:
        effective_yq = adapter.get_yq_value(message, raw_yq)
        print(f"原始YQ: {raw_yq} -> 有效YQ: {effective_yq}")
        assert effective_yq == expected, f"预期 {expected}，实际 {effective_yq}"
    
    print("✓ 映射YQ模式测试通过")

def test_yq_default_mode():
    """测试默认YQ模式（无配置）"""
    print("\n=== 测试默认YQ模式 ===")
    
    device_info = DeviceInfo(
        device_id="default_device",
        device_type="Unknown",
        manufacturer="Unknown",
        model="Unknown",
        protocol="Unknown",
        version="",
        yq_config={}  # 无YQ配置
    )
    
    from adapters.yingkai_adapter import YingKaiHL7Adapter
    adapter = YingKaiHL7Adapter({})
    
    message = StandardMessage(
        message_type="ORU",
        message_id="TEST004",
        timestamp=datetime.now(),
        device_info=device_info,
        patient=StandardPatient(patient_id="P004"),
        orders=[]
    )
    
    raw_yq = "ORIGINAL_YQ"
    effective_yq = adapter.get_yq_value(message, raw_yq)
    
    print(f"原始YQ: {raw_yq}")
    print(f"有效YQ: {effective_yq}")
    assert effective_yq == raw_yq, f"默认模式应返回原始值，预期 {raw_yq}，实际 {effective_yq}"
    print("✓ 默认YQ模式测试通过")

def test_database_access_simulation():
    """模拟数据库访问逻辑测试"""
    print("\n=== 模拟数据库访问逻辑测试 ===")
    
    # 创建模拟的HL7Message类
    class MockHL7Message:
        def __init__(self, sending_facility):
            self.message_control_id = "MSG001"
            self.sending_facility = sending_facility
            self.patient = StandardPatient(
                patient_id="P001",
                name="张三",
                sex="M",
                age="30",
                department="内科"
            )
            self.orders = [
                StandardOrder(
                    order_id="ORDER001",
                    sample_id="SAMPLE001",
                    results=[
                        StandardTestResult(
                            test_code="GLU",
                            test_name="葡萄糖",
                            value="5.5",
                            unit="mmol/L"
                        )
                    ]
                )
            ]
    
    # 模拟LIS系统的_get_effective_yq方法
    def mock_get_effective_yq(message_object, raw_yq):
        if hasattr(message_object, 'device_info') and message_object.device_info:
            device_info = message_object.device_info
            yq_config = getattr(device_info, 'yq_config', {})
            
            if yq_config:
                yq_source = yq_config.get('yq_source', 'message')
                
                if yq_source == 'custom':
                    return yq_config.get('yq_value', raw_yq)
                elif yq_source == 'device_id':
                    prefix = yq_config.get('yq_prefix', '')
                    return f"{prefix}{device_info.device_id}"
                elif yq_source == 'mapping':
                    mapping = yq_config.get('yq_mapping', {})
                    return mapping.get(raw_yq, raw_yq)
        
        if hasattr(message_object, 'sending_facility'):
            return message_object.sending_facility
        
        return raw_yq
    
    # 测试不同场景
    test_scenarios = [
        {
            'name': '自定义YQ场景',
            'yq_config': {'yq_source': 'custom', 'yq_value': 'CUSTOM_I2900'},
            'raw_yq': 'ORIGINAL',
            'expected': 'CUSTOM_I2900'
        },
        {
            'name': '设备ID YQ场景',
            'yq_config': {'yq_source': 'device_id', 'yq_prefix': 'DEV_'},
            'raw_yq': 'ORIGINAL',
            'expected': 'DEV_test_device'
        },
        {
            'name': '映射YQ场景',
            'yq_config': {'yq_source': 'mapping', 'yq_mapping': {'ORIGINAL': 'MAPPED_YQ'}},
            'raw_yq': 'ORIGINAL',
            'expected': 'MAPPED_YQ'
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n测试 {scenario['name']}:")
        
        # 创建带设备信息的StandardMessage
        device_info = DeviceInfo(
            device_id="test_device",
            device_type="Test",
            manufacturer="Test",
            model="Test",
            protocol="Test",
            version="1.0",
            yq_config=scenario['yq_config']
        )
        
        message = StandardMessage(
            message_type="ORU",
            message_id="TEST",
            timestamp=datetime.now(),
            device_info=device_info,
            patient=StandardPatient(patient_id="P001"),
            orders=[]
        )
        
        effective_yq = mock_get_effective_yq(message, scenario['raw_yq'])
        print(f"  原始YQ: {scenario['raw_yq']}")
        print(f"  有效YQ: {effective_yq}")
        print(f"  预期YQ: {scenario['expected']}")
        
        assert effective_yq == scenario['expected'], f"YQ处理失败"
        print(f"  ✓ {scenario['name']} 测试通过")
    
    print("\n✓ 所有数据库访问逻辑测试通过")

if __name__ == "__main__":
    try:
        test_yq_custom_mode()
        test_yq_device_id_mode()
        test_yq_mapping_mode()
        test_yq_default_mode()
        test_database_access_simulation()
        
        print("\n" + "="*50)
        print("🎉 所有YQ自定义功能测试全部通过！")
        print("="*50)
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        sys.exit(1)