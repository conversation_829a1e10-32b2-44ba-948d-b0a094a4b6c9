#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
瑞美LIS解码系统 - 配置切换工具
用于切换config.ini配置和自动运行对应的注册表文件
"""

import os
import sys
import json
import configparser
import subprocess
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import base64
from pathlib import Path

def encrypt_data(data: str) -> str:
    """使用简单的Base64编码加密数据"""
    # 先反转字符串，然后进行base64编码
    reversed_data = data[::-1]
    return base64.b64encode(reversed_data.encode()).decode()

def decrypt_data(encrypted_data: str) -> str:
    """解密Base64编码的数据"""
    try:
        # base64解码，然后反转字符串
        decoded = base64.b64decode(encrypted_data.encode()).decode()
        return decoded[::-1]
    except Exception:
        # 如果解密失败，返回原始数据
        return encrypted_data

def is_encrypted(data: str) -> bool:
    """检查数据是否已加密"""
    if not data:
        return False

    try:
        # 尝试base64解码
        decoded = base64.b64decode(data.encode())
        # 检查解码后的数据是否包含可打印字符
        decoded_str = decoded.decode('utf-8')
        # 如果解码成功且包含合理的字符，认为是加密的
        return len(decoded_str) > 0 and all(c.isprintable() or c.isspace() for c in decoded_str)
    except Exception:
        # 如果base64解码失败，认为不是加密的
        return False

class ConfigManager:
    def __init__(self):
        self.config_file = "config.ini"
        self.db_configs_file = "db_configs.json"
        self.configs = {}
        self.current_config = None
        self.load_configs()
    
    def load_configs(self):
        """加载数据库配置"""
        try:
            if os.path.exists(self.db_configs_file):
                with open(self.db_configs_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.configs = {k: v for k, v in data.items() if k != "__current__"}
                    self.current_config = data.get("__current__")
            else:
                # 创建默认配置
                self.create_default_config()
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {str(e)}")
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置"""
        self.configs = {
            "默认配置": {
                "name": "默认配置",
                "server": "81.70.17.88",
                "database": "lis2002",
                "username": "anQ=",
                "password": "bmFpaml0bmVyYWlq",
                "bat_file": ""
            }
        }
        self.current_config = "默认配置"
        self.save_configs()
    
    def save_configs(self):
        """保存配置到文件"""
        try:
            data = self.configs.copy()
            data["__current__"] = self.current_config
            with open(self.db_configs_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
    
    def update_config_ini(self, config_name):
        """更新config.ini文件"""
        if config_name not in self.configs:
            return False
        
        config = self.configs[config_name]
        
        try:
            # 读取现有的config.ini
            parser = configparser.ConfigParser()
            if os.path.exists(self.config_file):
                parser.read(self.config_file, encoding='utf-8')
            
            # 确保DATABASE节存在
            if not parser.has_section('DATABASE'):
                parser.add_section('DATABASE')
            
            # 更新数据库配置
            parser.set('DATABASE', 'server', config['server'])
            parser.set('DATABASE', 'database', config['database'])

            # 确保用户名和密码都是加密的
            # 如果db_configs.json中的数据已经是加密的，直接使用
            # 如果是明文，则加密后使用
            username = config['username']
            password = config['password']

            # 检查是否需要加密
            if is_encrypted(username):
                encrypted_username = username  # 已经是加密的
            else:
                encrypted_username = encrypt_data(username)  # 需要加密

            if is_encrypted(password):
                encrypted_password = password  # 已经是加密的
            else:
                encrypted_password = encrypt_data(password)  # 需要加密

            parser.set('DATABASE', 'username', encrypted_username)
            parser.set('DATABASE', 'password', encrypted_password)

            # 保持其他配置不变
            if not parser.has_option('DATABASE', 'driver'):
                parser.set('DATABASE', 'driver', 'ODBC Driver 17 for SQL Server')
            # 设置key_hash标识，让lis_system.py知道需要解密
            parser.set('DATABASE', 'key_hash', 'simple_encrypted')
            
            # 写入文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                parser.write(f)
            
            return True
        except Exception as e:
            messagebox.showerror("错误", f"更新config.ini失败: {str(e)}")
            return False
    
    def get_resource_path(self, relative_path):
        """获取资源文件的绝对路径，兼容PyInstaller打包"""
        try:
            # PyInstaller创建临时文件夹，并将路径存储在_MEIPASS中
            base_path = sys._MEIPASS
        except AttributeError:
            # 如果不是打包环境，使用当前目录
            base_path = os.path.abspath(".")

        return os.path.join(base_path, relative_path)

    def log_message(self, message, is_error=False):
        """记录日志消息到文件"""
        try:
            log_file = "reg_execution.log"
            timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(log_entry)
        except:
            pass  # 忽略日志写入错误

    def run_reg_file(self, config_name):
        """运行对应的注册表文件"""
        if config_name not in self.configs:
            self.log_message(f"配置 '{config_name}' 不存在", True)
            return False

        reg_file = self.configs[config_name].get('reg_file', '')
        if not reg_file:
            self.log_message(f"配置 '{config_name}' 没有设置注册表文件")
            return True  # 如果没有设置reg文件，认为成功

        # 获取正确的文件路径
        if not os.path.isabs(reg_file):
            # 首先尝试从资源路径获取
            reg_file_path = self.get_resource_path(reg_file)
            if not os.path.exists(reg_file_path):
                # 如果资源路径不存在，尝试当前工作目录
                reg_file_path = os.path.join(os.getcwd(), reg_file)
        else:
            reg_file_path = reg_file

        if not os.path.exists(reg_file_path):
            error_msg = f"注册表文件不存在: {reg_file_path}"
            self.log_message(error_msg, True)
            # 显示错误对话框
            try:
                messagebox.showwarning("警告", f"注册表文件不存在:\n{reg_file}\n\n请检查文件路径是否正确")
            except:
                pass
            return False

        try:
            self.log_message(f"正在导入注册表文件: {reg_file_path}")

            # 使用regedit /s来静默导入注册表文件
            if os.name != 'nt':
                error_msg = "非Windows系统，无法导入注册表文件"
                self.log_message(error_msg, True)
                messagebox.showerror("错误", error_msg)
                return False

            # 使用更简单可靠的方法执行注册表导入
            try:
                # 方法1: 使用subprocess.run (推荐)
                result = subprocess.run(['regedit', '/s', reg_file_path],
                                      capture_output=False,
                                      timeout=30,
                                      check=False)

                if result.returncode == 0:
                    success_msg = f"注册表文件导入成功: {os.path.basename(reg_file)}"
                    self.log_message(success_msg)
                    return True
                else:
                    error_msg = f"注册表文件导入失败，返回码: {result.returncode}"
                    self.log_message(error_msg, True)
                    messagebox.showerror("错误", f"注册表导入失败\n文件: {reg_file}\n返回码: {result.returncode}")
                    return False

            except subprocess.TimeoutExpired:
                error_msg = "注册表文件导入超时"
                self.log_message(error_msg, True)
                messagebox.showerror("错误", f"{error_msg}\n文件: {reg_file}")
                return False

            except FileNotFoundError:
                error_msg = "找不到regedit程序"
                self.log_message(error_msg, True)
                messagebox.showerror("错误", f"{error_msg}\n请确认系统环境正常")
                return False

            except Exception as e:
                # 如果subprocess.run失败，尝试备用方法
                self.log_message(f"subprocess.run失败，尝试备用方法: {str(e)}")

                try:
                    # 方法2: 使用os.system作为备用
                    import shlex
                    cmd_str = f'regedit /s "{reg_file_path}"'
                    result_code = os.system(cmd_str)

                    if result_code == 0:
                        success_msg = f"注册表文件导入成功(备用方法): {os.path.basename(reg_file)}"
                        self.log_message(success_msg)
                        return True
                    else:
                        error_msg = f"注册表文件导入失败(备用方法)，返回码: {result_code}"
                        self.log_message(error_msg, True)
                        messagebox.showerror("错误", f"注册表导入失败\n文件: {reg_file}\n返回码: {result_code}")
                        return False

                except Exception as e2:
                    error_msg = f"所有执行方法都失败: {str(e2)}"
                    self.log_message(error_msg, True)
                    messagebox.showerror("错误", f"注册表导入失败\n文件: {reg_file}\n错误: {str(e2)}")
                    return False

        except Exception as e:
            error_msg = f"运行注册表文件失败: {str(e)}"
            self.log_message(error_msg, True)
            messagebox.showerror("错误", f"注册表导入异常\n文件: {reg_file}\n错误: {str(e)}")
            return False
    
    def switch_config(self, config_name):
        """切换配置"""
        if config_name not in self.configs:
            self.log_message(f"尝试切换到不存在的配置: {config_name}", True)
            return False

        self.log_message(f"开始切换配置到: {config_name}")

        # 更新config.ini
        if self.update_config_ini(config_name):
            self.current_config = config_name
            self.save_configs()
            self.log_message(f"config.ini已更新为配置: {config_name}")

            # 运行对应的注册表文件
            reg_success = self.run_reg_file(config_name)

            if reg_success:
                self.log_message(f"配置切换完成: {config_name}")
            else:
                self.log_message(f"配置切换部分完成，注册表导入失败: {config_name}", True)

            return True
        else:
            self.log_message(f"config.ini更新失败: {config_name}", True)
            return False

class ConfigSwitcher:
    def __init__(self):
        self.config_manager = ConfigManager()
        self.root = tk.Tk()
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        self.root.title("瑞美LIS解码系统 - 配置切换工具")

        # 先隐藏窗口，避免闪烁
        self.root.withdraw()

        # 设置窗口大小和属性
        self.root.geometry("500x330")
        self.root.resizable(True, True)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置选择框架
        select_frame = ttk.LabelFrame(main_frame, text="选择配置", padding="10")
        select_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 配置列表
        self.config_var = tk.StringVar()
        self.config_combo = ttk.Combobox(select_frame, textvariable=self.config_var, 
                                        state="readonly", width=30)
        self.config_combo.grid(row=0, column=0, padx=(0, 10))
        
        # 切换按钮
        switch_btn = ttk.Button(select_frame, text="切换配置", command=self.switch_config)
        switch_btn.grid(row=0, column=1)
        
        # 当前配置显示
        current_frame = ttk.LabelFrame(main_frame, text="当前配置信息", padding="10")
        current_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.current_info = tk.Text(current_frame, height=8, width=60)
        self.current_info.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 配置管理按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, columnspan=2, pady=(0, 10))
        
        ttk.Button(btn_frame, text="添加配置", command=self.add_config).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(btn_frame, text="编辑配置", command=self.edit_config).grid(row=0, column=1, padx=5)
        ttk.Button(btn_frame, text="删除配置", command=self.delete_config).grid(row=0, column=2, padx=5)
        ttk.Button(btn_frame, text="刷新", command=self.refresh_configs).grid(row=0, column=3, padx=(5, 0))
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        current_frame.columnconfigure(0, weight=1)
        
        # 初始化界面
        self.refresh_configs()

        # 将窗口居中显示（在所有UI元素创建完成后）
        self.center_window()

    def center_window(self):
        """将窗口居中显示"""
        # 更新窗口以获取实际尺寸
        self.root.update_idletasks()

        # 获取窗口尺寸
        window_width = self.root.winfo_reqwidth()
        window_height = self.root.winfo_reqheight()

        # 如果窗口尺寸太小，使用默认尺寸
        if window_width < 600:
            window_width = 500
        if window_height < 500:
            window_height = 330

        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口位置和大小
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 显示窗口
        self.root.deiconify()
    
    def refresh_configs(self):
        """刷新配置列表"""
        self.config_manager.load_configs()
        config_names = list(self.config_manager.configs.keys())
        self.config_combo['values'] = config_names
        
        if self.config_manager.current_config in config_names:
            self.config_var.set(self.config_manager.current_config)
        elif config_names:
            self.config_var.set(config_names[0])
        
        self.update_current_info()
    
    def update_current_info(self):
        """更新当前配置信息显示"""
        current = self.config_manager.current_config
        if current and current in self.config_manager.configs:
            config = self.config_manager.configs[current]
            info = f"配置名称: {config['name']}\n"
            info += f"服务器: {config['server']}\n"
            info += f"数据库: {config['database']}\n"
            info += f"用户名: {config['username']}\n"
            info += f"密码: {'*' * len(config['password'])}\n"
            info += f"注册表文件: {config.get('reg_file', '未设置')}\n"
        else:
            info = "未选择配置"
        
        self.current_info.delete(1.0, tk.END)
        self.current_info.insert(1.0, info)
    
    def switch_config(self):
        """切换配置"""
        selected = self.config_var.get()
        if not selected:
            messagebox.showwarning("警告", "请选择要切换的配置")
            return

        # 获取配置信息
        config = self.config_manager.configs.get(selected, {})
        reg_file = config.get('reg_file', '')

        self.status_var.set("正在切换配置...")
        self.root.update()

        # 显示详细的切换信息
        if reg_file:
            self.status_var.set(f"正在切换配置并导入注册表文件...")
        else:
            self.status_var.set(f"正在切换配置（无注册表文件）...")
        self.root.update()

        if self.config_manager.switch_config(selected):
            self.status_var.set(f"已切换到配置: {selected}")
            self.update_current_info()

            # 显示成功消息
            if reg_file:
                success_msg = f"配置已切换到: {selected}\n\n"
                success_msg += f"数据库服务器: {config.get('server', 'N/A')}\n"
                success_msg += f"数据库名称: {config.get('database', 'N/A')}\n"
                if os.path.exists("reg_execution.log"):
                    success_msg += f"\n注册表文件: {reg_file}\n"
                    success_msg += "详细日志请查看 reg_execution.log 文件"
                messagebox.showinfo("切换成功", success_msg)
            else:
                messagebox.showinfo("切换成功", f"配置已切换到: {selected}\n（该配置无注册表文件）")
        else:
            self.status_var.set("切换失败")
            error_msg = "配置切换失败"
            if os.path.exists("reg_execution.log"):
                error_msg += "\n\n详细错误信息请查看 reg_execution.log 文件"
            messagebox.showerror("错误", error_msg)

    def add_config(self):
        """添加新配置"""
        dialog = ConfigDialog(self.root, "添加配置")
        if dialog.result:
            name = dialog.result['name']
            if name in self.config_manager.configs:
                messagebox.showerror("错误", "配置名称已存在")
                return

            self.config_manager.configs[name] = dialog.result
            self.config_manager.save_configs()
            self.refresh_configs()
            self.status_var.set(f"已添加配置: {name}")

    def edit_config(self):
        """编辑配置"""
        selected = self.config_var.get()
        if not selected:
            messagebox.showwarning("警告", "请选择要编辑的配置")
            return

        config = self.config_manager.configs[selected].copy()
        dialog = ConfigDialog(self.root, "编辑配置", config)
        if dialog.result:
            # 如果名称改变了，需要删除旧的配置
            if dialog.result['name'] != selected:
                if dialog.result['name'] in self.config_manager.configs:
                    messagebox.showerror("错误", "配置名称已存在")
                    return
                del self.config_manager.configs[selected]
                if self.config_manager.current_config == selected:
                    self.config_manager.current_config = dialog.result['name']

            self.config_manager.configs[dialog.result['name']] = dialog.result
            self.config_manager.save_configs()
            self.refresh_configs()
            self.status_var.set(f"已更新配置: {dialog.result['name']}")

    def delete_config(self):
        """删除配置"""
        selected = self.config_var.get()
        if not selected:
            messagebox.showwarning("警告", "请选择要删除的配置")
            return

        if len(self.config_manager.configs) <= 1:
            messagebox.showwarning("警告", "至少需要保留一个配置")
            return

        if messagebox.askyesno("确认", f"确定要删除配置 '{selected}' 吗？"):
            del self.config_manager.configs[selected]
            if self.config_manager.current_config == selected:
                self.config_manager.current_config = list(self.config_manager.configs.keys())[0]
            self.config_manager.save_configs()
            self.refresh_configs()
            self.status_var.set(f"已删除配置: {selected}")

    def run(self):
        """运行主程序"""
        self.root.mainloop()

class ConfigDialog:
    def __init__(self, parent, title, config=None):
        self.result = None
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)

        # 先隐藏对话框，避免闪烁
        self.dialog.withdraw()

        self.dialog.geometry("400x350")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.setup_dialog(config)

        # 居中显示对话框
        self.center_dialog(parent)

        # 显示对话框
        self.dialog.deiconify()

        self.dialog.wait_window()

    def center_dialog(self, parent):
        """将对话框相对于父窗口居中显示"""
        # 更新对话框以获取实际尺寸
        self.dialog.update_idletasks()

        # 获取对话框尺寸
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()

        # 获取父窗口位置和尺寸
        parent_x = parent.winfo_rootx()
        parent_y = parent.winfo_rooty()
        parent_width = parent.winfo_width()
        parent_height = parent.winfo_height()

        # 计算居中位置（相对于父窗口）
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2

        # 确保对话框不会超出屏幕边界
        screen_width = self.dialog.winfo_screenwidth()
        screen_height = self.dialog.winfo_screenheight()

        x = max(0, min(x, screen_width - dialog_width))
        y = max(0, min(y, screen_height - dialog_height))

        # 设置对话框位置
        self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")

    def setup_dialog(self, config):
        """设置对话框界面"""
        main_frame = ttk.Frame(self.dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 配置名称
        ttk.Label(main_frame, text="配置名称:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        self.name_var = tk.StringVar(value=config['name'] if config else "")
        ttk.Entry(main_frame, textvariable=self.name_var, width=30).grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        # 服务器地址
        ttk.Label(main_frame, text="服务器地址:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        self.server_var = tk.StringVar(value=config['server'] if config else "")
        ttk.Entry(main_frame, textvariable=self.server_var, width=30).grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        # 数据库名
        ttk.Label(main_frame, text="数据库名:").grid(row=2, column=0, sticky=tk.W, pady=(0, 5))
        self.database_var = tk.StringVar(value=config['database'] if config else "")
        ttk.Entry(main_frame, textvariable=self.database_var, width=30).grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        # 用户名
        ttk.Label(main_frame, text="用户名:").grid(row=3, column=0, sticky=tk.W, pady=(0, 5))
        # 如果是编辑模式，显示解密后的用户名
        username_value = ""
        if config and config.get('username'):
            if is_encrypted(config['username']):
                username_value = decrypt_data(config['username'])  # 解密
            else:
                username_value = config['username']  # 已经是明文
        self.username_var = tk.StringVar(value=username_value)
        ttk.Entry(main_frame, textvariable=self.username_var, width=30).grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        # 密码
        ttk.Label(main_frame, text="密码:").grid(row=4, column=0, sticky=tk.W, pady=(0, 5))
        # 如果是编辑模式，显示解密后的密码
        password_value = ""
        if config and config.get('password'):
            if is_encrypted(config['password']):
                password_value = decrypt_data(config['password'])  # 解密
            else:
                password_value = config['password']  # 已经是明文
        self.password_var = tk.StringVar(value=password_value)
        ttk.Entry(main_frame, textvariable=self.password_var, width=30, show="*").grid(row=4, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        # 注册表文件
        ttk.Label(main_frame, text="注册表文件:").grid(row=5, column=0, sticky=tk.W, pady=(0, 5))
        reg_frame = ttk.Frame(main_frame)
        reg_frame.grid(row=5, column=1, sticky=(tk.W, tk.E), pady=(0, 5))

        self.reg_file_var = tk.StringVar(value=config.get('reg_file', '') if config else "")
        ttk.Entry(reg_frame, textvariable=self.reg_file_var, width=25).pack(side=tk.LEFT, fill=tk.X, expand=True)
        ttk.Button(reg_frame, text="浏览", command=self.browse_reg_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=6, column=0, columnspan=2, pady=(20, 0))

        ttk.Button(btn_frame, text="确定", command=self.ok_clicked).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="取消", command=self.cancel_clicked).pack(side=tk.LEFT)

        # 配置网格权重
        main_frame.columnconfigure(1, weight=1)
        reg_frame.columnconfigure(0, weight=1)

    def browse_reg_file(self):
        """浏览选择注册表文件"""
        filename = filedialog.askopenfilename(
            title="选择注册表文件",
            filetypes=[("注册表文件", "*.reg"), ("所有文件", "*.*")]
        )
        if filename:
            self.reg_file_var.set(filename)

    def ok_clicked(self):
        """确定按钮点击"""
        name = self.name_var.get().strip()
        server = self.server_var.get().strip()
        database = self.database_var.get().strip()
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if not all([name, server, database, username]):
            messagebox.showerror("错误", "请填写所有必填字段")
            return

        # 确保用户名和密码都是加密存储的
        # 检查用户名是否需要加密
        if is_encrypted(username):
            encrypted_username = username  # 已经是加密的
        else:
            encrypted_username = encrypt_data(username)  # 需要加密

        # 检查密码是否需要加密
        if is_encrypted(password):
            encrypted_password = password  # 已经是加密的
        else:
            encrypted_password = encrypt_data(password)  # 需要加密

        self.result = {
            'name': name,
            'server': server,
            'database': database,
            'username': encrypted_username,  # 保存加密的用户名
            'password': encrypted_password,  # 保存加密的密码
            'reg_file': self.reg_file_var.get().strip()
        }
        self.dialog.destroy()

    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()

def main():
    """主函数"""
    try:
        app = ConfigSwitcher()
        app.run()
    except Exception as e:
        messagebox.showerror("错误", f"程序启动失败: {str(e)}")

if __name__ == "__main__":
    main()
