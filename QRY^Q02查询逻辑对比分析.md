# QRY^Q02查询逻辑对比分析

## 🚨 **发现的问题**

您指出的问题非常关键！现在的查询逻辑确实有问题。

## 📋 **对比分析**

### 原始逻辑（正确的）
```python
# 备份/lis_system2.py 第642-648行
if query_mode == 'BC':
    sql = "SELECT * FROM lis_pat WHERE brdh=? AND jyrq=?"
else:
    sql = "SELECT * FROM lis_pat WHERE ybh=? AND jyrq=?"
    
self.logger.info(f"主表查询参数: ({sample_no}, {today})")
cursor.execute(sql, (sample_no, today))
```

### 现在的逻辑（有问题的）
```python
# lis_system.py 第1299-1305行
if query_mode == 'BC':
    sql = "SELECT * FROM lis_pat WHERE brdh=? AND jyrq=? and yq=?"
else:
    sql = "SELECT * FROM lis_pat WHERE ybh=? AND jyrq=? and yq=?"
    
self.logger.info(f"主表查询参数: ({sample_no}, {today}, {yq})")
cursor.execute(sql, (sample_no, today, yq))
```

## ⚠️ **问题分析**

### 1. **多了yq条件**
原来查询只用：
- **BC模式**: `brdh + jyrq` （条码号 + 日期）
- **SN模式**: `ybh + jyrq` （样本编号 + 日期）

现在查询变成：
- **BC模式**: `brdh + jyrq + yq` （条码号 + 日期 + 仪器）
- **SN模式**: `ybh + jyrq + yq` （样本编号 + 日期 + 仪器）

### 2. **为什么这样不对？**

#### QRY^Q02的本质是：
> **设备向LIS查询"某个条码号/样本编号今天有什么项目要做"**

#### 标准逻辑应该是：
1. **BC模式**: 用条码号查找今天的样本
2. **SN模式**: 用样本编号查找今天的样本

#### 加上yq的问题：
1. **条码号是唯一的** - 一个条码号在同一天只能对应一个样本
2. **不需要限制仪器** - 条码号本身就足够唯一
3. **可能查不到数据** - 如果yq不匹配，明明有样本却查不到

### 3. **实际场景**
```
设备: "这个条码号12345今天有什么项目要做？"
系统应该: "查找条码号12345在今天的所有项目"
而不是: "查找条码号12345在今天且来自这个设备的项目"
```

## ✅ **修复方案**

### 方案1: 回到原始逻辑（推荐）
```python
if query_mode == 'BC':
    sql = "SELECT * FROM lis_pat WHERE brdh=? AND jyrq=?"
else:
    sql = "SELECT * FROM lis_pat WHERE ybh=? AND jyrq=?"

cursor.execute(sql, (sample_no, today))
```

### 方案2: 保留yq但改为可选
```python
if query_mode == 'BC':
    sql = "SELECT * FROM lis_pat WHERE brdh=? AND jyrq=? AND (yq=? OR yq IS NULL)"
else:
    sql = "SELECT * FROM lis_pat WHERE ybh=? AND jyrq=? AND (yq=? OR yq IS NULL)"
```

## 🔍 **从日志验证问题**

```log
主表查询参数: (, 2025-06-11 00:00:00.000, I2901)
主表查询结果: None
```

可以看到：
1. `sample_no` 是空的 `(,` 开头
2. 查询结果是 `None`
3. 系统返回 `NF应答`（Not Found）

## ⚡ **立即修复建议**

推荐立即回到原始的两参数查询逻辑：
1. **BC模式**: 只用条码号和日期查询
2. **SN模式**: 只用样本编号和日期查询
3. **去掉yq限制条件**

这样更符合医疗设备查询的标准逻辑，也避免了因为仪器编码不匹配导致的查询失败。

您觉得这个分析对吗？需要我立即修复这个查询逻辑吗？ 