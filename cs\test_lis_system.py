#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LIS系统测试脚本
包含ORU^R01结果上传和QRY^Q02查询的完整测试
"""

import socket
import time
import threading
from datetime import datetime

class LISTestClient:
    def __init__(self, host='127.0.0.1', port=22010):
        self.host = host
        self.port = port
        
    def send_message(self, message, description=""):
        """发送消息到LIS服务器并接收应答"""
        try:
            # 创建socket连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)  # 10秒超时
            sock.connect((self.host, self.port))
            
            print(f"\n{'='*60}")
            print(f"测试: {description}")
            print(f"{'='*60}")
            
            # 发送消息
            message_bytes = message.encode('utf-8')
            sock.sendall(message_bytes)
            print(f"发送消息 ({len(message_bytes)} 字节):")
            print(f"原始内容: {repr(message)}")
            print(f"格式化内容:\n{message.replace(chr(11), '<SB>').replace(chr(28), '<EB>').replace(chr(13), '<CR>')}")
            
            # 接收应答
            response = b''
            start_time = time.time()
            while time.time() - start_time < 5:  # 5秒内接收应答
                try:
                    data = sock.recv(4096)
                    if data:
                        response += data
                        # 检查是否收到完整应答（包含结束标记）
                        if b'\x1c\r' in response:
                            break
                except socket.timeout:
                    break
            
            if response:
                try:
                    decoded_response = response.decode('utf-8', errors='replace')
                    print(f"\n收到应答 ({len(response)} 字节):")
                    print(f"原始内容: {repr(decoded_response)}")
                    print(f"格式化内容:\n{decoded_response.replace(chr(11), '<SB>').replace(chr(28), '<EB>').replace(chr(13), '<CR>')}")
                except Exception as e:
                    print(f"应答解码错误: {e}")
                    print(f"原始应答: {response}")
            else:
                print("未收到应答")
                
            sock.close()
            return response
            
        except Exception as e:
            print(f"发送消息时出错: {e}")
            return None

def test_oru_r01_messages():
    """测试ORU^R01结果上传消息"""
    client = LISTestClient()
    
    # ORU^R01消息列表 - 基于您提供的实际消息
    oru_messages = [
        # 消息1: CEA结果
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164424||ORU^R01|3|P|2.3.1|Z80005000310|||0||UNICODE|||\r"
        f"PID|357|||||||O|||||||||||||||||||||||0^Y|\r"
        f"OBR|1319|285025671|13|I2900|N||20250610164424||1|029^1||N||20250610162315|0|||||||20250610164424||||||||||||||||||||||||||\r"
        f"OBX|NM|1|CEA^1|7.83|ng/mL|0.00-5.00|H||F||||20250610164420|||33645|20250502|3087|20250402|2799|20250402|2084|\r\x1c\r",
        
        # 消息2: AFP结果
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164726||ORU^R01|4|P|2.3.1|Z80005000310|||0||UNICODE|||\r"
        f"PID|357|||||||O|||||||||||||||||||||||0^Y|\r"
        f"OBR|1319|285025671|13|I2900|N||20250610164720||1|029^1||N||20250610162315|0|||||||20250610164726||||||||||||||||||||||||||\r"
        f"OBX|NM|1|AFP^1|2.91|ng/mL|0.00-10.00|N||F||||20250610164719|||13924|20250301|5378|20250402|2799|20250402|2084|\r\x1c\r",
        
        # 消息3: AFP和CEA结果
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164742||ORU^R01|5|P|2.3.1|Z80005000310|||0||UNICODE|||\r"
        f"PID|357|||||||O|||||||||||||||||||||||0^Y|\r"
        f"OBR|1319|285025671|13|I2900|N||20250610164720||1|029^1||N||20250610162315|0|||||||20250610164742||||||||||||||||||||||||||\r"
        f"OBX|NM|1|AFP^1|2.91|ng/mL|0.00-10.00|N||F||||20250610164719|||13924|20250301|5378|20250402|2799|20250402|2084|\r"
        f"OBX|NM|2|CEA^1|7.83|ng/mL|0.00-5.00|H||F||||20250610164420|||33645|20250502|3087|20250402|2799|20250402|2084|\r\x1c\r",
        
        # 消息4: 完整结果报告
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||20250610165058||ORU^R01|6|P|2.3.1|Z80005000310|||0||UNICODE|||\r"
        f"PID|357|||||||O|||||||||||||||||||||||0^Y|\r"
        f"OBR|1319|285025671|13|I2900|N||20250610164720||1|029^1||N||20250610162315|0|||||||20250610165058||||||||||||||||||||||||||\r"
        f"OBX|NM|1|AFP^1|2.91|ng/mL|0.00-10.00|N||F||||20250610164719|||13924|20250301|5378|20250402|2799|20250402|2084|\r"
        f"OBX|NM|2|CEA^1|7.83|ng/mL|0.00-5.00|H||F||||20250610164420|||33645|20250502|3087|20250402|2799|20250402|2084|\r\x1c\r"
    ]
    
    print("开始测试ORU^R01结果上传...")
    
    for i, message in enumerate(oru_messages, 1):
        client.send_message(message, f"ORU^R01消息{i} - 样本285025671结果上传")
        time.sleep(2)  # 等待2秒再发送下一条
    
    print("\nORU^R01测试完成，等待5秒让数据处理完毕...")
    time.sleep(5)

def test_qry_q02_messages():
    """测试QRY^Q02查询消息"""
    client = LISTestClient()
    
    # QRY^Q02查询消息列表 - 基于您提供的实际消息
    qry_messages = [
        # 查询样本285025671 (应该能查到，因为刚才上传了结果)
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164726||QRY^Q02|1|P|2.3.1|||0||UNICODE|||\r"
        f"QRD|20250610164726|R|I|||10^RD|285025671|||BC|4^1|N|||||||\r"
        f"QRF|I2900|||||RCT|COR|ALL||\r\x1c\r",
        
        # 查询样本0860194241 (可能查不到)
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||20250610160956||QRY^Q02|2|P|2.3.1|||0||UNICODE|||\r"
        f"QRD|20250610160956|R|I|||10^RD|0860194241|||BC|N003^4|N|||||||\r"
        f"QRF|I2900|||||RCT|COR|ALL||\r\x1c\r",
        
        # 查询不存在的样本号
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||20250610170000||QRY^Q02|3|P|2.3.1|||0||UNICODE|||\r"
        f"QRD|20250610170000|R|I|||10^RD|999999999|||BC|1^1|N|||||||\r"
        f"QRF|I2900|||||RCT|COR|ALL||\r\x1c\r"
    ]
    
    print("\n开始测试QRY^Q02样本查询...")
    
    for i, message in enumerate(qry_messages, 1):
        sample_no = message.split('|')[19] if '|' in message else 'Unknown'  # 提取样本号
        client.send_message(message, f"QRY^Q02查询{i} - 查询样本{sample_no}")
        time.sleep(3)  # 等待3秒再发送下一条

def test_mixed_scenario():
    """测试混合场景：结果上传和查询交替进行"""
    client = LISTestClient()
    
    print("\n开始混合场景测试...")
    
    # 场景1: 上传新样本结果
    new_sample_message = (
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||{datetime.now().strftime('%Y%m%d%H%M%S')}||ORU^R01|999|P|2.3.1|Z80005000310|||0||UNICODE|||\r"
        f"PID|999|||||||M|||||||||||||||||||||||25^Y|\r"
        f"OBR|1|TESTSAMPLE123|999|I2901|N||{datetime.now().strftime('%Y%m%d%H%M%S')}||1|1^1||N||{datetime.now().strftime('%Y%m%d%H%M%S')}|0|||||||{datetime.now().strftime('%Y%m%d%H%M%S')}||||||||||||||||||||||||||\r"
        f"OBX|NM|1|TEST^1|100.5|mg/dL|80.0-120.0|N||F||||{datetime.now().strftime('%Y%m%d%H%M%S')}|||12345|20250101|6789|20250101|9999|20250101|8888|\r\x1c\r"
    )
    
    client.send_message(new_sample_message, "混合测试 - 上传新样本TESTSAMPLE123")
    time.sleep(3)
    
    # 场景2: 立即查询刚上传的样本
    query_new_sample = (
        f"\x0bMSH|^~\\&|Analyzer id|I2901|LIS ID||{datetime.now().strftime('%Y%m%d%H%M%S')}||QRY^Q02|888|P|2.3.1|||0||UNICODE|||\r"
        f"QRD|{datetime.now().strftime('%Y%m%d%H%M%S')}|R|I|||10^RD|TESTSAMPLE123|||BC|1^1|N|||||||\r"
        f"QRF|I2901|||||RCT|COR|ALL||\r\x1c\r"
    )
    
    client.send_message(query_new_sample, "混合测试 - 查询刚上传的样本TESTSAMPLE123")

def main():
    """主测试函数"""
    print("LIS系统完整功能测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"目标服务器: 127.0.0.1:22010")
    print("=" * 60)
    
    try:
        # 测试连接
        test_client = LISTestClient()
        print("正在测试服务器连接...")
        test_message = f"\x0bMSH|^~\\&|TEST|TEST|TEST||{datetime.now().strftime('%Y%m%d%H%M%S')}||QRY^Q02|0|P|2.3.1|||0||UNICODE|||\r"
        test_message += f"QRD|{datetime.now().strftime('%Y%m%d%H%M%S')}|R|I|||10^RD|CONNECTIONTEST|||BC|1^1|N|||||||\r\x1c\r"
        
        response = test_client.send_message(test_message, "连接测试")
        if response is None:
            print("❌ 无法连接到LIS服务器，请确保服务器正在运行")
            return
        else:
            print("✅ 服务器连接正常")
        
        time.sleep(2)
        
        # 1. 先测试ORU^R01结果上传
        test_oru_r01_messages()
        
        # 2. 再测试QRY^Q02查询
        test_qry_q02_messages()
        
        # 3. 最后测试混合场景
        test_mixed_scenario()
        
        print("\n" + "=" * 60)
        print("所有测试已完成！")
        print("请检查LIS服务器日志以查看详细处理情况。")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出错: {e}")

if __name__ == "__main__":
    main() 