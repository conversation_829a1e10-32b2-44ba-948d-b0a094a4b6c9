<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LIS系统测试客户端</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 200px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .two-column {
                grid-template-columns: 1fr;
            }
        }
        .message-template {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .template-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LIS系统测试客户端</h1>
        
        <div class="two-column">
            <div>
                <div class="form-group">
                    <label for="server">服务器地址:</label>
                    <input type="text" id="server" value="127.0.0.1" placeholder="服务器IP地址">
                </div>
                
                <div class="form-group">
                    <label for="port">端口:</label>
                    <input type="number" id="port" value="22010" placeholder="端口号">
                </div>
                
                <div class="form-group">
                    <label for="messageType">消息类型:</label>
                    <select id="messageType" onchange="loadTemplate()">
                        <option value="ORU_R01">ORU^R01 (检验结果上传)</option>
                        <option value="QRY_Q02">QRY^Q02 (样本项目查询)</option>
                    </select>
                </div>
            </div>
            
            <div>
                <div class="message-template">
                    <div class="template-title">快速填充测试数据:</div>
                    <button onclick="loadTestData()">加载您提供的测试数据</button>
                    <button onclick="loadSimpleTest()">加载简单测试数据</button>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <label for="message">HL7消息内容:</label>
            <textarea id="message" placeholder="在此输入HL7消息内容..."></textarea>
        </div>
        
        <div class="button-group">
            <button onclick="sendMessage()" id="sendBtn">发送消息</button>
            <button onclick="clearMessage()">清空消息</button>
            <button onclick="validateMessage()">验证消息格式</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="form-group">
            <label for="response">服务器响应:</label>
            <textarea id="response" readonly placeholder="服务器响应将显示在这里..."></textarea>
        </div>
    </div>

    <script>
        // 预定义的消息模板
        const templates = {
            ORU_R01: `MSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164726||ORU^R01|4|P|2.3.1|Z80005000310|||0||UNICODE|||
PID|357|||||||O|||||||||||||||||||||||0^Y|
OBR|1319|285025671|13|I2900|N||20250610164720||1|029^1||N||20250610162315|0|||||||20250610164726||||||||||||||||||||||||||
OBX|NM|1|AFP^1|2.91|ng/mL|0.00-10.00|N||F||||20250610164719|||13924|20250301|5378|20250402|2799|20250402|2084|`,
            
            QRY_Q02: `MSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164726||QRY^Q02|1|P|2.3.1|||0||UNICODE|||
QRD|20250610164726|R|I|||10^RD|285025671|||BC|4^1|N||||||`
        };

        function loadTemplate() {
            const messageType = document.getElementById('messageType').value;
            const messageTextarea = document.getElementById('message');
            messageTextarea.value = templates[messageType] || '';
        }

        function loadTestData() {
            document.getElementById('message').value = templates.ORU_R01;
            showStatus('已加载您提供的测试数据', 'info');
        }

        function loadSimpleTest() {
            const simpleTest = `MSH|^~\\&|TestAnalyzer|TestLab|LIS||20250610120000||ORU^R01|123|P|2.3.1|||0||UNICODE|||
PID|1||12345||张^三||19900101|M|||||||||||||||||||||||30^Y|
OBR|1|TEST001|TEST001|CBC|N||20250610120000||1|||N||20250610120000|||||||||||||||||||||||||||||
OBX|NM|1|WBC^白细胞计数|7.5|10^9/L|4.0-10.0|N||F||||20250610120000|||||||
OBX|NM|2|RBC^红细胞计数|4.5|10^12/L|4.0-5.5|N||F||||20250610120000|||||||`;
            document.getElementById('message').value = simpleTest;
            showStatus('已加载简单测试数据', 'info');
        }

        function clearMessage() {
            document.getElementById('message').value = '';
            document.getElementById('response').value = '';
            hideStatus();
        }

        function validateMessage() {
            const message = document.getElementById('message').value.trim();
            if (!message) {
                showStatus('消息内容不能为空', 'error');
                return false;
            }
            
            const lines = message.split('\n').filter(line => line.trim());
            if (lines.length === 0) {
                showStatus('消息格式错误：没有有效的段', 'error');
                return false;
            }
            
            if (!lines[0].startsWith('MSH|')) {
                showStatus('消息格式错误：第一行必须是MSH段', 'error');
                return false;
            }
            
            showStatus(`消息验证通过，包含 ${lines.length} 个段`, 'success');
            return true;
        }

        async function sendMessage() {
            if (!validateMessage()) {
                return;
            }

            const server = document.getElementById('server').value;
            const port = document.getElementById('port').value;
            const message = document.getElementById('message').value;
            const sendBtn = document.getElementById('sendBtn');
            
            sendBtn.disabled = true;
            sendBtn.textContent = '发送中...';
            showStatus('正在连接服务器...', 'info');
            
            try {
                // 由于浏览器安全限制，我们无法直接建立TCP连接
                // 这里提供一个Python脚本的代码，用户可以复制运行
                const pythonScript = generatePythonScript(server, port, message);
                
                // 显示Python脚本
                document.getElementById('response').value = `由于浏览器安全限制，无法直接建立TCP连接。
请复制以下Python脚本并在命令行运行：

=== Python测试脚本 ===
${pythonScript}

=== 运行方法 ===
1. 将上述代码保存为 test_lis.py
2. 在命令行运行: python test_lis.py
3. 查看输出结果`;
                
                showStatus('已生成Python测试脚本，请按照说明运行', 'info');
                
            } catch (error) {
                showStatus('发送失败: ' + error.message, 'error');
            } finally {
                sendBtn.disabled = false;
                sendBtn.textContent = '发送消息';
            }
        }

        function generatePythonScript(server, port, message) {
            // 转换消息格式
            const lines = message.split('\n').filter(line => line.trim());
            const hl7Message = lines.join('\r');
            
            return `#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import socket
import time

def send_hl7_message():
    server = '${server}'
    port = ${port}
    
    # HL7消息内容
    message = '''${hl7Message}'''
    
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        print(f"正在连接到 {server}:{port}...")
        sock.connect((server, port))
        print("连接成功！")
        
        # 构造完整的HL7消息（添加开始和结束标记）
        full_message = b'\\x0b' + message.encode('utf-8') + b'\\x1c\\r'
        
        print("发送消息:")
        print(message)
        print("\\n" + "="*50)
        
        # 发送消息
        sock.sendall(full_message)
        print("消息已发送，等待响应...")
        
        # 接收响应
        response = sock.recv(4096)
        if response:
            # 移除控制字符
            clean_response = response.decode('utf-8', errors='ignore')
            clean_response = clean_response.replace('\\x0b', '').replace('\\x1c', '').replace('\\r', '\\n')
            print("\\n收到响应:")
            print(clean_response)
        else:
            print("未收到响应")
            
    except socket.timeout:
        print("连接超时")
    except ConnectionRefusedError:
        print("连接被拒绝，请检查服务器是否运行")
    except Exception as e:
        print(f"发生错误: {e}")
    finally:
        try:
            sock.close()
        except:
            pass

if __name__ == "__main__":
    send_hl7_message()`;
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + type;
            status.style.display = 'block';
        }

        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        // 页面加载时初始化
        window.onload = function() {
            loadTemplate();
        };
    </script>
</body>
</html> 