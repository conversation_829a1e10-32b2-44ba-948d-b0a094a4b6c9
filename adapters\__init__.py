#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
设备协议适配器包

该包包含了不同厂商设备的协议适配器实现：
- yingkai_adapter: YingKai Bio LIS Protocol V1.12 适配器
- abbott_adapter: Abbott ASTM 协议适配器  
- roche_adapter: Roche 自定义协议适配器
- 其他厂商适配器...
"""

__version__ = "1.0.0"
__author__ = "LIS System Team"

# 导出主要的适配器类
from .yingkai_adapter import YingKaiHL7Adapter
from .urit_adapter import URITAdapter

__all__ = [
    'YingKaiHL7Adapter',
    'URITAdapter',
]