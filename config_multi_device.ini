# 多设备支持的LIS系统配置文件
# 该配置文件扩展了原有的单设备配置，支持多厂商设备接入

[DATABASE]
server = localhost
database = lis2002
username = anQ=
password = bmFpaml0bmVyYWlq
driver = ODBC Driver 17 for SQL Server
key_hash = simple_encrypted

[SYSTEM]
host = 0.0.0.0
port = 22010
raw_dir = raw
processed_dir = result
log_file = lis_system.log
processed_files = processed_files.json
# 启用多设备支持
multi_device_enabled = true
# 消息路由器日志级别
router_log_level = INFO

# =============================================================================
# 协议适配器配置
# =============================================================================

[ADAPTERS]
# 可用的协议适配器列表
available = yingkai,abbott,roche,urit
# 默认适配器（用于无法识别的设备）
default = yingkai

[ADAPTER_yingkai]
module = adapters.yingkai_adapter
class = YingKaiHL7Adapter
protocol = HL7
version = 2.5
manufacturer = YingKai Bio
timeout = 30
# 字段长度限制
field_length_limits = {"patient_id": 50, "patient_name": 100, "sample_id": 50, "test_code": 20, "test_value": 200, "test_unit": 20}
# 启用消息验证
validation_enabled = true

[ADAPTER_abbott]
module = adapters.abbott_adapter
class = AbbottASTMAdapter
protocol = ASTM
version = E1394
manufacturer = Abbott
timeout = 60
# Abbott特定配置
astm_record_separator = \r
astm_field_separator = |
validation_enabled = true

[ADAPTER_roche]
module = adapters.roche_adapter  
class = RocheCustomAdapter
protocol = Custom
version = 1.0
manufacturer = Roche
timeout = 45
# Roche特定配置
custom_header = ROCHE
message_format = proprietary
validation_enabled = true

[ADAPTER_urit]
module = adapters.urit_adapter
class = URITAdapter
protocol = HL7
version = 2.3.1
manufacturer = URIT
timeout = 30
# URIT特定配置
test_mapping = {"ALT": "ALT", "AST": "AST", "TBIL": "TBIL", "DBIL": "DBIL", "TP": "TP", "ALB": "ALB", "GLU": "GLU", "BUN": "BUN", "CREA": "CREA", "UA": "UA"}
validation_enabled = true

# =============================================================================
# 设备配置
# =============================================================================

[DEVICES]
# 已配置的设备列表
configured = yingkai_lab1,yingkai_lab2,abbott_lab1,roche_lab1,urit_lab1

[DEVICE_yingkai_lab1]
device_type = YingKai_LIS
manufacturer = YingKai Bio
model = LIS_V112
protocol = HL7
adapter = yingkai
# IP地址范围（支持单个IP或范围）
ip_range = *************-*************
# 监听端口列表
ports = 22010,22011,22012
# 消息识别模式（支持多种模式）
message_patterns = MSH|,OBR|,OBX|,QRD|,ORU^R01,QRY^Q02
# 优先级（数字越小优先级越高）
priority = 10
# 数据库表映射
table_mapping = lis_pat,lis_result
# YQ配置
yq_source = custom              # YQ来源：message/custom/device_id/mapping
yq_value = I2900               # 自定义YQ值（当yq_source=custom时使用）
yq_prefix = YK_                # YQ前缀（当yq_source=device_id时使用）
yq_mapping = {"AUTO": "I2900", "MANUAL": "I2901"}  # YQ映射表（当yq_source=mapping时使用）
# 是否启用该设备
enabled = true

[DEVICE_yingkai_lab2]
device_type = YingKai_LIS
manufacturer = YingKai Bio
model = LIS_V111
protocol = HL7
adapter = yingkai
ip_range = *************-*************
ports = 22010
message_patterns = MSH|,OBR|,OBX|,QRD|
priority = 20
table_mapping = lis_pat,lis_result
# YQ配置
yq_source = device_id           # 使用设备ID作为YQ
yq_prefix = LAB2_              # YQ前缀
enabled = true

[DEVICE_abbott_lab1]
device_type = Abbott_iSTAT
manufacturer = Abbott
model = i-STAT_1
protocol = ASTM
adapter = abbott
ip_range = *************-*************
ports = 23000
message_patterns = H|.*ABBOTT,P|,O|,R|
priority = 30
table_mapping = abbott_pat,abbott_result
# YQ配置
yq_source = mapping            # 使用映射表
yq_mapping = {"ABBOTT": "ABT001", "iSTAT": "ABT002"}  # Abbott设备YQ映射
enabled = true

[DEVICE_roche_lab1]
device_type = Roche_cobas
manufacturer = Roche
model = cobas_6000
protocol = Custom
adapter = roche
ip_range = *************-*************
ports = 24000
message_patterns = ROCHE,^STX,^ETX
priority = 40
table_mapping = roche_pat,roche_result
enabled = false

[DEVICE_urit_lab1]
device_type = URIT_Analyzer
manufacturer = URIT
model = URIT-8030
protocol = HL7
adapter = urit
# IP地址范围（根据您的实际网络配置）
ip_range = *************-*************
# 监听端口列表
ports = 22010,22011
# 消息识别模式
message_patterns = MSH|^~\\&|urit|,|urit|,ORU^R01,|2.3.1|
# 优先级
priority = 15
# 数据库表映射
table_mapping = lis_pat,lis_result
# YQ配置 - 使用自定义YQ值
yq_source = custom
yq_value = URIT8030
# 是否启用该设备
enabled = true

# =============================================================================
# 表映射配置
# =============================================================================

[TABLE_MAPPING]
# 默认表映射（向后兼容）
pat_table = lis_pat
result_table = lis_result
mapping_table = xm_inter

# YingKai设备表映射
[TABLE_MAPPING_yingkai]
pat_table = lis_pat
result_table = lis_result
mapping_table = xm_inter
# 字段映射
patient_id_field = patient_id
patient_name_field = name
sample_id_field = sample_id
test_code_field = test_code
test_value_field = value
test_unit_field = unit

# Abbott设备表映射
[TABLE_MAPPING_abbott]
pat_table = abbott_pat
result_table = abbott_result
mapping_table = abbott_mapping
# 字段映射
patient_id_field = pat_id
patient_name_field = pat_name
sample_id_field = sample_no
test_code_field = item_code
test_value_field = result_value
test_unit_field = result_unit

# Roche设备表映射
[TABLE_MAPPING_roche]
pat_table = roche_pat
result_table = roche_result
mapping_table = roche_mapping
# 字段映射
patient_id_field = patient_no
patient_name_field = patient_name
sample_id_field = specimen_id
test_code_field = test_id
test_value_field = test_result
test_unit_field = test_unit

# =============================================================================
# 路由器配置
# =============================================================================

[ROUTER]
# 消息路由器配置
enabled = true
# 设备识别超时时间（秒）
identification_timeout = 5
# 消息处理超时时间（秒）
message_timeout = 30
# 最大消息缓存大小
max_message_cache = 1000
# 统计信息保存间隔（秒）
stats_save_interval = 300
# 失败重试次数
max_retry_count = 3
# 重试间隔（秒）
retry_interval = 5

# =============================================================================
# 日志配置
# =============================================================================

[LOGGING]
# 总体日志级别
level = INFO
# 适配器日志级别
adapter_level = INFO
# 路由器日志级别
router_level = INFO
# 设备识别器日志级别
identifier_level = WARNING
# 日志文件路径
log_file = logs/multi_device.log
# 日志文件大小限制（MB）
max_file_size = 50
# 日志文件备份数量
backup_count = 5
# 日志格式
format = %%(asctime)s - %%(name)s - %%(levelname)s - [%%(filename)s:%%(lineno)d] - %%(message)s

# =============================================================================
# 性能监控配置
# =============================================================================

[MONITORING]
# 启用性能监控
enabled = true
# 监控数据收集间隔（秒）
collection_interval = 60
# 性能数据保存文件
stats_file = stats/performance.json
# 最大保存的性能数据条数
max_stats_records = 1000
# 启用内存监控
memory_monitoring = true
# 启用线程监控
thread_monitoring = true
# 启用消息速率监控
message_rate_monitoring = true

# =============================================================================
# 安全配置
# =============================================================================

[SECURITY]
# 启用IP白名单
ip_whitelist_enabled = false
# 允许的IP地址列表
allowed_ips = ***********/24,***********/24,***********/24
# 启用消息大小限制
message_size_limit_enabled = true
# 最大消息大小（KB）
max_message_size = 1024
# 启用连接速率限制
connection_rate_limit_enabled = false
# 每分钟最大连接数
max_connections_per_minute = 100

# =============================================================================
# 调试配置
# =============================================================================

[DEBUG]
# 启用调试模式
enabled = false
# 保存原始消息
save_raw_messages = false
# 原始消息保存目录
raw_messages_dir = debug/raw_messages
# 保存解析后的消息
save_parsed_messages = false
# 解析后消息保存目录
parsed_messages_dir = debug/parsed_messages
# 最大保存的调试文件数
max_debug_files = 1000