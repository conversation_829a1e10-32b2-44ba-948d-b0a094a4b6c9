#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
URIT设备专用LIS服务器

基于现有的lis_system.py架构，专门处理URIT设备的HL7消息：
1. 接收URIT设备的HL7消息
2. 使用URIT适配器解析消息
3. 写入数据库（lis_pat, lis_result表）
4. 返回ACK响应
"""

import socket
import threading
import time
import json
import logging
import configparser
import pyodbc
from datetime import datetime
from pathlib import Path
import os
import base64
from concurrent.futures import ThreadPoolExecutor
from logging.handlers import RotatingFileHandler

# 导入URIT适配器
from adapters.urit_adapter import URITAdapter
from device_adapter import DeviceInfo

class URITLISServer:
    """URIT设备专用LIS服务器"""
    
    def __init__(self, config_file='config.ini'):
        self.config_file = config_file
        self.config = None
        self.db_config = None
        self.server_socket = None
        self.running = False
        self.clients = []
        self.executor = ThreadPoolExecutor(max_workers=20)
        
        # URIT适配器
        self.urit_adapter = None
        
        # 统计信息
        self.stats = {
            'messages_received': 0,
            'messages_processed': 0,
            'messages_failed': 0,
            'database_writes': 0,
            'database_errors': 0,
            'start_time': None
        }
        
        # 设置日志
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.load_config()
        
        # 初始化URIT适配器
        self.init_urit_adapter()
    
    def setup_logging(self):
        """设置日志系统"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置根日志器
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                RotatingFileHandler(
                    log_dir / "urit_lis_server.log",
                    maxBytes=10*1024*1024,  # 10MB
                    backupCount=5,
                    encoding='utf-8'
                ),
                logging.StreamHandler()
            ]
        )
    
    def load_config(self):
        """加载配置文件"""
        try:
            self.config = configparser.ConfigParser()
            self.config.read(self.config_file, encoding='utf-8')
            
            # 数据库配置
            self.db_config = {
                'server': self.config.get('DATABASE', 'server'),
                'database': self.config.get('DATABASE', 'database'),
                'username': self.decrypt_value(self.config.get('DATABASE', 'username')),
                'password': self.decrypt_value(self.config.get('DATABASE', 'password')),
                'driver': self.config.get('DATABASE', 'driver', fallback='ODBC Driver 17 for SQL Server')
            }
            
            # 服务器配置
            self.host = self.config.get('SYSTEM', 'host', fallback='0.0.0.0')
            self.port = self.config.getint('SYSTEM', 'port', fallback=22010)
            
            # 表名配置
            self.pat_table = self.config.get('TABLE_MAPPING', 'pat_table', fallback='lis_pat')
            self.result_table = self.config.get('TABLE_MAPPING', 'result_table', fallback='lis_result')
            
            self.logger.info("配置文件加载成功")
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            raise
    
    def decrypt_value(self, encrypted_value):
        """解密配置值"""
        try:
            if self.config.get('DATABASE', 'key_hash', fallback='') == 'simple_encrypted':
                # 简单解密：base64解码 + 字符串反转
                decoded = base64.b64decode(encrypted_value).decode('utf-8')
                return decoded[::-1]
            else:
                return encrypted_value
        except:
            return encrypted_value
    
    def init_urit_adapter(self):
        """初始化URIT适配器"""
        try:
            # URIT适配器配置
            adapter_config = {
                'field_length_limits': {
                    'patient_id': 50,
                    'sample_id': 50,
                    'test_code': 20,
                    'test_value': 200,
                    'test_unit': 20
                },
                'test_mapping': {
                    'ALT': 'ALT',
                    'AST': 'AST',
                    'TBIL': 'TBIL',
                    'DBIL': 'DBIL',
                    'TP': 'TP',
                    'ALB': 'ALB',
                    'GLU': 'GLU',
                    'BUN': 'BUN',
                    'CREA': 'CREA',
                    'UA': 'UA'
                }
            }
            
            self.urit_adapter = URITAdapter(adapter_config)
            self.logger.info("URIT适配器初始化成功")
            
        except Exception as e:
            self.logger.error(f"URIT适配器初始化失败: {e}")
            raise
    
    def get_db_connection(self):
        """获取数据库连接"""
        try:
            conn_str = (
                f"DRIVER={{{self.db_config['driver']}}};"
                f"SERVER={self.db_config['server']};"
                f"DATABASE={self.db_config['database']};"
                f"UID={self.db_config['username']};"
                f"PWD={self.db_config['password']};"
            )
            
            connection = pyodbc.connect(conn_str, timeout=30)
            return connection
            
        except Exception as e:
            self.logger.error(f"数据库连接失败: {e}")
            raise
    
    def test_database_connection(self):
        """测试数据库连接"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                result = cursor.fetchone()
                if result and result[0] == 1:
                    self.logger.info("数据库连接测试成功")
                    return True
                else:
                    self.logger.error("数据库连接测试失败")
                    return False
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            return False
    
    def write_to_database(self, standard_message):
        """写入数据库"""
        try:
            with self.get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 获取有效的YQ值（使用自定义URIT8030）
                effective_yq = self.get_effective_yq(standard_message)
                
                # 写入患者表 - 根据实际表结构调整
                patient_sql = f"""
                INSERT INTO {self.pat_table} (wyh, jyrq, yq, ybh, yblx, fb, ksdh, brxm, brxb, brly, zd, bz, bz1)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """
                
                patient_data = (
                    standard_message.message_id[:30],  # 限制为30字符
                    datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                    effective_yq[:6],  # 限制为6字符
                    standard_message.patient.sample_id[:20],  # 限制为20字符
                    '',  # yblx
                    '',  # fb
                    '',  # ksdh
                    '',  # brxm
                    '',  # brxb (性别字段名是brxb，不是xb)
                    '',  # brly
                    '',  # zd
                    '',  # bz
                    ''   # bz1
                )
                
                cursor.execute(patient_sql, patient_data)
                self.logger.info(f"患者数据写入成功: 样本ID {standard_message.patient.sample_id}")
                
                # 写入结果表
                for order in standard_message.orders:
                    for result in order.results:
                        result_sql = f"""
                        INSERT INTO {self.result_table} (wyh, jyrq, yq, ybh, xmdh, csjg, instrid, refs, result_flag, bz1, bz2, textresult, num_result)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        
                        result_data = (
                            standard_message.message_id[:20],  # 限制为20字符
                            datetime.now().strftime('%Y-%m-%d %H:%M:%S.%f')[:-3],
                            effective_yq[:6],  # 限制为6字符
                            standard_message.patient.sample_id[:20],  # 限制为20字符
                            result.test_code[:20],  # 限制为20字符
                            result.value[:50],  # 限制为50字符
                            '0',  # instrid
                            result.reference_range,
                            result.abnormal_flag,
                            '',   # bz1
                            '',   # bz2
                            '',   # textresult
                            result.value  # num_result
                        )
                        
                        cursor.execute(result_sql, result_data)
                        self.logger.info(f"检验结果写入成功: {result.test_code}={result.value}")
                
                conn.commit()
                self.stats['database_writes'] += 1
                self.logger.info(f"数据库写入完成: 消息ID {standard_message.message_id}")
                return True
                
        except Exception as e:
            self.logger.error(f"数据库写入失败: {e}")
            self.stats['database_errors'] += 1
            return False
    
    def get_effective_yq(self, standard_message):
        """获取有效的YQ值（使用URIT自定义配置）"""
        # 设置URIT的YQ自定义配置 (限制为6字符)
        standard_message.device_info.yq_config = {
            'yq_source': 'custom',
            'yq_value': 'URIT30'  # 缩短为6字符以符合数据库限制
        }
        
        # 使用适配器的YQ处理方法
        original_yq = standard_message.device_info.model or "8030"
        return self.urit_adapter.get_yq_value(standard_message, original_yq)
    
    def handle_client(self, client_socket, client_address):
        """处理客户端连接"""
        client_info = f"{client_address[0]}:{client_address[1]}"
        self.logger.info(f"客户端连接: {client_info}")
        
        try:
            while True:
                # 接收数据
                data = client_socket.recv(4096)
                if not data:
                    break
                
                self.stats['messages_received'] += 1
                self.logger.info(f"收到消息: {len(data)} 字节来自 {client_info}")
                
                # 处理消息
                response = self.process_message(data, client_address)
                
                # 发送响应
                if response:
                    client_socket.send(response)
                    self.logger.info(f"发送响应: {len(response)} 字节到 {client_info}")
                
        except Exception as e:
            self.logger.error(f"处理客户端 {client_info} 时出错: {e}")
        finally:
            try:
                client_socket.close()
                self.logger.info(f"客户端连接关闭: {client_info}")
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
            except:
                pass
    
    def process_message(self, raw_message, client_address):
        """处理收到的消息"""
        try:
            # 连接信息
            connection_info = {'address': client_address}
            
            # 检查是否为URIT消息
            if not self.urit_adapter.can_handle(raw_message, connection_info):
                self.logger.warning(f"无法识别的消息类型，来自 {client_address}")
                self.stats['messages_failed'] += 1
                return b"NAK\r"
            
            # 解析消息
            standard_message = self.urit_adapter.parse_message(raw_message, connection_info)
            self.logger.info(f"消息解析成功: 类型={standard_message.message_type}, 样本ID={standard_message.patient.sample_id}")
            
            # 写入数据库
            if self.write_to_database(standard_message):
                self.stats['messages_processed'] += 1
                
                # 生成ACK响应
                response = self.urit_adapter.generate_response(standard_message)
                self.logger.info(f"处理完成: 样本ID {standard_message.patient.sample_id}")
                return response
            else:
                self.stats['messages_failed'] += 1
                return b"NAK\r"
                
        except Exception as e:
            self.logger.error(f"消息处理失败: {e}")
            self.stats['messages_failed'] += 1
            return b"NAK\r"
    
    def start_server(self):
        """启动服务器"""
        try:
            # 测试数据库连接
            if not self.test_database_connection():
                raise Exception("数据库连接测试失败")
            
            # 创建服务器socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(10)
            
            self.running = True
            self.stats['start_time'] = datetime.now()
            
            self.logger.info(f"URIT LIS服务器启动成功: {self.host}:{self.port}")
            self.logger.info(f"数据库: {self.db_config['server']}/{self.db_config['database']}")
            self.logger.info("等待URIT设备连接...")
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    self.clients.append(client_socket)
                    
                    # 使用线程池处理客户端
                    self.executor.submit(self.handle_client, client_socket, client_address)
                    
                except Exception as e:
                    if self.running:
                        self.logger.error(f"接受连接时出错: {e}")
                        
        except Exception as e:
            self.logger.error(f"启动服务器失败: {e}")
            raise
    
    def stop_server(self):
        """停止服务器"""
        self.logger.info("正在停止URIT LIS服务器...")
        
        self.running = False
        
        # 关闭所有客户端连接
        for client in self.clients[:]:
            try:
                client.close()
            except:
                pass
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("URIT LIS服务器已停止")
    
    def print_stats(self):
        """打印统计信息"""
        if self.stats['start_time']:
            uptime = datetime.now() - self.stats['start_time']
            uptime_str = str(uptime).split('.')[0]  # 移除微秒
        else:
            uptime_str = "未知"
        
        print("\n" + "="*60)
        print("URIT LIS服务器统计信息")
        print("="*60)
        print(f"运行时间: {uptime_str}")
        print(f"接收消息: {self.stats['messages_received']}")
        print(f"处理成功: {self.stats['messages_processed']}")
        print(f"处理失败: {self.stats['messages_failed']}")
        print(f"数据库写入: {self.stats['database_writes']}")
        print(f"数据库错误: {self.stats['database_errors']}")
        
        if self.stats['messages_received'] > 0:
            success_rate = (self.stats['messages_processed'] / self.stats['messages_received']) * 100
            print(f"成功率: {success_rate:.1f}%")
        
        print("="*60)

def main():
    """主函数"""
    print("URIT设备专用LIS服务器")
    print("="*50)
    
    try:
        # 创建服务器实例
        server = URITLISServer()
        
        print(f"服务器配置:")
        print(f"  监听地址: {server.host}:{server.port}")
        print(f"  数据库: {server.db_config['server']}/{server.db_config['database']}")
        print(f"  患者表: {server.pat_table}")
        print(f"  结果表: {server.result_table}")
        print(f"  URIT适配器: 已加载")
        print("="*50)
        
        def signal_handler():
            """信号处理器"""
            server.stop_server()
        
        # 启动服务器
        try:
            server.start_server()
        except KeyboardInterrupt:
            print("\n收到中断信号...")
        finally:
            server.stop_server()
            server.print_stats()
            
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == '__main__':
    import sys
    sys.exit(main())