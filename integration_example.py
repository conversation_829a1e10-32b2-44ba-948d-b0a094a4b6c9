#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
LIS系统多设备集成示例

该文件展示了如何将多设备支持集成到现有的lis_system.py中，
提供了最小侵入性的集成方案。

使用步骤：
1. 将此文件中的代码集成到现有lis_system.py中
2. 在LISSystem类的__init__方法中添加多设备初始化
3. 在_handle_client方法中使用多设备消息处理

这种方式确保：
- 向后兼容：现有功能不受影响
- 可配置：通过配置文件控制是否启用多设备支持
- 渐进式：可以逐步迁移到多设备架构
"""

# 在lis_system.py的开头添加以下导入
from lis_system_integration import create_integration_wrapper, is_multi_device_available

class EnhancedLISSystem:
    """增强的LIS系统，支持多设备集成
    
    这是一个示例类，展示如何修改现有的LISSystem类
    """
    
    def __init__(self, config_file="config.ini"):
        """初始化增强的LIS系统"""
        # 现有的初始化代码...
        self.config_file = config_file
        self.logger = self._setup_logging()
        
        # 新增：多设备集成初始化
        self._init_multi_device_integration()
    
    def _init_multi_device_integration(self):
        """初始化多设备集成支持"""
        try:
            # 创建多设备集成包装器，传入现有的消息处理逻辑
            self.multi_device_integration = create_integration_wrapper(
                legacy_handler=self._legacy_message_processor,
                config_file=self.config_file
            )
            
            # 获取系统信息
            system_info = self.multi_device_integration.get_system_info()
            
            if system_info['multi_device_enabled']:
                self.logger.info("多设备支持已启用")
                self.logger.info(f"可用适配器: {list(system_info.get('available_adapters', {}).keys())}")
            else:
                self.logger.info("使用传统单设备模式")
                
        except Exception as e:
            self.logger.error(f"多设备集成初始化失败: {e}")
            self.multi_device_integration = None
    
    def _legacy_message_processor(self, message_data: bytes, connection_info: dict) -> bytes:
        """传统消息处理器
        
        这个方法包装了现有的消息处理逻辑，
        使其可以与多设备集成系统配合工作。
        """
        try:
            # 这里调用现有的消息处理逻辑
            # 例如：self.parse_hl7_message(), self._save_to_database() 等
            
            # 模拟现有处理逻辑
            self.logger.info(f"传统处理器处理消息: {len(message_data)} 字节")
            
            # 返回基本ACK响应（在实际集成中，这应该是现有的响应逻辑）
            return b"ACK\\r"
            
        except Exception as e:
            self.logger.error(f"传统消息处理失败: {e}")
            return b"NAK\\r"
    
    def _handle_client_enhanced(self, client_socket, address):
        """增强的客户端处理方法
        
        这个方法展示如何修改现有的_handle_client方法
        来使用多设备支持。
        """
        self.logger.info(f"接受来自 {address} 的连接")
        
        try:
            # 现有的缓冲区和消息提取逻辑...
            # 假设我们已经提取了完整的消息
            
            # 示例消息数据
            message_data = b"MSH|^~\\\\&|LIS|Hospital|LIS|Hospital|20231201120000||ORU^R01|12345|P|2.5\\r"
            
            # 准备连接信息
            connection_info = {
                'address': address,
                'socket': client_socket,
                'timestamp': None  # 可以添加更多上下文信息
            }
            
            # 使用多设备集成处理消息
            if self.multi_device_integration:
                response = self.multi_device_integration.process_message(message_data, connection_info)
            else:
                # 回退到传统处理
                response = self._legacy_message_processor(message_data, connection_info)
            
            # 发送响应
            client_socket.send(response)
            self.logger.info(f"已发送响应给 {address}")
            
        except Exception as e:
            self.logger.error(f"处理客户端 {address} 时出错: {e}")
            try:
                client_socket.send(b"NAK\\r")
            except:
                pass
        finally:
            client_socket.close()
    
    def _setup_logging(self):
        """设置日志（简化版）"""
        import logging
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger(__name__)
    
    def get_system_status(self):
        """获取系统状态（包括多设备信息）"""
        status = {
            'basic_info': '现有系统信息...',
            'multi_device_available': is_multi_device_available()
        }
        
        if self.multi_device_integration:
            status['multi_device_info'] = self.multi_device_integration.get_system_info()
        
        return status

# =============================================================================
# 集成指南
# =============================================================================

"""
集成指南：如何将多设备支持添加到现有lis_system.py

1. 添加导入语句：
   在lis_system.py开头添加：
   from lis_system_integration import create_integration_wrapper, is_multi_device_available

2. 修改LISSystem.__init__方法：
   在现有初始化代码后添加：
   self._init_multi_device_integration()

3. 添加多设备初始化方法：
   def _init_multi_device_integration(self):
       try:
           self.multi_device_integration = create_integration_wrapper(
               legacy_handler=self._legacy_message_processor,
               config_file=self.config_file
           )
       except Exception as e:
           self.logger.error(f"多设备集成初始化失败: {e}")
           self.multi_device_integration = None

4. 创建传统消息处理包装器：
   def _legacy_message_processor(self, message_data: bytes, connection_info: dict) -> bytes:
       # 调用现有的消息处理逻辑
       # 例如：parse_hl7_message(), _save_to_database() 等
       return response_bytes

5. 修改_handle_client方法：
   在消息处理部分，替换：
   # 原来的处理逻辑
   
   为：
   if self.multi_device_integration:
       response = self.multi_device_integration.process_message(message_data, connection_info)
   else:
       response = self._legacy_message_processor(message_data, connection_info)

6. 配置文件：
   确保config.ini或config_multi_device.ini中有正确的多设备配置

这种集成方式的优点：
- 最小侵入性：只需要少量代码修改
- 向后兼容：如果多设备模块不可用，自动回退到传统模式
- 渐进式迁移：可以逐步将更多功能迁移到多设备架构
- 配置驱动：通过配置文件控制功能启用/禁用
"""

if __name__ == "__main__":
    # 测试增强的LIS系统
    system = EnhancedLISSystem("config_multi_device.ini")
    status = system.get_system_status()
    print("系统状态:")
    for key, value in status.items():
        print(f"  {key}: {value}")