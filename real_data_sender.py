#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
URIT设备真实数据发送测试

模拟URIT设备向LIS系统发送真实的HL7消息，
通过socket连接到lis_system.py，验证完整的数据处理流程：
1. 消息发送
2. 消息识别和解析  
3. 数据库写入
4. 响应接收
"""

import socket
import time
import threading
import logging
from datetime import datetime, timedelta
import random

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class URITDataSender:
    """URIT设备数据发送器"""
    
    def __init__(self, host='127.0.0.1', port=22010):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        
    def connect(self):
        """连接到LIS系统"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.host, self.port))
            self.connected = True
            logger.info(f"已连接到LIS系统: {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"连接LIS系统失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            try:
                self.socket.close()
                self.connected = False
                logger.info("已断开与LIS系统的连接")
            except:
                pass
    
    def send_message(self, message):
        """发送HL7消息"""
        if not self.connected:
            logger.error("未连接到LIS系统")
            return False
        
        try:
            # 发送消息
            self.socket.send(message)
            logger.info(f"已发送消息，长度: {len(message)} 字节")
            
            # 接收响应
            response = self.socket.recv(1024)
            logger.info(f"收到响应: {response.decode('utf-8', errors='ignore')}")
            
            return True
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False
    
    def generate_urit_message(self, sample_id, test_code, test_value, unit, ref_range, abnormal_flag='N'):
        """生成URIT HL7消息"""
        
        # 当前时间
        now = datetime.now()
        timestamp = now.strftime("%Y%m%d%H%M%S")
        date_str = now.strftime("%Y-%m-%d")
        
        # 构建HL7消息
        msh = f"MSH|^~\\&|urit|8030|||{timestamp}||ORU^R01|{sample_id}|P|2.3.1||||0||ASCII|||"
        pid = f"PID|1||||||0|||||0|||||||||||||||||||"
        obr = f"OBR|1||{sample_id}|urit^8030|N||{date_str}|||||||||||||||||||||||||||||||||||||||"
        obx = f"OBX|1|NM|1|{test_code}|{test_value}|{unit}|{ref_range}|{abnormal_flag}|||F||0.0000|{date_str}||检验师||"
        
        # 组合完整消息（使用\r分隔）
        message = f"{msh}\r{pid}\r{obr}\r{obx}"
        
        return message.encode('utf-8')

def generate_test_data():
    """生成测试数据集"""
    test_data = [
        # 第一组数据 - 肝功能检查
        {
            'sample_id': '202408030001',
            'tests': [
                ('ALT', '45.2', 'U/L', '0.0-40.0', 'H'),  # 偏高
                ('AST', '38.5', 'U/L', '0.0-40.0', 'N'),  # 正常
                ('TBIL', '15.8', 'umol/L', '3.4-20.5', 'N'),  # 正常
                ('DBIL', '4.2', 'umol/L', '0.0-6.8', 'N'),  # 正常
            ]
        },
        # 第二组数据 - 肾功能检查
        {
            'sample_id': '202408030002', 
            'tests': [
                ('BUN', '6.8', 'mmol/L', '2.9-8.2', 'N'),  # 正常
                ('CREA', '95.0', 'umol/L', '44-133', 'N'),  # 正常
                ('UA', '420.0', 'umol/L', '208-428', 'N'),  # 正常
            ]
        },
        # 第三组数据 - 蛋白质检查  
        {
            'sample_id': '202408030003',
            'tests': [
                ('TP', '72.5', 'g/L', '64-83', 'N'),  # 正常
                ('ALB', '41.2', 'g/L', '35-55', 'N'),  # 正常
            ]
        },
        # 第四组数据 - 血糖检查
        {
            'sample_id': '202408030004',
            'tests': [
                ('GLU', '8.9', 'mmol/L', '3.9-6.1', 'H'),  # 偏高
            ]
        },
        # 第五组数据 - 综合检查
        {
            'sample_id': '202408030005',
            'tests': [
                ('ALT', '28.0', 'U/L', '0.0-40.0', 'N'),  # 正常
                ('AST', '32.0', 'U/L', '0.0-40.0', 'N'),  # 正常
                ('GLU', '5.2', 'mmol/L', '3.9-6.1', 'N'),  # 正常
                ('CREA', '78.0', 'umol/L', '44-133', 'N'),  # 正常
            ]
        }
    ]
    
    return test_data

def run_real_data_test():
    """运行真实数据测试"""
    print("=" * 70)
    print("URIT设备真实数据发送测试")
    print("=" * 70)
    print()
    
    # 提示用户确认
    print("⚠️  注意：此测试将向数据库写入真实数据")
    print("📋 测试内容：")
    print("   - 5个样本的检验数据")
    print("   - 包含肝功能、肾功能、蛋白质、血糖等检查")
    print("   - 数据将写入 lis_pat 和 lis_result 表")
    print()
    
    # 数据库信息
    print("🗃️  目标数据库：")
    print("   - 服务器: ***********")
    print("   - 数据库: lis2002") 
    print("   - 表: lis_pat, lis_result")
    print("   - YQ标识: URIT8030")
    print()
    
    print("🔧 请确保：")
    print("   1. lis_system.py 正在运行")
    print("   2. 数据库连接正常")
    print("   3. 多设备模式已启用")
    print()
    
    # 确认继续
    confirm = input("是否继续进行真实数据测试？(y/N): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return False
    
    print("\n🚀 开始发送真实数据...")
    print("=" * 70)
    
    # 创建数据发送器
    sender = URITDataSender()
    
    # 连接到LIS系统
    if not sender.connect():
        print("❌ 无法连接到LIS系统，请确保 lis_system.py 正在运行")
        return False
    
    # 生成测试数据
    test_data = generate_test_data()
    
    # 发送数据统计
    total_samples = len(test_data)
    total_tests = sum(len(sample['tests']) for sample in test_data)
    sent_samples = 0
    sent_tests = 0
    failed_messages = 0
    
    print(f"📊 待发送数据：{total_samples}个样本，{total_tests}项检验")
    print()
    
    try:
        for i, sample_data in enumerate(test_data, 1):
            sample_id = sample_data['sample_id']
            tests = sample_data['tests']
            
            print(f"📤 发送样本 {i}/{total_samples}: {sample_id}")
            
            # 为每个检验项目发送单独的消息
            for j, (test_code, test_value, unit, ref_range, abnormal_flag) in enumerate(tests, 1):
                try:
                    # 生成HL7消息
                    message = sender.generate_urit_message(
                        sample_id, test_code, test_value, unit, ref_range, abnormal_flag
                    )
                    
                    # 发送消息
                    if sender.send_message(message):
                        print(f"   ✅ {test_code}: {test_value} {unit} ({abnormal_flag})")
                        sent_tests += 1
                    else:
                        print(f"   ❌ {test_code}: 发送失败")
                        failed_messages += 1
                    
                    # 间隔发送，避免过快
                    time.sleep(0.5)
                    
                except Exception as e:
                    print(f"   ❌ {test_code}: 错误 - {e}")
                    failed_messages += 1
            
            sent_samples += 1
            print(f"   📋 样本 {sample_id} 完成 ({len(tests)}项检验)")
            print()
            
            # 样本间隔稍长
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断发送")
    except Exception as e:
        print(f"\n❌ 发送过程出错: {e}")
    finally:
        sender.disconnect()
    
    # 显示发送结果
    print("=" * 70)
    print("📊 发送结果统计")
    print("=" * 70)
    print(f"样本总数: {total_samples}")
    print(f"成功样本: {sent_samples}")
    print(f"检验总数: {total_tests}")
    print(f"成功检验: {sent_tests}")
    print(f"失败消息: {failed_messages}")
    print(f"成功率: {(sent_tests/total_tests*100):.1f}%")
    print()
    
    if sent_tests > 0:
        print("✅ 数据发送完成！")
        print()
        print("🔍 请检查数据库中的数据：")
        print("   SELECT * FROM lis_pat WHERE yq = 'URIT8030' ORDER BY jyrq DESC")
        print("   SELECT * FROM lis_result WHERE yq = 'URIT8030' ORDER BY jyrq DESC")
        print()
        print("📋 预期结果：")
        print("   - lis_pat表：5条患者记录")
        print("   - lis_result表：12条检验结果记录")
        print("   - YQ字段：URIT8030")
        print("   - 样本ID：1, 2, 3, 4, 5 (处理后)")
        
        # 生成SQL查询用于验证
        print()
        print("🔧 验证SQL语句：")
        print("-- 查看刚发送的患者数据")
        print("SELECT wyh, yq, ybh, jyrq FROM lis_pat WHERE yq = 'URIT8030' AND jyrq >= CONVERT(date, GETDATE()) ORDER BY jyrq DESC;")
        print()
        print("-- 查看刚发送的检验结果")
        print("SELECT wyh, yq, ybh, xmdh, csjg, refs, result_flag, jyrq FROM lis_result WHERE yq = 'URIT8030' AND jyrq >= CONVERT(date, GETDATE()) ORDER BY jyrq DESC;")
        
        return True
    else:
        print("❌ 未成功发送任何数据")
        return False

def check_lis_system_running():
    """检查LIS系统是否运行"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('127.0.0.1', 22010))
        sock.close()
        return result == 0
    except:
        return False

if __name__ == '__main__':
    try:
        # 检查LIS系统是否运行
        if not check_lis_system_running():
            print("❌ LIS系统未运行")
            print("请先启动 lis_system.py:")
            print("   python lis_system.py")
            print()
            input("启动LIS系统后按回车继续...")
        
        # 运行真实数据测试
        success = run_real_data_test()
        
        if success:
            print("\n🎉 真实数据测试完成！")
        else:
            print("\n⚠️ 测试未完全成功，请检查日志")
            
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()