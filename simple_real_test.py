#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的真实数据测试
"""

import socket
import time
import threading
from datetime import datetime
from urit_lis_server import URITLISServer

def run_server(server):
    """运行服务器"""
    try:
        server.start_server()
    except:
        pass

def main():
    print("简单URIT真实数据测试")
    print("=" * 50)
    
    # 1. 创建服务器
    print("1. 创建URIT服务器...")
    server = URITLISServer()
    print("   OK 服务器创建成功")
    
    # 2. 后台启动服务器
    print("2. 启动服务器...")
    server_thread = threading.Thread(target=run_server, args=(server,), daemon=True)
    server_thread.start()
    time.sleep(2)
    print("   OK 服务器已启动")
    
    # 3. 发送测试数据
    print("3. 发送测试数据...")
    
    # 构建简单的URIT消息
    now = datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")
    date_str = now.strftime("%Y-%m-%d")
    
    # 测试消息
    test_data = [
        ("202408040001", "ALT", "42.5", "U/L", "0.0-40.0", "H"),
        ("202408040002", "AST", "35.0", "U/L", "0.0-40.0", "N"),
        ("202408040003", "GLU", "5.8", "mmol/L", "3.9-6.1", "N"),
    ]
    
    sent_count = 0
    for sample_id, test_code, value, unit, ref_range, flag in test_data:
        try:
            # 构建HL7消息
            msh = f"MSH|^~\\&|urit|8030|||{timestamp}||ORU^R01|{sample_id}|P|2.3.1||||0||ASCII|||"
            pid = f"PID|1||||||0|||||0|||||||||||||||||||"
            obr = f"OBR|1||{sample_id}|urit^8030|N||{date_str}|||||||||||||||||||||||||||||||||||||||"
            obx = f"OBX|1|NM|1|{test_code}|{value}|{unit}|{ref_range}|{flag}|||F||0.0000|{date_str}||检验师||"
            
            message = f"{msh}\r{pid}\r{obr}\r{obx}".encode('utf-8')
            
            # 发送消息
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect(('127.0.0.1', 22010))
            sock.send(message)
            
            # 简单接收（不等待完整响应）
            try:
                sock.recv(1024)
            except:
                pass
            
            sock.close()
            
            print(f"   样本{sample_id[-1]}: {test_code}={value} {unit} - 已发送")
            sent_count += 1
            time.sleep(0.3)
            
        except Exception as e:
            print(f"   样本{sample_id[-1]}: 发送失败 - {e}")
    
    print(f"   总计发送: {sent_count}/3 条消息")
    
    # 4. 等待处理
    print("4. 等待数据处理...")
    time.sleep(5)
    
    # 5. 检查数据库
    print("5. 检查数据库...")
    try:
        with server.get_db_connection() as conn:
            cursor = conn.cursor()
            
            today = datetime.now().strftime('%Y-%m-%d')
            
            # 查询数据
            cursor.execute(f"SELECT COUNT(*) FROM {server.pat_table} WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = '{today}'")
            pat_count = cursor.fetchone()[0]
            
            cursor.execute(f"SELECT COUNT(*) FROM {server.result_table} WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = '{today}'")
            result_count = cursor.fetchone()[0]
            
            print(f"   患者记录: {pat_count} 条")
            print(f"   检验结果: {result_count} 条")
            
            if pat_count > 0 and result_count > 0:
                print("   ✓ 数据写入成功")
                
                # 显示具体数据
                cursor.execute(f"SELECT TOP 3 ybh, xmdh, csjg FROM {server.result_table} WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = '{today}' ORDER BY jyrq DESC")
                results = cursor.fetchall()
                
                print("   最新检验结果:")
                for row in results:
                    print(f"     样本{row[0]}: {row[1]}={row[2]}")
                
                return True
            else:
                print("   ✗ 未找到数据")
                return False
                
    except Exception as e:
        print(f"   ✗ 数据库检查失败: {e}")
        return False
    finally:
        # 6. 停止服务器
        print("6. 停止服务器...")
        server.stop_server()
        print("   OK 服务器已停止")

if __name__ == '__main__':
    try:
        success = main()
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 真实数据测试成功！")
            print("URIT设备数据已成功写入数据库")
            print("可以查看数据库验证:")
            print("SELECT * FROM lis_pat WHERE yq = 'URIT8030'")
            print("SELECT * FROM lis_result WHERE yq = 'URIT8030'")
        else:
            print("❌ 真实数据测试失败")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()