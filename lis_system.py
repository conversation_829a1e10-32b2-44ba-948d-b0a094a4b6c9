import socket
import threading
import time
import json
import logging
import configparser
import pyodbc
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from typing import List, Optional
import os
# 可选依赖：系统托盘功能
try:
    import pystray
    from PIL import Image, ImageDraw
    TRAY_AVAILABLE = True
except ImportError:
    TRAY_AVAILABLE = False
    print("警告: pystray 或 PIL 模块未安装，系统托盘功能将被禁用")
import sys

# 可选依赖：Windows特定功能
try:
    import win32gui
    import win32process
    import psutil
    import ctypes
    from ctypes import wintypes
    WINDOWS_FEATURES_AVAILABLE = True
except ImportError:
    WINDOWS_FEATURES_AVAILABLE = False
    print("警告: Windows特定模块未安装，部分功能将被禁用")
import tkinter as tk
from tkinter import messagebox
import base64
from concurrent.futures import ThreadPoolExecutor
from logging.handlers import RotatingFileHandler

# Windows控制台模式常量
ENABLE_QUICK_EDIT_MODE = 0x0040
ENABLE_INSERT_MODE = 0x0020
ENABLE_MOUSE_INPUT = 0x0010
STD_INPUT_HANDLE = -10

# 全局变量
lis_instance = None
tray_icon = None

class ModernMessageBox:
    """现代化的消息框"""
    
    @staticmethod
    def show_info(title, message, parent=None):
        """显示信息对话框"""
        ModernMessageBox._create_dialog(title, message, "info", parent)
    
    @staticmethod
    def show_warning(title, message, parent=None):
        """显示警告对话框"""
        ModernMessageBox._create_dialog(title, message, "warning", parent)
    
    @staticmethod
    def show_error(title, message, parent=None):
        """显示错误对话框"""
        ModernMessageBox._create_dialog(title, message, "error", parent)
    
    @staticmethod
    def ask_yes_no(title, message, parent=None):
        """显示是/否对话框"""
        return ModernMessageBox._create_dialog(title, message, "question", parent, return_result=True)
    
    @staticmethod
    def _create_dialog(title, message, dialog_type, parent=None, return_result=False):
        """创建对话框"""
        try:
            # 直接创建GUI，不使用线程
            root = tk.Tk()
            root.withdraw()
            
            dialog = tk.Toplevel(root)
            dialog.title(title)
            dialog.geometry("400x200")
            dialog.configure(bg="#2c3e50")
            dialog.resizable(False, False)
            
            # 设置图标
            icons = {
                "info": "ℹ️",
                "warning": "⚠️", 
                "error": "❌",
                "question": "❓"
            }
            
            colors = {
                "info": "#3498db",
                "warning": "#f39c12",
                "error": "#e74c3c", 
                "question": "#9b59b6"
            }
            
            result = [False]  # 用列表存储结果，以便在内部函数中修改
            
            # 标题栏
            title_frame = tk.Frame(dialog, bg=colors[dialog_type], height=50)
            title_frame.pack(fill=tk.X)
            title_frame.pack_propagate(False)
            
            title_label = tk.Label(title_frame, 
                                  text=f"{icons[dialog_type]} {title}",
                                  font=('Microsoft YaHei UI', 14, 'bold'),
                                  bg=colors[dialog_type], fg="white")
            title_label.pack(expand=True)
            
            # 内容区域
            content_frame = tk.Frame(dialog, bg="#2c3e50")
            content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            message_label = tk.Label(content_frame, text=message,
                                    font=('Microsoft YaHei UI', 11),
                                    bg="#2c3e50", fg="#ecf0f1",
                                    wraplength=350, justify=tk.LEFT)
            message_label.pack(expand=True)
            
            # 按钮区域
            button_frame = tk.Frame(content_frame, bg="#2c3e50")
            button_frame.pack(fill=tk.X, pady=(10, 0))
            
            def close_dialog(user_result=False):
                result[0] = user_result
                try:
                    dialog.destroy()
                    root.quit()
                    root.destroy()
                except:
                    pass
            
            # 关闭窗口事件
            dialog.protocol("WM_DELETE_WINDOW", lambda: close_dialog(False))
            
            if dialog_type == "question":
                # 是/否按钮
                yes_btn = tk.Button(button_frame, text="是", 
                                   command=lambda: close_dialog(True),
                                   bg="#27ae60", fg="white",
                                   font=('Microsoft YaHei UI', 10, 'bold'),
                                   relief=tk.FLAT, bd=0, padx=20, pady=5,
                                   cursor="hand2")
                yes_btn.pack(side=tk.RIGHT, padx=(5, 0))
                
                no_btn = tk.Button(button_frame, text="否",
                                  command=lambda: close_dialog(False), 
                                  bg="#e74c3c", fg="white",
                                  font=('Microsoft YaHei UI', 10, 'bold'),
                                  relief=tk.FLAT, bd=0, padx=20, pady=5,
                                  cursor="hand2")
                no_btn.pack(side=tk.RIGHT, padx=(5, 0))
            else:
                # 确定按钮
                ok_btn = tk.Button(button_frame, text="确定",
                                  command=lambda: close_dialog(True),
                                  bg=colors[dialog_type], fg="white",
                                  font=('Microsoft YaHei UI', 10, 'bold'),
                                  relief=tk.FLAT, bd=0, padx=20, pady=5,
                                  cursor="hand2")
                ok_btn.pack(side=tk.RIGHT)
            
            # 居中显示
            dialog.transient(root)
            dialog.grab_set()
            dialog.update_idletasks()
            
            screen_width = dialog.winfo_screenwidth()
            screen_height = dialog.winfo_screenheight()
            x = (screen_width - 400) // 2
            y = (screen_height - 200) // 2
                
            dialog.geometry(f"400x200+{x}+{y}")
            dialog.focus_force()
            
            # 启动消息循环并等待用户操作
            root.mainloop()
            
            return result[0] if return_result else True
            
        except Exception as e:
            print(f"创建对话框时出错: {e}")
            return False if return_result else False

def encrypt_data(data: str) -> str:
    """使用简单的Base64编码加密数据"""
    # 先反转字符串，然后进行base64编码
    reversed_data = data[::-1]
    return base64.b64encode(reversed_data.encode()).decode()

def decrypt_data(encrypted_data: str) -> str:
    """解密Base64编码的数据"""
    # base64解码，然后反转字符串
    decoded = base64.b64decode(encrypted_data.encode()).decode()
    return decoded[::-1]

def show_centered_messagebox(message, title, box_type=0x40):
    """显示居中的Windows MessageBox，确保在屏幕中心"""
    if WINDOWS_FEATURES_AVAILABLE:
        import ctypes
        
        # 使用MB_TOPMOST确保MessageBox在最前面，Windows会自动居中
        # 0x40000 = MB_TOPMOST, 确保窗口在最前面
        return ctypes.windll.user32.MessageBoxW(0, message, title, box_type | 0x40000)
    else:
        print(f"{title}: {message}")
        return 1  # 默认返回OK

# 数据类定义
@dataclass
class TestResult:
    test_name: str          # 检验项目名称
    value: float            # 检验结果值
    unit: str              # 单位
    reference_range: str    # 参考范围
    abnormal_flag: str      # 异常标志
    observation_time: str   # 观察时间
    instrument_id: str      # 仪器ID
    reagent_lot: str       # 试剂批号
    reagent_expiry: str    # 试剂有效期
    control_id: str        # 质控ID

@dataclass
class Order:
    order_id: str          # 医嘱ID
    sample_id: str         # 样本ID
    test_type: str         # 检验类型
    order_time: str        # 医嘱时间
    report_time: str       # 报告时间
    priority: str          # 优先级
    results: List[TestResult]  # 检验结果列表

@dataclass
class Patient:
    patient_id: str        # 患者ID
    sex: str              # 性别
    age: str              # 年龄
    name: str             # 姓名
    birth_date: str       # 出生日期
    department: str       # 科室
    bed_no: str          # 床号

@dataclass
class HL7Message:
    message_type: str      # 消息类型
    message_control_id: str # 消息控制ID
    sending_facility: str  # 发送机构
    receiving_facility: str # 接收机构
    message_time: str     # 消息时间
    patient: Patient      # 患者信息
    orders: List[Order]   # 医嘱列表

class SettingsWindow:
    def __init__(self, config_path: str):
        self.window = tk.Tk()
        self.window.withdraw()  # 先隐藏窗口，防止闪烁
        self.window.title("瑞美LIS解码系统 - 配置设置")
        self.window.geometry("520x450")
        self.window.configure(bg="#2c3e50")
        self.window.resizable(False, False)
        
        # 设置窗口图标
        try:
            self.window.iconbitmap("app_icon.ico")
        except:
            pass
            
        self.config_path = config_path
        self.config = configparser.ConfigParser()
        self.config.read(config_path, encoding='utf-8')

        # 创建样式
        self._create_styles()
        
        # 主容器
        main_container = tk.Frame(self.window, bg="#2c3e50")
        main_container.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
        
        # 标题区域
        title_frame = tk.Frame(main_container, bg="#34495e", height=60)
        title_frame.pack(fill=tk.X, pady=0)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🔧 数据库连接设置", 
                              font=('Microsoft YaHei UI', 16, 'bold'), 
                              bg="#34495e", fg="#ecf0f1")
        title_label.pack(expand=True)
        
        # 内容区域
        content_frame = tk.Frame(main_container, bg="#2c3e50")
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # 表单容器
        form_container = tk.Frame(content_frame, bg="#34495e", relief=tk.RAISED, bd=1)
        form_container.pack(fill=tk.X, pady=(0, 20))
        
        form_frame = tk.Frame(form_container, bg="#34495e")
        form_frame.pack(fill=tk.X, padx=25, pady=25)
        
        # 数据库配置标题
        db_title = tk.Label(form_frame, text="📊 数据库配置", 
                           font=('Microsoft YaHei UI', 12, 'bold'), 
                           bg="#34495e", fg="#3498db")
        db_title.pack(anchor=tk.W, pady=(0, 15))

        # 服务器
        self._create_input_field(form_frame, "🖥️ 服务器地址:", 
                                self.config.get('DATABASE', 'server', fallback='localhost'),
                                'server_var')

        # 数据库名
        self._create_input_field(form_frame, "🗄️ 数据库名:", 
                                self.config.get('DATABASE', 'database', fallback='lis'),
                                'database_var')

        # 用户名
        username = self.config.get('DATABASE', 'username', fallback='sa')
        try:
            if 'key_hash' in self.config['DATABASE']:
                username = decrypt_data(username)
        except:
            pass
        self._create_input_field(form_frame, "👤 用户名:", username, 'username_var')

        # 密码
        password = self.config.get('DATABASE', 'password', fallback='')
        try:
            if 'key_hash' in self.config['DATABASE']:
                password = decrypt_data(password)
        except:
            pass
        self._create_input_field(form_frame, "🔐 密码:", password, 'password_var', show="*")

        # 系统配置标题
        sys_title = tk.Label(form_frame, text="⚙️ 系统配置", 
                            font=('Microsoft YaHei UI', 12, 'bold'), 
                            bg="#34495e", fg="#e74c3c")
        sys_title.pack(anchor=tk.W, pady=(20, 15))

        # 端口设置
        self._create_input_field(form_frame, "🔌 监听端口:", 
                                self.config.get('SYSTEM', 'port', fallback='22010'),
                                'port_var')
                                
        # 加密选项
        encrypt_frame = tk.Frame(form_frame, bg="#34495e")
        encrypt_frame.pack(fill=tk.X, pady=(15, 0))
        
        self.encrypt_var = tk.BooleanVar(value=True)
        encrypt_check = tk.Checkbutton(encrypt_frame, text="🔒 加密保存用户名和密码", 
                                      variable=self.encrypt_var,
                                      bg="#34495e", fg="#ecf0f1", 
                                      font=('Microsoft YaHei UI', 10),
                                      activebackground="#34495e", 
                                      activeforeground="#3498db",
                                      selectcolor="#2c3e50")
        encrypt_check.pack(anchor=tk.W)

        # 按钮区域
        button_frame = tk.Frame(content_frame, bg="#2c3e50")
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 保存按钮
        save_btn = tk.Button(button_frame, text="💾 保存配置", 
                            command=self.save_settings,
                            bg="#27ae60", fg="white", 
                            font=('Microsoft YaHei UI', 11, 'bold'),
                            relief=tk.FLAT, bd=0, padx=20, pady=8,
                            activebackground="#2ecc71", activeforeground="white",
                            cursor="hand2")
        save_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 取消按钮
        cancel_btn = tk.Button(button_frame, text="❌ 取消", 
                              command=self.window.destroy,
                              bg="#e74c3c", fg="white",
                              font=('Microsoft YaHei UI', 11, 'bold'),
                              relief=tk.FLAT, bd=0, padx=20, pady=8,
                              activebackground="#c0392b", activeforeground="white",
                              cursor="hand2")
        cancel_btn.pack(side=tk.LEFT, padx=10)
        
        # 测试连接按钮
        test_btn = tk.Button(button_frame, text="🔍 测试连接", 
                            command=self.test_connection,
                            bg="#3498db", fg="white",
                            font=('Microsoft YaHei UI', 11, 'bold'),
                            relief=tk.FLAT, bd=0, padx=20, pady=8,
                            activebackground="#2980b9", activeforeground="white",
                            cursor="hand2")
        test_btn.pack(side=tk.RIGHT)

        self.center_window()
        self.window.deiconify()
        self.window.transient()
        self.window.grab_set()
        self.window.focus_force()

    def _create_styles(self):
        """创建统一的样式"""
        self.label_style = {
            'font': ('Microsoft YaHei UI', 10),
            'bg': '#34495e',
            'fg': '#bdc3c7',
            'anchor': 'w'
        }
        
        self.entry_style = {
            'font': ('Microsoft YaHei UI', 10),
            'bg': '#ecf0f1',
            'fg': '#2c3e50',
            'relief': tk.FLAT,
            'bd': 1,
            'insertbackground': '#2c3e50'
        }

    def _create_input_field(self, parent, label_text, default_value, var_name, show=None):
        """创建输入字段"""
        field_frame = tk.Frame(parent, bg="#34495e")
        field_frame.pack(fill=tk.X, pady=(0, 12))
        
        label = tk.Label(field_frame, text=label_text, width=15, **self.label_style)
        label.pack(side=tk.LEFT, padx=(0, 15))
        
        var = tk.StringVar(value=default_value)
        setattr(self, var_name, var)
        
        entry_kwargs = self.entry_style.copy()
        if show:
            entry_kwargs['show'] = show
            
        entry = tk.Entry(field_frame, textvariable=var, **entry_kwargs)
        entry.pack(side=tk.LEFT, fill=tk.X, expand=True, ipady=5)
        
        # 特殊处理端口输入
        if var_name == 'port_var':
            setattr(self, 'port_entry', entry)
            def validate_port(*args):
                try:
                    value = var.get()
                    if value and not value.isdigit():
                        var.set(''.join(filter(str.isdigit, value)))
                except:
                    pass
            var.trace('w', validate_port)
    
    def test_connection(self):
        """测试数据库连接"""
        try:
            import pyodbc
            
            server = self.server_var.get()
            database = self.database_var.get()
            username = self.username_var.get()
            password = self.password_var.get()
            
            if not all([server, database, username]):
                self._show_message("错误", "请填写完整的数据库连接信息", "error")
                return
                
            conn_str = f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}"
            
            # 创建进度窗口
            progress_window = self._create_progress_window()
            
            def test_conn():
                try:
                    conn = pyodbc.connect(conn_str, timeout=10)
                    conn.close()
                    progress_window.destroy()
                    self._show_message("成功", "数据库连接测试成功！", "info")
                except Exception as e:
                    progress_window.destroy()
                    self._show_message("连接失败", f"数据库连接失败:\n{str(e)}", "error")
            
            self.window.after(100, test_conn)
            
        except ImportError:
            self._show_message("错误", "缺少pyodbc模块，无法测试连接", "error")
        except Exception as e:
            self._show_message("错误", f"测试连接时出错: {str(e)}", "error")
    
    def _create_progress_window(self):
        """创建进度窗口"""
        progress = tk.Toplevel(self.window)
        progress.title("正在测试...")
        progress.geometry("300x100")
        progress.configure(bg="#2c3e50")
        progress.resizable(False, False)
        progress.transient(self.window)
        progress.grab_set()
        
        # 居中显示
        x = self.window.winfo_x() + (self.window.winfo_width() // 2) - 150
        y = self.window.winfo_y() + (self.window.winfo_height() // 2) - 50
        progress.geometry(f"300x100+{x}+{y}")
        
        tk.Label(progress, text="正在测试数据库连接...", 
                bg="#2c3e50", fg="#ecf0f1",
                font=('Microsoft YaHei UI', 12)).pack(expand=True)
        
        return progress
    
    def _show_message(self, title, message, msg_type="info"):
        """显示消息对话框"""
        if msg_type == "error":
            tk.messagebox.showerror(title, message, parent=self.window)
        elif msg_type == "warning":
            tk.messagebox.showwarning(title, message, parent=self.window)
        else:
            tk.messagebox.showinfo(title, message, parent=self.window)

    def center_window(self):
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        window_width = 520
        window_height = 450
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def save_settings(self):
        try:
            port = int(self.port_var.get())
            if not (1024 <= port <= 65535):
                raise ValueError("端口号必须在1024-65535之间")
                
            # 确保配置段存在
            if 'DATABASE' not in self.config:
                self.config['DATABASE'] = {}
            if 'SYSTEM' not in self.config:
                self.config['SYSTEM'] = {}
                
            # 保存数据库配置
            self.config['DATABASE']['server'] = self.server_var.get()
            self.config['DATABASE']['database'] = self.database_var.get()
            
            # 保存系统配置
            self.config['SYSTEM']['port'] = str(port)
            
            # 确保其他SYSTEM配置项存在
            if 'host' not in self.config['SYSTEM']:
                self.config['SYSTEM']['host'] = '0.0.0.0'
            if 'raw_dir' not in self.config['SYSTEM']:
                self.config['SYSTEM']['raw_dir'] = 'raw'
            if 'processed_dir' not in self.config['SYSTEM']:
                self.config['SYSTEM']['processed_dir'] = 'result'
            if 'log_file' not in self.config['SYSTEM']:
                self.config['SYSTEM']['log_file'] = 'lis_system.log'
            if 'processed_files' not in self.config['SYSTEM']:
                self.config['SYSTEM']['processed_files'] = 'processed_files.json'
                
            # 处理用户名和密码（加密或明文）
            if self.encrypt_var.get():
                try:
                    username = self.username_var.get()
                    password = self.password_var.get()
                    encrypted_username = encrypt_data(username)
                    encrypted_password = encrypt_data(password)
                    self.config['DATABASE']['username'] = encrypted_username
                    self.config['DATABASE']['password'] = encrypted_password
                    self.config['DATABASE']['key_hash'] = "simple_encrypted"
                    
                    with open(self.config_path, 'w', encoding='utf-8') as f:
                        self.config.write(f)
                    tk.messagebox.showinfo("成功", "数据库配置已加密保存")
                    self.window.destroy()
                except Exception as e:
                    tk.messagebox.showerror("加密错误", f"加密数据库配置时出错: {str(e)}")
            else:
                self.config['DATABASE']['username'] = self.username_var.get()
                self.config['DATABASE']['password'] = self.password_var.get()
                if 'key_hash' in self.config['DATABASE']:
                    del self.config['DATABASE']['key_hash']
                    
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    self.config.write(f)
                tk.messagebox.showinfo("成功", "配置已保存")
                self.window.destroy()
                
        except ValueError as e:
            tk.messagebox.showerror("错误", str(e))
        except Exception as e:
            tk.messagebox.showerror("错误", f"保存配置时出错: {str(e)}")
            import traceback
            print(f"详细错误信息: {traceback.format_exc()}")

class LISSystem:
    def __init__(self, config_file="config.ini"):
        """
        初始化LIS系统
        :param config_file: 配置文件路径
        """
        # 获取程序所在目录的绝对路径
        if getattr(sys, 'frozen', False):
            # 如果是打包后的可执行文件
            self.program_dir = os.path.dirname(os.path.abspath(sys.executable))
        else:
            # 如果是Python脚本
            self.program_dir = os.path.dirname(os.path.abspath(__file__))
        
        print(f"程序所在目录: {self.program_dir}")
        
        # 定义配置文件可能存在的路径列表
        possible_paths = [
            os.path.join(self.program_dir, config_file),  # 程序所在目录
            os.path.join(os.path.dirname(self.program_dir), config_file),  # 上级目录
            os.path.join(r"C:\Lis2002\I2900", config_file),  # I2900主程序目录
            os.path.join(os.path.dirname(os.path.dirname(self.program_dir)), "I2900", config_file)  # 相对于程序的I2900目录
        ]
        
        # 遍历所有可能的路径，找到第一个存在的配置文件
        config_path = None
        for path in possible_paths:
            print(f"尝试加载配置文件: {path}")  # 调试用
            if os.path.exists(path):
                config_path = path
                break
        
        if config_path is None:
            # 如果找不到配置文件，创建默认配置文件
            default_config = {
                'SYSTEM': {
                    'host': '127.0.0.1',
                    'port': '22010',
                    'raw_dir': os.path.join(self.program_dir, 'raw'),  # 使用绝对路径
                    'processed_dir': os.path.join(self.program_dir, 'result'),  # 使用绝对路径
                    'log_file': os.path.join(self.program_dir, 'lis_system.log'),  # 使用绝对路径
                    'processed_files': os.path.join(self.program_dir, 'processed_files.json')  # 使用绝对路径
                },
                'DATABASE': {
                    'driver': 'SQL Server',
                    'server': 'localhost',
                    'database': 'lis',
                    'username': 'sa',
                    'password': '123456'
                },
                'TABLE_MAPPING': {
                    'pat_table': 'lis_pat',
                    'result_table': 'lis_result'
                }
            }
            
            # 在程序所在目录创建配置文件
            config_path = os.path.join(self.program_dir, config_file)
            print(f"创建默认配置文件: {config_path}")
            
            # 创建配置文件
            self.config = configparser.ConfigParser()
            for section, options in default_config.items():
                self.config[section] = options
            
            # 确保目录存在
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            
            # 写入配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                self.config.write(f)
            
            print(f"已创建默认配置文件: {config_path}")
        else:
            print(f"成功加载配置文件: {config_path}")
            self.config = configparser.ConfigParser()
            
            # 检查是否存在加密的数据库配置
            encrypted_db_config_path = config_path + '.db.enc'
            if os.path.exists(encrypted_db_config_path):
                try:
                    # 弹出密码输入框
                    root = tk.Tk()
                    root.withdraw()  # 隐藏主窗口
                    
                    # 创建密码输入对话框
                    dialog = tk.Toplevel(root)
                    dialog.title("输入密码")
                    dialog.geometry("300x100")
                    dialog.configure(bg="#f0f0f0")
                    
                    # 居中显示
                    dialog.transient(root)
                    dialog.grab_set()
                    
                    # 创建密码输入框
                    tk.Label(dialog, text="请输入数据库配置解密密码:", bg="#f0f0f0").pack(pady=5)
                    password_var = tk.StringVar()
                    tk.Entry(dialog, textvariable=password_var, show="*").pack(pady=5)
                    
                    # 存储结果
                    result = {'password': None}
                    
                    def on_ok():
                        result['password'] = password_var.get()
                        dialog.destroy()
                    
                    def on_cancel():
                        dialog.destroy()
                    
                    # 添加按钮
                    button_frame = tk.Frame(dialog, bg="#f0f0f0")
                    button_frame.pack(pady=5)
                    tk.Button(button_frame, text="确定", command=on_ok, bg="#4CAF50", fg="white").pack(side=tk.LEFT, padx=5)
                    tk.Button(button_frame, text="取消", command=on_cancel, bg="#f44336", fg="white").pack(side=tk.LEFT, padx=5)
                    
                    # 等待对话框关闭
                    root.wait_window(dialog)
                    root.destroy()
                    
                    if result['password']:
                        # 简单的密码验证，只检查key_hash是否为固定值
                        if self.config['DATABASE']['key_hash'] != "simple_encrypted":
                            raise ValueError("密码错误")
                        
                        # 解密用户名和密码
                        username = decrypt_data(self.config['DATABASE']['username'])
                        password = decrypt_data(self.config['DATABASE']['password'])
                        
                        # 更新配置
                        self.config['DATABASE']['username'] = username
                        self.config['DATABASE']['password'] = password
                    else:
                        raise ValueError("未提供密码")
                        
                except Exception as e:
                    self.logger.error(f"解密数据库配置失败: {str(e)}")
                    raise
            else:
                # 使用未加密的配置
                self.config.read(config_path, encoding='utf-8')
            
            # 确保所有路径都是绝对路径
            if 'SYSTEM' in self.config:
                # 将相对路径转换为绝对路径
                for key in ['raw_dir', 'processed_dir', 'log_file', 'processed_files']:
                    if key in self.config['SYSTEM']:
                        # 如果路径不是绝对路径，则转换为相对于程序目录的绝对路径
                        path = self.config['SYSTEM'][key]
                        if not os.path.isabs(path):
                            self.config['SYSTEM'][key] = os.path.join(self.program_dir, path)
                            print(f"转换路径 {key}: {path} -> {self.config['SYSTEM'][key]}")
        
        if 'SYSTEM' not in self.config:
            raise KeyError(f"配置文件{config_path}缺少[SYSTEM]段")
        
        # 获取配置文件所在目录作为基础目录
        self.base_dir = os.path.dirname(config_path)
        
        # 系统配置
        self.host = self.config['SYSTEM']['host']
        self.port = int(self.config['SYSTEM']['port'])
        
        # 使用绝对路径
        self.raw_dir = Path(self.config['SYSTEM']['raw_dir'])
        self.processed_dir = Path(self.config['SYSTEM']['processed_dir'])
        self.processed_files = {}
        
        # 创建必要的目录
        self.raw_dir.mkdir(exist_ok=True)
        self.processed_dir.mkdir(exist_ok=True)
        
        # 打印目录路径信息
        print(f"监控的raw目录路径: {self.raw_dir}")
        print(f"写入result的目录路径: {self.processed_dir}")
        
        # 配置优化的日志系统
        log_file = self.config['SYSTEM']['log_file']
        
        # 创建自定义格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
        )
        
        # 轮转文件处理器
        file_handler = RotatingFileHandler(
            log_file, 
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setFormatter(formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        
        # 配置根日志器
        self.logger = logging.getLogger('LIS')
        self.logger.setLevel(logging.INFO)
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
        
        # 记录目录路径到日志
        self.logger.info(f"监控的raw目录路径: {self.raw_dir}")
        self.logger.info(f"写入result的目录路径: {self.processed_dir}")
        
        # 加载已处理文件记录
        self._load_processed_files()
        
        # 初始化数据库连接
        self._init_database()
        
        # 性能监控
        self.stats = {
            'messages_processed': 0,
            'database_operations': 0,
            'errors': 0,
            'start_time': time.time()
        }
        self.stats_lock = threading.Lock()
        
        # 线程池
        self.thread_pool = ThreadPoolExecutor(max_workers=20, thread_name_prefix="LIS-Worker")
        
        # 数据库字段长度限制（根据实际数据库表结构调整）
        self.pat_field_limits = {
            'wyh': 50,          # 文件号
            'yq': 20,           # 仪器
            'ybh': 20,          # 样本编号
            'yblx': 10,         # 样本类型
            'fb': 10,           # 分班
            'ksdh': 20,         # 科室代号
            'sjys': 20,         # 送检医生
            'yhdh': 20,         # 用户代号
            'brdh': 50,         # 病人代号
            'brxm': 30,         # 病人姓名
            'brxb': 2,          # 病人性别
            'brly': 20,         # 病人来源
            'ch': 10,           # 床号
            'nl': 10,           # 年龄
            'nldw': 5,          # 年龄单位
            'sqh': 30,          # 申请号
            'zd': 100,          # 诊断
            'bz': 100,          # 备注
            'bz1': 100,         # 备注1
            'zjf': 20,          # 主治医生
            'bq': 20,           # 病区
            'czybh': 20         # 操作员编号
        }
        
        self.result_field_limits = {
            'wyh': 50,          # 文件号
            'yq': 20,           # 仪器
            'ybh': 20,          # 样本编号
            'xmdh': 20,         # 项目代号
            'csjg': 50,         # 检验结果
            'instrid': 10,      # 仪器ID
            'od': 20,           # 操作者
            'operna': 20,       # 操作员
            'refs': 50,         # 参考范围
            'result_flag': 5,   # 异常标志
            'bz1': 100,         # 备注1
            'bz2': 100,         # 备注2
            'textresult': 100,  # 文本结果
            'num_result': 50    # 数值结果
        }

    def _get_effective_yq(self, message_object=None, raw_yq=""):
        """获取有效的YQ值，支持自定义配置
        
        Args:
            message_object: 消息对象（HL7Message或StandardMessage）
            raw_yq: 原始YQ值
            
        Returns:
            处理后的YQ值
        """
        # 如果有StandardMessage对象且包含设备信息
        if hasattr(message_object, 'device_info') and message_object.device_info:
            device_info = message_object.device_info
            yq_config = getattr(device_info, 'yq_config', {})
            
            if yq_config:
                yq_source = yq_config.get('yq_source', 'message')
                
                if yq_source == 'custom':
                    # 使用自定义YQ值
                    custom_yq = yq_config.get('yq_value', raw_yq)
                    self.logger.info(f"使用自定义YQ值: {custom_yq} (原始值: {raw_yq})")
                    return custom_yq
                elif yq_source == 'device_id':
                    # 使用设备ID作为YQ
                    prefix = yq_config.get('yq_prefix', '')
                    device_yq = f"{prefix}{device_info.device_id}"
                    self.logger.info(f"使用设备ID作为YQ值: {device_yq}")
                    return device_yq
                elif yq_source == 'mapping':
                    # 使用映射表
                    mapping = yq_config.get('yq_mapping', {})
                    mapped_yq = mapping.get(raw_yq, raw_yq)
                    if mapped_yq != raw_yq:
                        self.logger.info(f"YQ映射: {raw_yq} -> {mapped_yq}")
                    return mapped_yq
        
        # 如果是传统HL7Message对象，直接使用sending_facility
        if hasattr(message_object, 'sending_facility'):
            return message_object.sending_facility
        
        # 默认返回原始值
        return raw_yq

    def _validate_and_truncate_data(self, data_dict, field_limits):
        """验证并截断数据以防止数据库字段长度错误（智能特殊字符处理）"""
        validated_data = {}
        
        # 定义需要清理HL7分隔符的字段（主要是自由文本字段）
        text_fields_need_cleaning = {
            'brxm',  # 患者姓名
            'brly',  # 患者来源
            'zd',    # 诊断
            'bz',    # 备注
            'bz1',   # 备注1
            'textresult'  # 文本结果
        }
        
        for field, value in data_dict.items():
            if value is None:
                validated_data[field] = None
                continue
                
            # 转换为字符串并去除首尾空白
            str_value = str(value).strip()
            
            # 只对特定的自由文本字段处理HL7特殊字符
            if field in text_fields_need_cleaning:
                # 处理HL7分隔符，保护数据完整性
                str_value = str_value.replace('^', ' ')   # 组件分隔符 -> 空格
                str_value = str_value.replace('|', ' ')   # 字段分隔符 -> 空格  
                str_value = str_value.replace('~', ' ')   # 重复分隔符 -> 空格
                str_value = str_value.replace('\\', ' ')  # 转义字符 -> 空格
                # 清理多余的空格
                str_value = ' '.join(str_value.split())
                
                self.logger.debug(f"字段 {field} 清理特殊字符: '{value}' -> '{str_value}'")
            
            # 检查字段长度限制
            if field in field_limits:
                max_length = field_limits[field]
                if len(str_value) > max_length:
                    original_length = len(str_value)
                    str_value = str_value[:max_length]
                    self.logger.warning(f"字段 {field} 数据过长 ({original_length} > {max_length})，已截断: '{value}' -> '{str_value}'")
            
            validated_data[field] = str_value
            
        return validated_data

    def get_performance_stats(self):
        """获取性能统计信息"""
        with self.stats_lock:
            runtime = time.time() - self.stats['start_time']
            return {
                **self.stats,
                'runtime_seconds': runtime,
                'messages_per_second': self.stats['messages_processed'] / max(runtime, 1),
                'active_threads': threading.active_count(),
                'memory_usage_mb': self._get_memory_usage()
            }
    
    def _get_memory_usage(self):
        """获取内存使用量(MB)"""
        if not WINDOWS_FEATURES_AVAILABLE:
            return 0.0
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except:
            return 0.0
    
    def _init_database(self):
        """初始化数据库连接"""
        try:
            # 检查是否需要解密用户名和密码
            if 'key_hash' in self.config['DATABASE']:
                try:
                    # 解密用户名和密码
                    username = decrypt_data(self.config['DATABASE']['username'])
                    password = decrypt_data(self.config['DATABASE']['password'])
                    
                    # 更新配置
                    self.config['DATABASE']['username'] = username
                    self.config['DATABASE']['password'] = password
                    
                    self.logger.info("成功解密数据库配置")
                    
                except Exception as e:
                    self.logger.error(f"解密数据库配置失败: {str(e)}")
                    # 如果解密失败，显示错误提示
                    show_error_message(f"解密数据库配置失败: {str(e)}")
                    raise
            
            # 构建连接字符串
            self.conn_str = (
                f"DRIVER={{{self.config['DATABASE']['driver']}}};"
                f"SERVER={self.config['DATABASE']['server']};"
                f"DATABASE={self.config['DATABASE']['database']};"
                f"UID={self.config['DATABASE']['username']};"
                f"PWD={self.config['DATABASE']['password']}"
            )
            # 测试连接是否可用
            test_conn = pyodbc.connect(self.conn_str)
            test_conn.close()
            self.logger.info("数据库连接测试成功")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")
            raise

    def _get_db_connection(self):
        """获取新的数据库连接（优化版 - 添加连接池概念）"""
        try:
            conn = pyodbc.connect(self.conn_str)
            # 设置连接属性优化性能
            conn.autocommit = True  # 默认自动提交
            return conn
        except Exception as e:
            self.logger.error(f"获取数据库连接失败: {str(e)}")
            raise

    def _calculate_and_save_fpsa_tpsa_ratio(self, cursor, jyrq, yq, ybh, message_object=None):
        """计算并保存F-PSA/T-PSA比值"""
        try:
            # 获取有效的YQ值
            effective_yq = self._get_effective_yq(message_object, yq)
            
            # 查找F-PSA/T-PSA项目是否存在且为'未做'
            check_fpsa_tpsa_sql = f"""
            SELECT COUNT(1) FROM {self.config['TABLE_MAPPING']['result_table']} 
            WHERE jyrq=? AND yq=? AND ybh=? AND xmdh='F-PSA/T-PSA' AND csjg='未做'
            """
            cursor.execute(check_fpsa_tpsa_sql, (jyrq, effective_yq, ybh))
            fpsa_tpsa_exists = cursor.fetchone()[0]
            
            if fpsa_tpsa_exists:
                self.logger.info(f"发现F-PSA/T-PSA项目为'未做'，开始计算比值")
                
                # 查找F-PSA结果
                fpsa_sql = f"""
                SELECT csjg FROM {self.config['TABLE_MAPPING']['result_table']} 
                WHERE jyrq=? AND yq=? AND ybh=? AND xmdh='F-PSA'
                """
                cursor.execute(fpsa_sql, (jyrq, effective_yq, ybh))
                fpsa_row = cursor.fetchone()
                
                # 查找PSA结果（先尝试PSA，再尝试t-PSA）
                psa_sql = f"""
                SELECT csjg FROM {self.config['TABLE_MAPPING']['result_table']} 
                WHERE jyrq=? AND yq=? AND ybh=? AND xmdh='PSA'
                """
                cursor.execute(psa_sql, (jyrq, effective_yq, ybh))
                psa_row = cursor.fetchone()
                
                # 如果没找到PSA，尝试查找t-PSA
                if not psa_row:
                    tpsa_sql = f"""
                    SELECT csjg FROM {self.config['TABLE_MAPPING']['result_table']} 
                    WHERE jyrq=? AND yq=? AND ybh=? AND xmdh='t-PSA'
                    """
                    cursor.execute(tpsa_sql, (jyrq, effective_yq, ybh))
                    psa_row = cursor.fetchone()
                    if psa_row:
                        self.logger.info(f"使用t-PSA作为PSA值进行计算")
                
                if fpsa_row and psa_row:
                    try:
                        fpsa_value = float(fpsa_row[0])
                        psa_value = float(psa_row[0])
                        
                        self.logger.info(f"F-PSA值: {fpsa_value}, PSA值: {psa_value}")
                        
                        if psa_value != 0:
                            # 计算比值，保留两位小数
                            ratio = fpsa_value / psa_value
                            ratio_str = f"{ratio:.2f}"
                            
                            self.logger.info(f"计算得到F-PSA/T-PSA比值: {ratio_str}")
                            
                            # 更新F-PSA/T-PSA的结果
                            update_ratio_sql = f"""
                            UPDATE {self.config['TABLE_MAPPING']['result_table']} 
                            SET csjg=?, num_result=? 
                            WHERE jyrq=? AND yq=? AND ybh=? AND xmdh='F-PSA/T-PSA'
                            """
                            cursor.execute(update_ratio_sql, (ratio_str, ratio_str, jyrq, effective_yq, ybh))
                            
                            self.logger.info(f"已更新F-PSA/T-PSA比值结果: {ratio_str}")
                        else:
                            self.logger.warning("PSA值为0，无法计算F-PSA/T-PSA比值")
                            
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"F-PSA或PSA值无法转换为数值，跳过比值计算: {e}")
                        self.logger.warning(f"F-PSA值: {fpsa_row[0] if fpsa_row else 'None'}, PSA值: {psa_row[0] if psa_row else 'None'}")
                else:
                    missing = []
                    if not fpsa_row:
                        missing.append("F-PSA")
                    if not psa_row:
                        missing.append("PSA或t-PSA")
                    self.logger.warning(f"缺少计算F-PSA/T-PSA比值所需的项目: {', '.join(missing)}")
            else:
                self.logger.debug("未发现需要计算的F-PSA/T-PSA项目")
                
        except Exception as e:
            self.logger.error(f"计算F-PSA/T-PSA比值时出错: {str(e)}")

    def _save_to_database(self, hl7_message: HL7Message):
        """保存数据到数据库（优化版 - 支持brdh查ybh并按xmdh更新结果）"""
        conn = None
        cursor = None
        start_time = time.time()
        
        try:
            # 为每个事务创建新的连接
            conn = self._get_db_connection()
            conn.autocommit = False  # 事务模式
            cursor = conn.cursor()
            
            # 保存每个样本（每个Order）到lis_pat表
            pat_sql = f"""
            INSERT INTO {self.config['TABLE_MAPPING']['pat_table']} (
                wyh, jyrq, yq, ybh, yblx, fb, ksdh, sjys, yhdh,
                brdh, brxm, brxb, brly, ch, nl, nldw, sqh, sqsj, dysj,
                dybz, jgbz, bbbz, zd, bz, bz1, zjf, cyrq, bq, czybh
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            for order in hl7_message.orders:
                # jyrq用OBR第7列 Observation Date/Time
                jyrq_raw = order.order_time
                if jyrq_raw and len(jyrq_raw) >= 8:
                    try:
                        jyrq_fmt = datetime.strptime(jyrq_raw[:14], '%Y%m%d%H%M%S') if len(jyrq_raw) >= 14 else datetime.strptime(jyrq_raw[:8], '%Y%m%d')
                        jyrq = jyrq_fmt.strftime('%Y-%m-%d 00:00:00.000')
                    except Exception:
                        jyrq = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
                else:
                    jyrq = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
                
                # 获取有效的YQ值
                effective_yq = self._get_effective_yq(hl7_message, hl7_message.sending_facility)
                
                # 如果有brdh，先通过brdh、yq、jyrq查询是否存在记录
                ybh = order.sample_id
                if order.order_id:  # 如果有brdh
                    find_pat_sql = f"SELECT ybh FROM {self.config['TABLE_MAPPING']['pat_table']} WHERE brdh=? AND yq=? AND jyrq=?"
                    cursor.execute(find_pat_sql, (order.order_id, effective_yq, jyrq))
                    row = cursor.fetchone()
                    if row and row[0]:
                        ybh = row[0]
                        self.logger.info(f"通过brdh={order.order_id}查到ybh={ybh}，患者信息已存在，不再插入")
                        continue
                
                # 判断是否已存在相同jyrq、yq、ybh的数据
                check_pat_sql = f"SELECT COUNT(1) FROM {self.config['TABLE_MAPPING']['pat_table']} WHERE jyrq=? AND yq=? AND ybh=?"
                cursor.execute(check_pat_sql, (jyrq, effective_yq, ybh))
                exists = cursor.fetchone()[0]
                if exists:
                    self.logger.info(f"已存在jyrq={jyrq}, yq={effective_yq}, ybh={ybh}的患者信息，不再插入。")
                    continue
                
                # 准备患者数据字典进行验证
                pat_data_dict = {
                    'wyh': hl7_message.message_control_id,
                    'yq': effective_yq,
                    'ybh': ybh,
                    'yblx': '',
                    'fb': '',
                    'ksdh': hl7_message.patient.department,
                    'sjys': '',
                    'yhdh': '',
                    'brdh': order.order_id,
                    'brxm': hl7_message.patient.name,
                    'brxb': hl7_message.patient.sex,
                    'brly': '',
                    'ch': hl7_message.patient.bed_no,
                    'nl': '',
                    'nldw': '1',
                    'sqh': '',
                    'zd': '',
                    'bz': '',
                    'bz1': '',
                    'zjf': '',
                    'bq': '',
                    'czybh': ''
                }
                
                # 验证和截断数据
                validated_pat_data = self._validate_and_truncate_data(pat_data_dict, self.pat_field_limits)
                
                pat_data = (
                    validated_pat_data['wyh'],      # wyh
                    jyrq,                           # jyrq
                    validated_pat_data['yq'],       # yq
                    validated_pat_data['ybh'],      # ybh
                    validated_pat_data['yblx'],     # yblx
                    validated_pat_data['fb'],       # fb
                    validated_pat_data['ksdh'],     # ksdh
                    validated_pat_data['sjys'],     # sjys
                    validated_pat_data['yhdh'],     # yhdh
                    validated_pat_data['brdh'],     # brdh
                    validated_pat_data['brxm'],     # brxm
                    validated_pat_data['brxb'],     # brxb
                    validated_pat_data['brly'],     # brly
                    validated_pat_data['ch'],       # ch
                    validated_pat_data['nl'],       # nl
                    validated_pat_data['nldw'],     # nldw
                    validated_pat_data['sqh'],      # sqh
                    jyrq,                           # sqsj
                    jyrq,                           # dysj
                    'N',                            # dybz
                    'N',                            # jgbz
                    'N',                            # bbbz
                    validated_pat_data['zd'],       # zd
                    validated_pat_data['bz'],       # bz
                    validated_pat_data['bz1'],      # bz1
                    validated_pat_data['zjf'],      # zjf
                    None,                           # cyrq
                    validated_pat_data['bq'],       # bq
                    validated_pat_data['czybh']     # czybh
                )
                self.logger.info("===== 患者信息插入语句 =====")
                self.logger.info(f"SQL: {pat_sql}")
                self.logger.info(f"参数详情:")
                self.logger.info(f"  wyh (文件号): '{hl7_message.message_control_id}'")
                self.logger.info(f"  jyrq (检验日期): '{jyrq}'")
                self.logger.info(f"  yq (仪器): '{effective_yq}' (原始值: '{hl7_message.sending_facility}')")
                self.logger.info(f"  ybh (样本编号): '{ybh}'")
                self.logger.info(f"  yblx (样本类型): ''")
                self.logger.info(f"  fb (分班): ''")
                self.logger.info(f"  ksdh (科室代号): '{hl7_message.patient.department}'")
                self.logger.info(f"  sjys (送检医生): ''")
                self.logger.info(f"  yhdh (用户代号): ''")
                self.logger.info(f"  brdh (病人代号): '{order.order_id}'")
                self.logger.info(f"  brxm (病人姓名): '{hl7_message.patient.name}'")
                self.logger.info(f"  brxb (病人性别): '{hl7_message.patient.sex}'")
                self.logger.info(f"  brly (病人来源): ''")
                self.logger.info(f"  ch (床号): '{hl7_message.patient.bed_no}'")
                self.logger.info(f"  nl (年龄): ''")
                self.logger.info(f"  nldw (年龄单位): '1'")
                self.logger.info(f"  sqh (申请号): ''")
                self.logger.info(f"  sqsj (申请时间): '{jyrq}'")
                self.logger.info(f"  dysj (打印时间): '{jyrq}'")
                self.logger.info(f"  dybz (打印标志): 'N'")
                self.logger.info(f"  jgbz (结果标志): 'N'")
                self.logger.info(f"  bbbz (报表标志): 'N'")
                self.logger.info("==========================")
                cursor.execute(pat_sql, pat_data)
                self.logger.info("患者信息插入成功")
            
            # 保存检验结果到lis_result表
            processed_samples = set()  # 记录已处理的样本，避免重复计算比值
            
            for order in hl7_message.orders:
                # jyrq用OBR第7列 Observation Date/Time
                jyrq_raw = order.order_time
                if jyrq_raw and len(jyrq_raw) >= 8:
                    try:
                        jyrq_fmt = datetime.strptime(jyrq_raw[:14], '%Y%m%d%H%M%S') if len(jyrq_raw) >= 14 else datetime.strptime(jyrq_raw[:8], '%Y%m%d')
                        jyrq = jyrq_fmt.strftime('%Y-%m-%d 00:00:00.000')
                    except Exception:
                        jyrq = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
                else:
                    jyrq = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
                
                # 获取有效的YQ值
                effective_yq = self._get_effective_yq(hl7_message, hl7_message.sending_facility)
                
                # 如果有brdh，通过brdh和yq查询ybh
                ybh = order.sample_id
                if order.order_id:  # 如果有brdh
                    find_ybh_sql = f"SELECT ybh FROM {self.config['TABLE_MAPPING']['pat_table']} WHERE brdh=? AND yq=? AND jyrq=?"
                    cursor.execute(find_ybh_sql, (order.order_id, effective_yq, jyrq))
                    row = cursor.fetchone()
                    if row and row[0]:
                        ybh = row[0]
                        self.logger.info(f"通过brdh={order.order_id}查到ybh={ybh}，用于结果入库")
                
                for result in order.results:
                    # 查找xmdh
                    select_sql = "SELECT xmdh FROM xm_inter WHERE yq=? AND tdh=?"
                    cursor.execute(select_sql, (effective_yq, result.test_name))
                    row = cursor.fetchone()
                    xmdh = row[0] if row else result.test_name
                    if not row:
                        self.logger.warning(f"未在xm_inter表中找到yq={effective_yq}, tdh={result.test_name}的xmdh，使用原值。")
                    
                    # 查询xm_info表获取小数位数
                    xsws_sql = "SELECT xsws FROM xm_info WHERE yq=? AND xmdh=?"
                    cursor.execute(xsws_sql, (effective_yq, xmdh))
                    xsws_row = cursor.fetchone()
                    xsws = xsws_row[0] if xsws_row else 0  # 如果找不到，默认0位小数
                    
                    # 检查结果值是否为空，如果为空则跳过该项目的更新
                    if not result.value or str(result.value).strip() == '':
                        self.logger.info(f"项目 {xmdh} 的结果值为空，跳过更新以保持原始结果")
                        continue
                    
                    # 根据小数位数处理结果值，支持特殊格式（如 >1000.000, <0.001）
                    result_str = str(result.value)  # 用于csjg字段
                    num_result_str = None  # 用于num_result字段，只存储纯数值

                    # 检查是否包含比较符号或特殊字符
                    symbol_prefix = ""
                    numeric_part = result_str

                    # 提取符号前缀和数值部分
                    for symbol in ['>=', '<=', '>', '<', '±', '+']:
                        if result_str.startswith(symbol):
                            symbol_prefix = symbol
                            numeric_part = result_str[len(symbol):].strip()
                            break

                    # 尝试处理数值部分
                    try:
                        value = float(numeric_part)

                        if xsws == -1:
                            # 小数位数为-1时，保持原始数值不做格式化处理
                            formatted_numeric = str(value)
                        else:
                            rounded_value = round(value, xsws)  # 四舍五入到指定小数位
                            # 使用格式化字符串保留指定小数位数，包括末尾的0
                            formatted_numeric = f"{rounded_value:.{xsws}f}"

                        # 重新组合符号和格式化后的数值
                        if symbol_prefix:
                            result_str = symbol_prefix + formatted_numeric
                            num_result_str = None  # 包含符号的值，num_result字段存储NULL
                            self.logger.info(f"特殊格式数值处理: 原始值={result.value}, 符号='{symbol_prefix}', 数值部分={numeric_part}, 小数位数={xsws}, 格式化后='{formatted_numeric}', 最终csjg='{result_str}', num_result=NULL")
                        else:
                            result_str = formatted_numeric
                            num_result_str = formatted_numeric  # 纯数值，两个字段都存储
                            self.logger.info(f"纯数值处理: 原始值={result.value}, 转换值={value}, 小数位数={xsws}, csjg='{result_str}', num_result='{num_result_str}'")

                    except (ValueError, TypeError):
                        # 无法转换为数值，保持原样
                        result_str = str(result.value)
                        num_result_str = None
                        self.logger.info(f"非数值结果: 原始值={result.value}, csjg='{result_str}', num_result=NULL")
                    
                    # 检查是否已存在相同记录
                    check_result_sql = f"SELECT COUNT(1) FROM {self.config['TABLE_MAPPING']['result_table']} WHERE jyrq=? AND yq=? AND ybh=? AND xmdh=?"
                    cursor.execute(check_result_sql, (jyrq, effective_yq, ybh, xmdh))
                    exists_result = cursor.fetchone()[0]
                    
                    if exists_result:
                        update_sql = f"UPDATE {self.config['TABLE_MAPPING']['result_table']} SET csjg=?, result_flag=?, refs=?, num_result=? WHERE jyrq=? AND yq=? AND ybh=? AND xmdh=?"
                        # 准备更新数据字典进行验证
                        update_data_dict = {
                            'csjg': result_str,
                            'result_flag': result.abnormal_flag,
                            'refs': result.reference_range,
                            'num_result': num_result_str,  # 使用专门处理的数值字段
                            'yq': effective_yq,
                            'ybh': ybh,
                            'xmdh': xmdh
                        }

                        # 验证和截断数据
                        validated_update_data = self._validate_and_truncate_data(update_data_dict, self.result_field_limits)

                        update_data = (
                            validated_update_data['csjg'],
                            validated_update_data['result_flag'],
                            validated_update_data['refs'],
                            validated_update_data['num_result'],
                            jyrq,
                            validated_update_data['yq'],
                            validated_update_data['ybh'],
                            validated_update_data['xmdh']
                        )
                        self.logger.info("===== 数据库更新语句 =====")
                        self.logger.info(f"SQL: {update_sql}")
                        self.logger.info(f"参数详情:")
                        self.logger.info(f"  csjg (检验结果): '{result_str}'")
                        self.logger.info(f"  result_flag (异常标志): '{result.abnormal_flag}'")
                        self.logger.info(f"  refs (参考范围): '{result.reference_range}'")
                        self.logger.info(f"  num_result (数值结果): '{num_result_str}'")
                        self.logger.info(f"  WHERE条件:")
                        self.logger.info(f"    jyrq (检验日期): '{jyrq}'")
                        self.logger.info(f"    yq (仪器): '{hl7_message.sending_facility}'")
                        self.logger.info(f"    ybh (样本编号): '{ybh}'")
                        self.logger.info(f"    xmdh (项目代号): '{xmdh}'")
                        self.logger.info("========================")
                        cursor.execute(update_sql, update_data)
                        self.logger.info("检验结果更新成功")
                    else:
                        result_sql = f"""
                        INSERT INTO {self.config['TABLE_MAPPING']['result_table']} (
                            wyh, jyrq, yq, ybh, xmdh, csjg,instrid,od, operna,refs,
                            result_flag, bz1, bz2, textresult,num_result
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        # 准备检验结果数据字典进行验证
                        result_data_dict = {
                            'wyh': hl7_message.message_control_id,
                            'yq': effective_yq,
                            'ybh': ybh,
                            'xmdh': xmdh,
                            'csjg': result_str,
                            'instrid': '0',
                            'od': '',
                            'operna': '仪器',
                            'refs': result.reference_range,
                            'result_flag': result.abnormal_flag,
                            'bz1': '',
                            'bz2': '',
                            'textresult': '',
                            'num_result': num_result_str  # 使用专门处理的数值字段
                        }
                        
                        # 验证和截断数据
                        validated_result_data = self._validate_and_truncate_data(result_data_dict, self.result_field_limits)
                        
                        result_data = (
                            validated_result_data['wyh'],       # wyh
                            jyrq,                               # jyrq
                            validated_result_data['yq'],        # yq
                            validated_result_data['ybh'],       # ybh
                            validated_result_data['xmdh'],      # xmdh
                            validated_result_data['csjg'],      # csjg
                            validated_result_data['instrid'],   # instrid
                            validated_result_data['od'],        # od
                            validated_result_data['operna'],    # operna
                            validated_result_data['refs'],      # refs
                            validated_result_data['result_flag'], # result_flag
                            validated_result_data['bz1'],       # bz1
                            validated_result_data['bz2'],       # bz2
                            validated_result_data['textresult'], # textresult
                            validated_result_data['num_result'] # num_result
                        )
                        self.logger.info("===== 数据库插入语句 =====")
                        self.logger.info(f"SQL: {result_sql}")
                        self.logger.info(f"参数详情:")
                        self.logger.info(f"  wyh (文件号): '{hl7_message.message_control_id}'")
                        self.logger.info(f"  jyrq (检验日期): '{jyrq}'")
                        self.logger.info(f"  yq (仪器): '{effective_yq}' (原始值: '{hl7_message.sending_facility}')")
                        self.logger.info(f"  ybh (样本编号): '{ybh}'")
                        self.logger.info(f"  xmdh (项目代号): '{xmdh}'")
                        self.logger.info(f"  csjg (检验结果): '{result_str}'")
                        self.logger.info(f"  instrid (仪器ID): '0'")
                        self.logger.info(f"  od (操作者): ''")
                        self.logger.info(f"  operna (操作员): '仪器'")
                        self.logger.info(f"  refs (参考范围): '{result.reference_range}'")
                        self.logger.info(f"  result_flag (异常标志): '{result.abnormal_flag}'")
                        self.logger.info(f"  bz1: ''")
                        self.logger.info(f"  bz2: ''")
                        self.logger.info(f"  textresult: ''")
                        self.logger.info(f"  num_result (数值结果): '{num_result_str}'")
                        self.logger.info("========================")
                        cursor.execute(result_sql, result_data)
                        self.logger.info("检验结果插入成功")
                
                # 记录样本标识符并计算F-PSA/T-PSA比值（每个样本只计算一次）
                sample_key = (jyrq, effective_yq, ybh)
                if sample_key not in processed_samples:
                    processed_samples.add(sample_key)
                    self._calculate_and_save_fpsa_tpsa_ratio(cursor, jyrq, effective_yq, ybh, hl7_message)
            
            # 确保提交事务
            conn.commit()
            end_time = time.time()
            self.logger.info(f"数据库事务已提交，耗时: {end_time - start_time:.3f}秒")
            
            # 更新统计
            with self.stats_lock:
                self.stats['database_operations'] += 1
            
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"保存数据到数据库时出错: {str(e)}")
            
            # 更新错误统计
            with self.stats_lock:
                self.stats['errors'] += 1
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def _load_processed_files(self):
        """加载已处理文件记录，记录每个文件的行数"""
        log_file = self.config['SYSTEM']['processed_files']
        if Path(log_file).exists():
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    self.processed_files = json.load(f)
            except json.JSONDecodeError:
                self.processed_files = {}
        else:
            self.processed_files = {}

    def _save_processed_files(self):
        """保存已处理文件记录，包含行数信息"""
        log_file = self.config['SYSTEM']['processed_files']
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(self.processed_files, f, ensure_ascii=False, indent=2)

    def _get_file_line_count(self, file_path):
        """获取文件的行数"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return sum(1 for _ in f)
        except Exception as e:
            self.logger.error(f"获取文件行数失败 {file_path}: {str(e)}")
            return 0

    def _get_today_file(self):
        """获取今天的文件名"""
        return Path(self.raw_dir) / f"{datetime.now().strftime('%y%m%d')}.txt"

    def parse_hl7_message(self, message: str) -> HL7Message:
        """解析HL7消息为结构化数据（所有fields[x]访问加越界保护）"""
        try:
            # 打印原始消息用于调试
            self.logger.info("开始解析消息:")
            self.logger.info("-" * 50)
            self.logger.info(message)
            self.logger.info("-" * 50)
            
            # 清理消息：移除多余的空格和换行，确保每个段都在一行
            # 先统一处理不同的换行符格式
            normalized_message = message.replace('\r\n', '\r').replace('\n', '\r')
            
            cleaned_message = []
            for line in normalized_message.split('\r'):
                line = line.strip()
                if line:  # 只忽略空行
                    cleaned_message.append(line)
                    self.logger.info(f"处理段: {line}")  # 打印每个处理后的段
            
            # 按行分割消息
            segments = cleaned_message
            self.logger.info(f"总共找到 {len(segments)} 个段")
            
            # 解析MSH段
            msh = segments[0].split('|')
            message_type = msh[8] if len(msh) > 8 else ''
            message_control_id = msh[9] if len(msh) > 9 else ''
            sending_facility = msh[3] if len(msh) > 3 else ''
            receiving_facility = msh[4] if len(msh) > 4 else ''
            message_time = msh[6] if len(msh) > 6 else ''
            
            # 解析PID段
            pid = None
            for segment in segments:
                if segment.startswith('PID|'):
                    pid = segment.split('|')
                    self.logger.info(f"找到PID段: {pid}")  # 打印找到的PID段
                    break
            
            if not pid:
                self.logger.warning("未找到PID段")
                pid = []
            
            patient = Patient(
                patient_id=pid[2] if len(pid) > 2 else "",
                sex=pid[8] if len(pid) > 8 else "",
                age=pid[31] if len(pid) > 31 else "",
                name=pid[5] if len(pid) > 5 else "",
                birth_date=pid[7] if len(pid) > 7 else "",
                department=pid[10] if len(pid) > 10 else "",
                bed_no=pid[11] if len(pid) > 11 else ""
            )
            
            # 解析OBR和OBX段
            orders = []
            current_order = None
            
            for segment in segments:
                fields = segment.split('|')
                segment_type = fields[0] if len(fields) > 0 else ''
                
                if segment_type == 'OBR':
                    if current_order:
                        orders.append(current_order)
                    
                    current_order = Order(
                        order_id=fields[2] if len(fields) > 2 else '',
                        sample_id=fields[3] if len(fields) > 3 else '',  # 样本编号
                        test_type=fields[4] if len(fields) > 4 else '',
                        order_time=fields[7] if len(fields) > 7 else '',  # OBR第7列 Observation Date/Time
                        report_time=fields[22] if len(fields) > 22 else '',
                        priority=fields[5] if len(fields) > 5 else "",
                        results=[]
                    )
                    
                elif segment_type == 'OBX' and current_order:
                    # 添加调试信息
                    self.logger.info(f"OBX段解析 - fields: {fields}")
                    self.logger.info(f"fields[4] = '{fields[4] if len(fields) > 4 else 'INDEX_OUT_OF_RANGE'}'")

                    # 安全地解析检验结果值，支持包含比较符号的值（如 >1000.000, <0.001）
                    raw_value = fields[4] if len(fields) > 4 else ''

                    # 保持原始值用于存储，但尝试提取数值用于计算
                    parsed_value = raw_value  # 默认保持原始字符串

                    if raw_value and raw_value.strip():
                        # 尝试直接转换为数值
                        try:
                            parsed_value = float(raw_value.strip())
                            self.logger.info(f"数值解析成功: '{raw_value}' -> {parsed_value}")
                        except (ValueError, TypeError):
                            # 如果包含比较符号，保持原始字符串格式
                            if any(symbol in raw_value for symbol in ['>', '<', '>=', '<=', '±', '+']):
                                parsed_value = raw_value.strip()
                                self.logger.info(f"检测到特殊格式结果值，保持原样: '{raw_value}' -> '{parsed_value}'")
                            else:
                                # 其他无法解析的情况，记录警告但保持原始值
                                parsed_value = raw_value.strip()
                                self.logger.warning(f"无法解析检验结果值，保持原样: '{raw_value}' -> '{parsed_value}'")
                    else:
                        # 空值情况
                        parsed_value = ''
                        self.logger.info(f"检验结果值为空: '{raw_value}' -> '{parsed_value}'")
                    
                    test_result = TestResult(
                        test_name=fields[3].split('^')[0] if len(fields) > 3 else '',
                        value=parsed_value,
                        unit=fields[5] if len(fields) > 5 else '',
                        reference_range=fields[6] if len(fields) > 6 else '',
                        abnormal_flag=fields[7] if len(fields) > 7 else '',
                        observation_time=fields[14] if len(fields) > 14 else '',
                        instrument_id=fields[18] if len(fields) > 18 else '',
                        reagent_lot=fields[19] if len(fields) > 19 else '',
                        reagent_expiry=fields[20] if len(fields) > 20 else '',
                        control_id=fields[21] if len(fields) > 21 else ''
                    )
                    current_order.results.append(test_result)
            
            if current_order:
                orders.append(current_order)
            
            # 打印解析结果用于调试
            self.logger.info("解析结果:")
            self.logger.info(f"消息类型: {message_type}")
            self.logger.info(f"患者信息: {patient}")
            self.logger.info(f"医嘱数量: {len(orders)}")
            
            return HL7Message(
                message_type=message_type,
                message_control_id=message_control_id,
                sending_facility=sending_facility,
                receiving_facility=receiving_facility,
                message_time=message_time,
                patient=patient,
                orders=orders
            )
            
        except Exception as e:
            self.logger.error(f"解析HL7消息时出错: {str(e)}")
            raise

    def format_message(self, hl7_message: HL7Message) -> str:
        """将HL7消息格式化为可读文本"""
        output = []
        
        for order in hl7_message.orders:
            # 添加样本基本信息
            output.append(f"样本ID：{order.sample_id} | 检验类型：{hl7_message.sending_facility}")
            
            # 添加检验结果
            for result in order.results:
                # 格式化结果值、单位和参考范围
                result_str = f"{result.test_name}: {result.value} {result.unit}（参考：{result.reference_range}） 异常标志：{result.abnormal_flag}"
                output.append(result_str)
            
            # 每个样本之间添加空行
            output.append("")
        
        return "\n".join(output)

    def _handle_client(self, client_socket, address):
        """处理客户端连接，支持结果上传和QRY^Q02样本项目查询（优化版）"""
        self.logger.info(f"接受来自 {address} 的连接")
        buffer = b''
        MAX_BUFFER_SIZE = 1024 * 1024  # 1MB缓冲区限制
        client_socket.settimeout(30)  # 30秒超时
        client_socket.setblocking(True)  # 确保socket是阻塞模式
        client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)  # 禁用Nagle算法
        client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)  # 启用keep-alive

        try:
            while True:
                try:
                    # 接收数据
                    data = client_socket.recv(4096)
                    if not data:
                        # 收到空数据，这是客户端的心跳包，保持连接
                        self.logger.debug(f"收到来自 {address} 的心跳包")
                        continue

                    buffer += data
                    self.logger.info(f"收到数据长度: {len(data)} 字节")
                    
                    # 缓冲区大小控制
                    if len(buffer) > MAX_BUFFER_SIZE:
                        self.logger.warning(f"缓冲区过大({len(buffer)} 字节)，截断到一半")
                        buffer = buffer[-MAX_BUFFER_SIZE//2:]

                    # 处理缓冲区中的所有完整消息
                    while buffer:
                        # 查找消息开始标记 (单字符)
                        start_index = buffer.find(b'\x0b')  # VT (Vertical Tab)
                        if start_index == -1:
                            # 没有找到开始标记，清空缓冲区
                            buffer = b''
                            break

                        # 查找消息结束标记 (双字符 + CR)
                        end_index = buffer.find(b'\x1c\x0d', start_index)  # FS (File Separator) + CR
                        if end_index == -1:
                            # 没有找到结束标记，保留数据等待下次接收
                            buffer = buffer[start_index:]
                            break

                        # 提取完整消息 (包含开始和结束标记)
                        message = buffer[start_index:end_index + 2]  # +2 包含结束标记的两个字符
                        buffer = buffer[end_index + 2:]  # 更新缓冲区，跳过结束标记和CR

                        if message.startswith(b'\x0b'):
                            message = message[1:]  # 移除VT
                            try:
                                # 解码消息
                                decoded_message = message.decode('utf-8')
                                self.logger.info(f"解码消息: {decoded_message[:100]}...")  # 只打印前100个字符

                                # 判断消息类型
                                if 'ORU^R01' in decoded_message:
                                    # 保存ORU^R01原始报文到raw目录
                                    try:
                                        # 使用绝对路径
                                        raw_file = Path(self.raw_dir) / f'{datetime.now():%y%m%d}.txt'
                                        with open(raw_file, 'ab') as f:
                                            f.write(b'\x0b' + message + b'\x1c\x0d')  # 使用正确的结束标记
                                        self.logger.info(f"ORU^R01原始报文已保存到: {raw_file}")
                                        
                                        # 立即解析消息并保存到数据库
                                        parsed_message = self.parse_hl7_message(decoded_message)
                                        self._save_to_database(parsed_message)
                                        self.logger.info("ORU^R01消息已解析并保存到数据库")
                                        
                                        # 更新消息处理统计
                                        with self.stats_lock:
                                            self.stats['messages_processed'] += 1
                                        
                                        # 格式化解析结果并保存
                                        formatted_output = self.format_message(parsed_message)
                                        output_filename = f"decoded_{datetime.now():%y%m%d}.txt"
                                        output_path = Path(self.processed_dir) / output_filename
                                        with open(output_path, 'a', encoding='utf-8') as f:
                                            if formatted_output.strip():
                                                f.write(formatted_output)
                                                f.write("\n" + "="*80 + "\n\n")
                                        self.logger.info(f"解析结果已追加至: {output_path}")
                                        
                                        # 更新processed_files记录
                                        file_key = str(raw_file)
                                        current_lines = self._get_file_line_count(raw_file)
                                        self.processed_files[file_key] = {
                                            "processed_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                            "output_file": str(output_path),
                                            "line_count": current_lines
                                        }
                                        self._save_processed_files()
                                        
                                    except Exception as e:
                                        self.logger.error(f"处理ORU^R01消息时出错: {str(e)}")
                                    
                                    # 发送ACK应答
                                    ack = self._handle_oru_ack(decoded_message)
                                    client_socket.sendall(ack)  # 使用sendall确保完整发送
                                    self.logger.info("已应答ORU^R01样本结果上传请求")

                                elif 'QRY^Q02' in decoded_message:
                                    # QRY^Q02等交互日志保存到log目录
                                    try:
                                        log_dir = Path(self.program_dir) / 'log'
                                        log_dir.mkdir(exist_ok=True)
                                        log_file = log_dir / f'{datetime.now():%y%m%d}.txt'
                                        with open(log_file, 'a', encoding='utf-8') as f:
                                            f.write(decoded_message + '\n')
                                        self.logger.info(f"QRY^Q02交互日志已保存到: {log_file}")
                                    except Exception as e:
                                        self.logger.error(f"保存QRY^Q02交互日志到{log_file}失败: {e}")
                                    response = self._handle_query(decoded_message)
                                    client_socket.sendall(response)  # 使用sendall确保完整发送
                                    self.logger.info("已应答QRY^Q02样本项目查询请求")

                            except UnicodeDecodeError:
                                self.logger.error("消息解码错误")
                                # 发送AR应答
                                try:
                                    client_socket.sendall(self._build_ack_r01('', '', '', '', msa_code='AR', err_msg='消息解码错误', err_code='206'))
                                except Exception:
                                    pass
                            except Exception as e:
                                self.logger.error(f"处理消息时出错: {str(e)}")
                                # 发送AR应答
                                try:
                                    client_socket.sendall(self._build_ack_r01('', '', '', '', msa_code='AR', err_msg=str(e), err_code='206'))
                                except Exception:
                                    pass

                except socket.timeout:
                    continue
                except ConnectionResetError:
                    self.logger.info(f"客户端 {address} 异常断开连接 (ConnectionResetError)")
                    break
                except ConnectionAbortedError:
                    self.logger.info(f"客户端 {address} 异常断开连接 (ConnectionAbortedError)")
                    break
                except Exception as e:
                    self.logger.error(f"接收数据时出错: {str(e)}")
                    break

        except Exception as e:
            self.logger.error(f"处理客户端 {address} 时出错: {str(e)}")
        finally:
            try:
                # 尝试获取socket状态
                client_socket.getpeername()
                self.logger.info(f"客户端 {address} 正常关闭连接")
            except:
                self.logger.info(f"客户端 {address} 异常关闭连接")
            finally:
                client_socket.close()
                self.logger.info(f"关闭与 {address} 的连接")

    def _handle_query(self, message: str) -> bytes:
        """处理QRY^Q02样本项目查询请求，返回DSR^Q03或ACK^Q03应答（含详细调试日志）"""
        conn = None
        cursor = None
        try:
            self.logger.info("收到QRY^Q02请求原始内容：\n" + message)
            # 解析QRY^Q02
            lines = [line for line in message.split('\r') if line.strip()]
            msh = lines[0].split('|')
            analyzer_id = msh[2]
            raw_yq = msh[3]
            lis_id = msh[4]
            msg_time = msh[6]
            msg_id = msh[9]
            
            # 对于查询消息，使用默认YQ处理（无设备信息上下文）
            yq = raw_yq
            serial_number = msh[11] if len(msh) > 11 else ''
            # QRD段
            qrd = next((l for l in lines if l.startswith('QRD|')), None)
            if not qrd:
                self.logger.error("未找到QRD段，返回AR错误")
                return self._build_ack_q03(analyzer_id, lis_id, msg_time, msg_id, msa_code='AR', err_msg='无QRD段', err_code='206')
            qrd_fields = qrd.split('|')
            # 根据用户反馈，查询模式在第2个字段 QRD|20250617190338|BC|D|1|||RD|285025977||N003^4|N|T|
            query_mode = qrd_fields[2] if len(qrd_fields) > 2 else ''    # 查询模式 (字段2)
            sample_no = qrd_fields[8] if len(qrd_fields) > 8 else ''     # 样本编号/条码号 (字段8)
            rack_pos = qrd_fields[11] if len(qrd_fields) > 11 else ''    # N (字段11)
            dilute = qrd_fields[12] if len(qrd_fields) > 12 else ''      # T (字段12)
            self.logger.info(f"解析字段：query_mode={query_mode}, sample_no={sample_no}, rack_pos={rack_pos}, dilute={dilute}")
            # 查询主表
            today = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
            conn = self._get_db_connection()
            cursor = conn.cursor()

            # 根据查询模式选择不同的SQL
            if query_mode == 'SN':
                # SN 样本编号模式
                sql = "SELECT * FROM lis_pat WHERE ybh=? AND jyrq=? AND yq=?"
                self.logger.info(f"主表查询SQL (SN模式): {sql}")
                self.logger.info(f"主表查询参数: ({sample_no}, {today}, {yq})")
                cursor.execute(sql, (sample_no, today, yq))
            else:
                # BC 条码模式 (默认或其他)
                sql = "SELECT * FROM lis_pat WHERE brdh=? AND jyrq=? AND yq=?"
                self.logger.info(f"主表查询SQL (BC模式): {sql}")
                self.logger.info(f"主表查询参数: ({sample_no}, {today}, {yq})")
                cursor.execute(sql, (sample_no, today, yq))

            pat = cursor.fetchone()
            self.logger.info(f"主表查询结果: {pat}")
            if not pat:
                self.logger.warning("未查到主表数据，返回NF应答")
                return self._build_ack_q03(analyzer_id, lis_id, msg_time, msg_id, msa_code='NF')
            # 字段索引需根据实际表结构调整
            # 例如: brdh=10, bed_no=14, name=11, sex=12, age=15, ybh=3, yq=2, jyrq=1
            # 查询结果表，并通过xm_inter表获取正确的项目代号
            result_sql = """
            SELECT DISTINCT xm.tdh as xmdh 
            FROM lis_result r
            JOIN xm_inter xm ON r.yq = xm.yq AND r.xmdh = xm.xmdh AND xm.dualmode = '0'
            WHERE r.jyrq = ? AND r.yq = ? AND r.ybh = ?
            AND (RIGHT(r.csjg, 2) = '未做' and RIGHT(r.csjg, 2) != '增项')
            """
            result_params = (pat[1], pat[2], pat[3])
            self.logger.info(f"结果表查询SQL: {result_sql}")
            self.logger.info(f"结果表查询参数: {result_params}")
            cursor.execute(result_sql, result_params)
            results = cursor.fetchall()
            self.logger.info(f"结果表查询结果: {results}")
            xmdh_list = [r[0] for r in results]  # 使用xm_inter表的tdh作为xmdh
            # 组装DSR^Q03应答
            response_bytes = self._build_dsr_q03(analyzer_id, lis_id, msg_time, msg_id, pat, xmdh_list, rack_pos, dilute, query_mode, sample_no)
            try:
                self.logger.info(f"应答报文内容：\n{response_bytes.decode('utf-8', errors='replace')}")
            except Exception as e:
                self.logger.error(f"应答报文内容打印失败: {e}")
            return response_bytes
        except Exception as e:
            self.logger.error(f"QRY^Q02应答处理异常: {str(e)}")
            return self._build_ack_q03('','','','','AR',str(e),'206')
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()

    def _build_dsr_q03(self, analyzer_id, lis_id, msg_time, msg_id, pat, xmdh_list, rack_pos, dilute, query_mode='BC', query_sample_no='') -> bytes:
        """组装DSR^Q03应答报文，DSP1和DSP2严格按协议示例输出
        
        Args:
            analyzer_id: 分析仪ID
            lis_id: LIS系统ID
            msg_time: 消息时间
            msg_id: 消息ID
            pat: 患者数据记录
            xmdh_list: 项目代号列表
            rack_pos: 架号位置
            dilute: 稀释倍数
            query_mode: 查询模式 ('BC'=条码模式, 'SN'=样本编号模式)
            query_sample_no: 查询的样本编号/条码号
        """
        now = datetime.now().strftime('%Y%m%d%H%M%S')
        print(f"pat: {pat}")
        # 字段索引需根据实际表结构调整
        brdh = pat[10] if len(pat) > 9 else ''      # 样本条码
        ybh = pat[3] if len(pat) > 3 else ''        # 样本编号 (修正字段索引)
        bed_no = pat[14] if len(pat) > 14 else ''   # 床号
        name = pat[11] if len(pat) > 10 else ''     # 姓名
        #去掉性别包含的空格 性别的原始数据是1用M/2用F/3用O
        sex_raw = pat[12].strip() if len(pat) > 12 else ''
        sex = 'M' if sex_raw == '1' else 'F' if sex_raw == '2' else 'O' if sex_raw == '3' else ''
        nl = pat[15] if len(pat) > 15 else ''       # 年龄
        #处理年龄单位如果是1则Y
        nldw = 'Y' if len(pat) > 16 and pat[16] == '1' else '岁' if len(pat) > 16 and pat[16] == '2' else ''
        ksdh = pat[7] if len(pat) > 7 else ''       # 科室
        
        # 根据查询模式验证返回数据
        if query_mode == 'SN':
            # 样本编号模式：返回的样本编号必须与查询的编号一致
            if query_sample_no and ybh != query_sample_no:
                self.logger.warning(f"样本编号模式验证失败：查询编号={query_sample_no}, 数据库编号={ybh}")
            # 强制使用查询的样本编号确保一致性
            ybh = query_sample_no
        elif query_mode == 'BC':
            # 条码模式：返回的条码必须与查询的条码一致
            if query_sample_no and brdh != query_sample_no:
                self.logger.warning(f"条码模式验证失败：查询条码={query_sample_no}, 数据库条码={brdh}")
            # 强制使用查询的条码确保一致性
            brdh = query_sample_no
        
        # 检测时间、送检时间、报告时间都用now（可根据实际表结构调整）
        test_time = now
        send_time = now
        report_time = now
        
        # 结果类型设置为0（样本结果）
        result_type = '0'
        
        # 组装报文
        msh = f"MSH|^~\\&|{analyzer_id}|I2900|{lis_id}||{now}||DSR^Q03|{msg_id}|P|2.3.1||||0||UNICODE|||"
        msa = f"MSA|OK|{msg_id}||||0|"
        dsp1 = f"DSP|1||{brdh}|{bed_no}|{name}|||{sex}|||||||||||||||||||||{nl}^{nldw}|"
        dsp2 = f"DSP|2|{brdh}|{ybh}|{analyzer_id}|{result_type}||{test_time}|N|1|{rack_pos}||{dilute}||{send_time}|0||||||{ksdh}|{report_time}|||||||||||||||||||||||||||"
        dsp3 = f"DSP|3|{len(xmdh_list)}|{'^'.join(xmdh_list)}|||"
        msg = f"\x0b{msh}\r{msa}\r{dsp1}\r{dsp2}\r{dsp3}\r\x1c\r"
        
        # 记录验证信息
        self.logger.info(f"DSR^Q03应答验证信息：")
        self.logger.info(f"  查询模式: {query_mode}")
        self.logger.info(f"  查询样本号: {query_sample_no}")
        self.logger.info(f"  返回条码: {brdh}")
        self.logger.info(f"  返回样本编号: {ybh}")
        self.logger.info(f"  结果类型: {result_type} (0=样本结果)")
        
        # 明确打印完整应答报文内容
        try:
            self.logger.info("完整应答报文：\n" + msg.replace('\r', '\n'))
        except Exception as e:
            self.logger.error(f"完整应答报文打印失败: {e}")
        return msg.encode('utf-8')

    def _build_ack_q03(self, analyzer_id, lis_id, msg_time, msg_id, msa_code='NF', err_msg='', err_code='') -> bytes:
        """组装ACK^Q03应答报文"""
        now = datetime.now().strftime('%Y%m%d%H%M%S')
        msh = f"MSH|^~\\&|{analyzer_id}|I2900|{lis_id}||{now}||ACK^Q03|{msg_id}|P|2.3.1||||0||UNICODE|||"
        msa = f"MSA|{msa_code}|{msg_id}|{err_msg}|||{err_code}|"
        msg = f"\x0b{msh}\r{msa}\r\x1c\r"
        return msg.encode('utf-8')

    def _handle_oru_ack(self, message: str) -> bytes:
        """处理ORU^R01消息，组装ACK^R01应答（不再校验年龄和单位）"""
        try:
            lines = [line for line in message.split('\r') if line.strip()]
            msh = lines[0].split('|')
            analyzer_id = msh[2] if len(msh) > 2 else ''
            lis_id = msh[4] if len(msh) > 4 else ''
            msg_time = msh[6] if len(msh) > 6 else ''
            msg_id = msh[9] if len(msh) > 9 else ''
            # 不再校验年龄和单位，直接返回AA
            return self._build_ack_r01(analyzer_id, lis_id, msg_time, msg_id, msa_code='AA')
        except Exception as e:
            return self._build_ack_r01('', '', '', '', msa_code='AR', err_msg=str(e), err_code='206')

    def _build_ack_r01(self, analyzer_id, lis_id, msg_time, msg_id, msa_code='AA', err_msg='', err_code='0') -> bytes:
        """组装ACK^R01应答报文"""
        now = datetime.now().strftime('%Y%m%d%H%M%S')
        msh = f"MSH|^~\\&|{analyzer_id}|I2900|{lis_id}||{now}||ACK^R01|{msg_id}|P|2.3.1||||0||UNICODE|||"
        msa = f"MSA|{msa_code}|{msg_id}|{err_msg}|||{err_code}|"
        msg = f"\x0b{msh}\r{msa}\r\x1c\r"
        try:
            self.logger.info("ORU^R01应答报文：\n" + msg.replace('\r', '\n'))
        except Exception as e:
            self.logger.error(f"ORU^R01应答报文打印失败: {e}")
        return msg.encode('utf-8')

    def _process_raw_files(self):
        """处理raw目录下的所有文件"""
        try:
            # 使用绝对路径
            raw_dir = Path(self.raw_dir)
            if not raw_dir.exists():
                return
            
            for file_path in raw_dir.glob('*.txt'):
                file_key = str(file_path)
                current_lines = self._get_file_line_count(file_path)
                
                # 检查文件是否已处理或是否有新行
                if file_key not in self.processed_files or \
                   self.processed_files[file_key].get('line_count', 0) < current_lines:
                    
                    self.logger.info(f"处理raw目录下的文件: {file_path}")
                    
                    # 读取文件内容
                    with open(file_path, 'rb') as f:  # 使用二进制模式读取
                        content = f.read()
                    
                    # 按消息分隔符分割消息
                    messages = content.split(b'\x1c')
                    
                    # 创建或打开输出文件
                    output_filename = f"decoded_{datetime.now():%y%m%d}.txt"
                    output_path = Path(self.processed_dir) / output_filename
                    
                    # 使用追加模式打开文件
                    with open(output_path, 'a', encoding='utf-8') as f:
                        for message in messages:
                            if not message.strip():
                                continue
                            
                            # 移除消息开始标记
                            if message.startswith(b'\x0b'):
                                message = message[1:]
                            
                            try:
                                # 解码消息
                                decoded_message = message.decode('utf-8')
                                
                                # 打印原始消息内容
                                self.logger.info("原始消息内容:")
                                self.logger.info("-" * 50)
                                self.logger.info(decoded_message)
                                self.logger.info("-" * 50)
                                
                                # 解析消息
                                parsed_message = self.parse_hl7_message(decoded_message)
                                
                                # 打印解析后的消息内容
                                self.logger.info("解析后的消息内容:")
                                self.logger.info("-" * 50)
                                self.logger.info(self.format_message(parsed_message))
                                self.logger.info("-" * 50)
                                
                                # 保存到数据库
                                self._save_to_database(parsed_message)
                                self.logger.info(f"消息已保存到数据库")
                                
                                # 格式化解析结果并保存
                                formatted_output = self.format_message(parsed_message)
                                # 只在非空内容后添加分隔线
                                if formatted_output.strip():
                                    f.write(formatted_output)
                                    f.write("\n" + "="*80 + "\n\n")
                                
                            except Exception as e:
                                self.logger.error(f"处理消息时出错: {str(e)}")
                                continue
                    
                    # 更新处理记录
                    self.processed_files[file_key] = {
                        "processed_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "output_file": str(output_path),
                        "line_count": current_lines
                    }
                    self._save_processed_files()
                    
                    self.logger.info(f"文件 {file_path} 处理完成")
                    
        except Exception as e:
            self.logger.error(f"处理raw目录文件时出错: {str(e)}")

    def _monitor_files(self):
        """监控文件变化"""
        while True:
            try:
                # 处理raw目录下的所有文件
                self._process_raw_files()
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                self.logger.error(f"监控文件时出错: {str(e)}")
                time.sleep(5)

    def _performance_monitor(self):
        """性能监控线程"""
        while True:
            try:
                time.sleep(60)  # 每分钟报告一次
                stats = self.get_performance_stats()
                self.logger.info(f"系统性能报告: 消息处理数={stats['messages_processed']}, "
                               f"数据库操作数={stats['database_operations']}, "
                               f"错误数={stats['errors']}, "
                               f"运行时间={stats['runtime_seconds']:.1f}秒, "
                               f"消息处理速度={stats['messages_per_second']:.2f}/秒, "
                               f"活跃线程数={stats['active_threads']}, "
                               f"内存使用={stats['memory_usage_mb']:.1f}MB")
            except Exception as e:
                self.logger.error(f"性能监控错误: {str(e)}")
                time.sleep(60)

    def start(self):
        """启动LIS系统（优化版）"""
        # 启动时处理raw目录下的文件
        self._process_raw_files()
        
        # 启动文件监控线程
        monitor_thread = threading.Thread(target=self._monitor_files)
        monitor_thread.daemon = True
        monitor_thread.start()
        self.logger.info("文件监控器已启动")
        
        # 启动性能监控线程
        stats_thread = threading.Thread(target=self._performance_monitor)
        stats_thread.daemon = True
        stats_thread.start()
        self.logger.info("性能监控器已启动")
        
        # 启动服务器
        while True:  # 外层循环确保服务器持续运行
            server = None
            try:
                server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                server.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)  # 启用TCP keepalive
                server.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)  # 禁用Nagle算法
                server.bind((self.host, self.port))
                server.listen(5)
                self.logger.info(f"服务器启动，监听 {self.host}:{self.port}")
                
                while True:
                    try:
                        # 设置超时，这样可以定期检查服务器状态
                        server.settimeout(5)
                        client_socket, address = server.accept()
                        client_socket.settimeout(None)  # 重置客户端socket的超时
                        client_socket.setsockopt(socket.SOL_SOCKET, socket.SO_KEEPALIVE, 1)  # 启用TCP keepalive
                        client_socket.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)  # 禁用Nagle算法
                        
                        # 使用线程池处理客户端
                        self.thread_pool.submit(self._handle_client, client_socket, address)
                        
                    except socket.timeout:
                        # 超时是正常的，继续监听
                        continue
                    except Exception as e:
                        self.logger.error(f"处理客户端连接时出错: {str(e)}")
                        continue
                        
            except Exception as e:
                self.logger.error(f"服务器错误: {str(e)}")
                self.logger.info("5秒后尝试重新启动服务器...")
                time.sleep(5)  # 等待5秒后重试
            finally:
                if server:
                    try:
                        server.close()
                    except:
                        pass

    def cleanup(self):
        """清理资源"""
        try:
            self.logger.info("正在清理系统资源...")
            
            # 关闭线程池
            if hasattr(self, 'thread_pool'):
                self.thread_pool.shutdown(wait=True, timeout=10)
                self.logger.info("线程池已关闭")
            
            # 最后的性能报告
            stats = self.get_performance_stats()
            self.logger.info(f"最终性能报告: {stats}")
            
        except Exception as e:
            self.logger.error(f"清理资源时出错: {str(e)}")

def is_admin():
    """检查是否有管理员权限"""
    if not WINDOWS_FEATURES_AVAILABLE:
        return True  # 如果没有Windows模块，假设有权限
    try:
        return ctypes.windll.shell32.IsUserAnAdmin()
    except:
        return False

def run_as_admin():
    """以管理员权限运行程序"""
    if not WINDOWS_FEATURES_AVAILABLE:
        print("Windows特定功能不可用，跳过权限检查")
        return  # 如果没有Windows模块，跳过权限检查

    if not is_admin():
        print("当前没有管理员权限，但程序将继续运行")
        print("注意：某些功能可能受限")
        return  # 不强制要求管理员权限，让程序继续运行

        # 如果需要强制管理员权限，取消下面的注释
        # print("需要管理员权限，正在重新启动...")
        # ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, " ".join(sys.argv), None, 1)
        # sys.exit()
    else:
        print("已获得管理员权限")

def disable_console_quick_edit():
    """禁用Windows控制台的快速编辑模式，避免程序意外暂停"""
    if not WINDOWS_FEATURES_AVAILABLE:
        return  # 如果没有Windows模块，跳过
    try:
        # 只在Windows系统上执行
        if os.name != 'nt':
            return

        # 获取标准输入句柄
        kernel32 = ctypes.windll.kernel32
        h_stdin = kernel32.GetStdHandle(STD_INPUT_HANDLE)

        if h_stdin == -1:  # INVALID_HANDLE_VALUE
            return

        # 获取当前控制台模式
        mode = wintypes.DWORD()
        if not kernel32.GetConsoleMode(h_stdin, ctypes.byref(mode)):
            return

        # 移除快速编辑模式、插入模式和鼠标输入
        new_mode = mode.value
        new_mode &= ~ENABLE_QUICK_EDIT_MODE  # 移除快速编辑模式
        new_mode &= ~ENABLE_INSERT_MODE      # 移除插入模式
        new_mode &= ~ENABLE_MOUSE_INPUT      # 移除鼠标输入

        # 设置新的控制台模式
        kernel32.SetConsoleMode(h_stdin, new_mode)
        print("已禁用控制台快速编辑模式，避免程序意外暂停")
        
    except Exception as e:
        print(f"设置控制台模式时出错: {e}")
        # 即使失败也不影响程序继续运行

def find_running_instance():
    """查找是否有相同程序的实例在运行"""
    if not WINDOWS_FEATURES_AVAILABLE:
        return None  # 如果没有psutil，跳过重复实例检查

    current_pid = os.getpid()
    current_exe = os.path.abspath(sys.executable)

    try:
        for proc in psutil.process_iter(['pid', 'name', 'exe']):
            try:
                # 检查是否是Python进程
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    # 检查是否是同一个程序
                    if proc.info['exe'] and os.path.abspath(proc.info['exe']) == current_exe:
                        # 排除当前进程
                        if proc.info['pid'] != current_pid:
                            return proc.info['pid']
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    except:
        pass
    return None

def terminate_process(pid):
    """终止指定PID的进程"""
    if not WINDOWS_FEATURES_AVAILABLE:
        return True  # 如果没有psutil，假设成功

    try:
        process = psutil.Process(pid)
        process.terminate()
        # 等待进程结束
        process.wait(timeout=5)
        return True
    except psutil.NoSuchProcess:
        return True
    except psutil.TimeoutExpired:
        # 如果进程没有及时结束，强制结束
        try:
            process.kill()
            return True
        except:
            return False
    except:
        return False

def create_image():
    """创建一个简单的托盘图标"""
    if not TRAY_AVAILABLE:
        return None
    # 创建一个简单的托盘图标
    image = Image.new('RGB', (64, 64), (0, 128, 255))
    d = ImageDraw.Draw(image)
    d.rectangle([16, 16, 48, 48], fill=(255, 255, 255))
    return image

def on_exit(icon=None, item=None):
    """退出程序"""
    if icon and TRAY_AVAILABLE:
        icon.stop()
    sys.exit(0)

def main():
    print("程序启动中...")

    # 检查是否有管理员权限
    print("检查管理员权限...")
    run_as_admin()
    print("权限检查完成")

    # 禁用控制台快速编辑模式
    print("禁用控制台快速编辑模式...")
    disable_console_quick_edit()
    print("控制台设置完成")

    # 检查是否有其他实例在运行
    print("检查是否有其他实例在运行...")
    existing_pid = find_running_instance()
    if existing_pid:
        print(f"发现程序已在运行(PID: {existing_pid})，正在关闭...")
        if terminate_process(existing_pid):
            print("已关闭旧实例，正在启动新实例...")
            time.sleep(2)  # 等待旧实例完全关闭
        else:
            print("无法关闭旧实例，程序退出")
            sys.exit(1)
    else:
        print("没有发现其他实例")
    
    # 启动LIS服务线程
    t = threading.Thread(target=run_lis, daemon=True)
    t.start()

    # 托盘菜单（如果可用）
    if TRAY_AVAILABLE:
        # 全局变量存储LIS实例和图标，用于动态更新
        global lis_instance, tray_icon
        
        def show_port_settings():
            """显示端口设置对话框"""
            def _show_port_thread():
                try:
                    # 读取当前配置
                    config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.ini")
                    config = configparser.ConfigParser()
                    config.read(config_path, encoding='utf-8')
                    
                    current_port = config.get('SYSTEM', 'port', fallback='22010')
                    
                    # 使用Windows原生InputBox获取新端口
                    if WINDOWS_FEATURES_AVAILABLE:
                        import ctypes
                        
                        # 使用更可靠的方式创建居中的输入对话框
                        from tkinter import simpledialog
                        import tkinter as tk
                        
                        # 创建一个小的可见根窗口作为父窗口，确保对话框居中
                        root = tk.Tk()
                        root.title("LIS系统")
                        
                        # 计算屏幕中心位置
                        screen_width = root.winfo_screenwidth()
                        screen_height = root.winfo_screenheight()
                        
                        # 创建一个1x1像素的窗口在屏幕中心
                        window_width = 1
                        window_height = 1
                        x = (screen_width - window_width) // 2
                        y = (screen_height - window_height) // 2
                        
                        root.geometry(f"{window_width}x{window_height}+{x}+{y}")
                        root.attributes('-topmost', True)
                        root.attributes('-alpha', 0.01)  # 几乎透明
                        root.update()
                        
                        # 显示端口输入对话框
                        new_port = simpledialog.askinteger(
                            "自定义端口",
                            f"当前端口: {current_port}\n请输入新的端口号 (1024-65535):",
                            initialvalue=int(current_port),
                            minvalue=1024,
                            maxvalue=65535,
                            parent=root
                        )
                        
                        root.destroy()
                        
                        if new_port and new_port != int(current_port):
                            # 更新配置文件
                            config['SYSTEM']['port'] = str(new_port)
                            with open(config_path, 'w', encoding='utf-8') as f:
                                config.write(f)
                            
                            # 显示重启提示
                            result = show_centered_messagebox(
                                f"端口已更改为 {new_port}\n需要重启程序才能生效，是否立即重启？", 
                                "端口更改", 
                                0x24  # MB_YESNO | MB_ICONQUESTION
                            )
                            
                            if result == 6:  # IDYES
                                restart_service()
                        elif new_port == int(current_port):
                            show_centered_messagebox("端口未发生变化", "提示", 0x40)
                    else:
                        print(f"当前端口: {current_port}")
                        print("请手动编辑config.ini文件来更改端口")
                        
                except Exception as e:
                    print(f"显示端口设置时出错: {e}")
                    if WINDOWS_FEATURES_AVAILABLE:
                        show_centered_messagebox(f"端口设置失败: {e}", "错误", 0x10)
                    import traceback
                    traceback.print_exc()

            # 在新线程中运行，避免阻塞托盘
            threading.Thread(target=_show_port_thread, daemon=True).start()

        def show_settings():
            """显示完整的系统设置界面"""
            def _show_full_settings():
                try:
                    # 显示一个简化的设置信息，或者启动配置工具
                    show_centered_messagebox(
                        "完整的系统配置请使用:\n1. 运行 main.py 配置工具\n2. 手动编辑 config.ini 文件\n\n当前可通过'端口设置'菜单快速修改端口",
                        "系统设置",
                        0x40
                    )
                except Exception as e:
                    print(f"显示设置界面时出错: {e}")
                    if WINDOWS_FEATURES_AVAILABLE:
                        show_centered_messagebox(f"显示设置失败: {e}", "错误", 0x10)

            # 在新线程中运行
            threading.Thread(target=_show_full_settings, daemon=True).start()
        def show_status():
            """显示服务状态"""
            def _show_status_thread():
                try:
                    if lis_instance:
                        stats = lis_instance.get_performance_stats()
                        runtime_seconds = stats.get('runtime_seconds', 0)
                        runtime_str = f"{int(runtime_seconds//3600)}小时{int((runtime_seconds%3600)//60)}分钟{int(runtime_seconds%60)}秒"
                        status_msg = f"""监听端口: {lis_instance.port}
运行时间: {runtime_str}
处理消息数: {stats.get('messages_processed', 0)}
数据库操作数: {stats.get('database_operations', 0)}
活跃线程数: {stats.get('active_threads', 0)}
内存使用: {stats.get('memory_usage_mb', 0):.1f}MB
错误数: {stats.get('errors', 0)}"""
                        
                        # 使用Windows原生MessageBox
                        if WINDOWS_FEATURES_AVAILABLE:
                            show_centered_messagebox(status_msg, "LIS系统状态", 0x40)  # MB_ICONINFORMATION
                        else:
                            print(f"LIS系统状态:\n{status_msg}")
                    else:
                        # 使用Windows原生MessageBox显示警告
                        if WINDOWS_FEATURES_AVAILABLE:
                            show_centered_messagebox("LIS系统未运行", "状态", 0x30)  # MB_ICONWARNING
                        else:
                            print("状态: LIS系统未运行")
                        
                except Exception as e:
                    print(f"显示状态弹窗时出错: {e}")
                    import traceback
                    traceback.print_exc()
            
            # 在新线程中显示状态，避免阻塞pystray
            threading.Thread(target=_show_status_thread, daemon=True).start()
        
        def restart_service():
            """重启服务"""
            def _restart():
                try:
                    # 使用Windows原生MessageBox显示重启信息
                    if WINDOWS_FEATURES_AVAILABLE:
                        show_centered_messagebox("正在重启LIS系统...", "重启", 0x40)  # MB_ICONINFORMATION

                    # 停止托盘图标
                    if tray_icon:
                        tray_icon.stop()

                    # 重启程序
                    os.execv(sys.executable, [sys.executable] + sys.argv)

                except Exception as e:
                    # 使用Windows原生MessageBox显示错误
                    if WINDOWS_FEATURES_AVAILABLE:
                        show_centered_messagebox(f"重启失败: {e}", "错误", 0x10)  # MB_ICONERROR
                    else:
                        print(f"重启失败: {e}")

            # 在新线程中执行，避免阻塞托盘
            threading.Thread(target=_restart, daemon=True).start()

        # 获取当前端口
        current_port = lis_instance.port if lis_instance else 22010
        
        menu = pystray.Menu(
            pystray.MenuItem('服务状态', show_status),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem('端口设置', pystray.Menu(
                pystray.MenuItem(f'当前端口: {current_port}', None),  # 只显示，不可点击
                pystray.Menu.SEPARATOR,
                pystray.MenuItem('自定义端口...', show_port_settings)
            )),
            pystray.Menu.SEPARATOR,
            pystray.MenuItem('重启服务', restart_service),
            pystray.MenuItem('设置', show_settings),
            pystray.MenuItem('退出', on_exit)
        )
        
        tray_icon = pystray.Icon("LIS系统", create_image(), "LIS系统", menu)
        print("LIS系统已启动，运行在系统托盘中...")
        tray_icon.run()
    else:
        print("LIS系统已启动（无托盘功能）...")
        print("按 Ctrl+C 退出程序")
        try:
            # 保持程序运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在退出...")
            on_exit()

def run_lis():
    """启动LIS系统"""
    global lis_instance
    lis_instance = None
    try:
        lis_instance = LISSystem()
        lis_instance.start()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"LIS系统运行错误: {e}")
    finally:
        if lis_instance:
            lis_instance.cleanup()
        lis_instance = None

def show_error_message(message):
    """显示错误消息对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 计算屏幕中心位置
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    
    # 创建错误对话框
    dialog = tk.Toplevel(root)
    dialog.title("错误")
    dialog.withdraw()  # 先隐藏，防止闪烁
    dialog.configure(bg="#f0f0f0")
    
    # 错误图标
    try:
        error_icon = tk.Label(dialog, text="❌", font=("Arial", 24), fg="red", bg="#f0f0f0")
        error_icon.pack(pady=(10, 5))
    except:
        pass  # 如果无法显示图标，忽略
    
    # 错误消息
    msg_label = tk.Label(dialog, text=message, wraplength=350, justify="center", bg="#f0f0f0")
    msg_label.pack(pady=10, padx=20)
    
    # 确定按钮
    button_frame = tk.Frame(dialog, bg="#f0f0f0")
    button_frame.pack(pady=10)
    ok_button = tk.Button(button_frame, text="确定", command=dialog.destroy, width=10, bg="#4CAF50", fg="white")
    ok_button.pack()
    
    # 设置窗口尺寸
    dialog.geometry("400x150")
    
    # 居中显示
    x = (screen_width - 400) // 2
    y = (screen_height - 150) // 2
    dialog.geometry(f"400x150+{x}+{y}")
    
    # 显示窗口
    dialog.deiconify()
    dialog.grab_set()
    
    # 等待对话框关闭
    root.wait_window(dialog)
    root.destroy()

if __name__ == "__main__":
    main()