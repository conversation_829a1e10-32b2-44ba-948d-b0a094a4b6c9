# URIT设备接入指南

## 设备信息

- **设备品牌**: URIT（优利特）
- **设备型号**: 8030系列生化分析仪
- **协议类型**: HL7 v2.3.1
- **消息类型**: ORU^R01（结果上传）

## 消息格式分析

### 您的设备消息格式
```
MSH|^~\&|urit|8030|||20180101010440||ORU^R01|201801010001|P|2.3.1||||0||ASCII|||
PID|1||||||0|||||0|||||||||||||||||||
OBR|1||201801010001|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||
OBX|1|NM|1|ALT|14.7|U/L|0.0-40.0|N|||F||-0.0049|2018-01-01||杨玉清||
```

### 字段解析
- **MSH段**: 消息头，设备标识为"urit"，型号为"8030"
- **PID段**: 患者信息（URIT设备中较简单）
- **OBR段**: 样本请求信息，样本ID在第3字段
  - 样本ID处理: 截取后4位并去除前导0
  - 示例: `201801010001` → `0001` → `1`
  - 示例: `201801010002` → `0002` → `2`
- **OBX段**: 检验结果
  - 第4字段: 项目代码（如ALT）
  - 第5字段: 结果值（如14.7）
  - 第6字段: 单位（如U/L）
  - 第7字段: 参考范围（如0.0-40.0）

## 配置步骤

### 1. 更新配置文件

在 `config_multi_device.ini` 中添加URIT设备配置：

```ini
[DEVICES]
configured = yingkai_lab1,yingkai_lab2,abbott_lab1,roche_lab1,urit_lab1

[DEVICE_urit_lab1]
device_type = URIT_Analyzer
manufacturer = URIT
model = URIT-8030
protocol = HL7
adapter = urit
# 根据您的实际网络环境配置IP范围
ip_range = *************-*************
ports = 22010,22011
message_patterns = MSH|^~\\&|urit|,|urit|,ORU^R01,|2.3.1|
priority = 15
table_mapping = lis_pat,lis_result
# YQ配置 - 自定义YQ值
yq_source = custom
yq_value = URIT8030
enabled = true
```

### 2. 适配器配置

已自动配置URIT适配器：

```ini
[ADAPTER_urit]
module = adapters.urit_adapter
class = URITAdapter
protocol = HL7
version = 2.3.1
manufacturer = URIT
timeout = 30
test_mapping = {"ALT": "ALT", "AST": "AST", "TBIL": "TBIL", "DBIL": "DBIL", "TP": "TP", "ALB": "ALB"}
validation_enabled = true
```

## 数据库映射

### 患者表 (lis_pat)
```sql
INSERT INTO lis_pat (wyh, jyrq, yq, ybh, yblx, fb, ksdh, cwh, brxm, xb, nl, brly, brdh, zd, bz, bz1)
VALUES (
    '201801010001',           -- wyh (消息控制ID)
    '2018-01-01 00:00:00.000', -- jyrq (检验日期)
    'URIT8030',               -- yq (仪器标识，使用自定义值)
    '1',                      -- ybh (样本编号，处理后: 201801010001 -> 1)
    '', '', '', '', '', '', '', '', '', '', '', ''  -- 其他字段
)
```

### 结果表 (lis_result)
```sql
INSERT INTO lis_result (wyh, jyrq, yq, ybh, xmdh, csjg, instrid, refs, result_flag, bz1, bz2, textresult, num_result)
VALUES (
    '201801010001',           -- wyh (消息控制ID)
    '2018-01-01 00:00:00.000', -- jyrq (检验日期)
    'URIT8030',               -- yq (仪器标识)
    '1',                      -- ybh (样本编号，处理后: 201801010001 -> 1)
    'ALT',                    -- xmdh (项目代码)
    '14.7',                   -- csjg (结果值)
    '0',                      -- instrid (仪器ID)
    '0.0-40.0',              -- refs (参考范围)
    'N',                      -- result_flag (异常标志)
    '', '', '',               -- bz1, bz2, textresult
    '14.7'                    -- num_result (数值结果)
)
```

## 样本ID处理规则

系统会自动处理URIT设备的样本ID：

| 原始样本ID | 截取后4位 | 去除前导0后 | 最终样本ID |
|-----------|----------|------------|-----------|
| 201801010001 | 0001 | 1 | 1 |
| 201801010002 | 0002 | 2 | 2 |
| 201801010123 | 0123 | 123 | 123 |
| 201801010000 | 0000 | 0 | 0 |
| 201801015678 | 5678 | 5678 | 5678 |

## YQ配置选项

### 方案1: 自定义固定YQ值（推荐）
```ini
yq_source = custom
yq_value = URIT8030
```
效果：所有URIT设备数据的YQ字段都使用"URIT8030"

### 方案2: 设备ID模式
```ini
yq_source = device_id
yq_prefix = URIT_
```
效果：YQ值为"URIT_urit_lab1"

### 方案3: 映射模式
```ini
yq_source = mapping
yq_mapping = {"8030": "URIT8030", "8031": "URIT8031"}
```
效果：根据消息中的设备型号映射到对应YQ值

## 启动和测试

### 1. 测试配置
```bash
# 运行URIT设备测试
python test_urit_device.py
```

### 2. 启动系统
```bash
# 启动LIS系统（多设备模式）
python lis_system.py
```

### 3. 验证数据
启动后，URIT设备发送的数据将：
1. 自动识别为URIT设备消息
2. 解析HL7格式
3. 使用自定义YQ值"URIT8030"
4. 写入lis_pat和lis_result表

## 支持的检验项目

当前支持的项目代码映射：
- ALT (丙氨酸氨基转移酶)
- AST (天门冬氨酸氨基转移酶)
- TBIL (总胆红素)
- DBIL (直接胆红素)
- TP (总蛋白)
- ALB (白蛋白)
- GLU (葡萄糖)
- BUN (血尿素氮)
- CREA (肌酐)
- UA (尿酸)

如需添加其他项目，可在配置文件的`test_mapping`中添加。

## 故障排查

### 1. 设备无法识别
- 检查IP地址范围配置
- 确认端口配置正确
- 查看日志中的设备识别信息

### 2. 消息解析失败
- 确认消息格式符合HL7 v2.3.1标准
- 检查消息是否包含必要的MSH、OBR、OBX段
- 查看适配器日志

### 3. 数据库写入失败
- 检查YQ值是否正确设置
- 确认字段长度不超过限制
- 验证数据库连接

### 4. YQ自定义不生效
- 确认多设备模式已启用
- 检查设备配置是否正确加载
- 查看YQ处理日志

## 注意事项

1. **网络配置**: 根据实际网络环境调整IP地址范围
2. **端口配置**: 确保端口不冲突
3. **数据库兼容**: 确保数据库表结构支持URIT数据格式
4. **日志监控**: 启动后注意观察日志输出，确认设备正常工作
5. **测试验证**: 建议先在测试环境验证功能正常后再部署到生产环境

现在您的URIT设备已经可以完美集成到LIS系统中了！