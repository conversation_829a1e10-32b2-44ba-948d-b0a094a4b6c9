#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
URIT设备数据解码测试

测试URIT设备的HL7消息解析和数据库写入功能
"""

import logging
import sys
from datetime import datetime
from adapters.urit_adapter import URITAdapter
from device_adapter import DeviceInfo

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_urit_message_parsing():
    """测试URIT消息解析"""
    print("=== 测试URIT设备消息解析 ===")
    
    # 您提供的实际URIT消息
    urit_message1 = b"""MSH|^~\\&|urit|8030|||20180101010440||ORU^R01|201801010001|P|2.3.1||||0||ASCII|||
PID|1||||||0|||||0|||||||||||||||||||
OBR|1||201801010001|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||
OBX|1|NM|1|ALT|14.7|U/L|0.0-40.0|N|||F||-0.0049|2018-01-01||杨玉清||"""

    urit_message2 = b"""MSH|^~\\&|urit|8030|||20180101010441||ORU^R01|201801010002|P|2.3.1||||0||ASCII|||
PID|1||||||0|||||0|||||||||||||||||||
OBR|1||201801010002|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||
OBX|1|NM|1|ALT|13.8|U/L|0.0-40.0|N|||F||-0.0046|2018-01-01||杨玉清||"""
    
    # 创建URIT适配器
    adapter_config = {
        'field_length_limits': {
            'patient_id': 50,
            'sample_id': 50,
            'test_code': 20,
            'test_value': 200,
            'test_unit': 20
        },
        'test_mapping': {
            'ALT': 'ALT',
            'AST': 'AST',
            'TBIL': 'TBIL',
            'DBIL': 'DBIL'
        }
    }
    
    adapter = URITAdapter(adapter_config)
    
    # 模拟连接信息
    connection_info = {
        'address': ('*************', 22010)
    }
    
    # 测试消息识别
    print("\n1. 测试消息识别:")
    can_handle1 = adapter.can_handle(urit_message1, connection_info)
    can_handle2 = adapter.can_handle(urit_message2, connection_info)
    print(f"消息1识别结果: {can_handle1}")
    print(f"消息2识别结果: {can_handle2}")
    
    assert can_handle1, "应该能识别URIT消息1"
    assert can_handle2, "应该能识别URIT消息2"
    
    # 测试消息解析
    print("\n2. 测试消息解析:")
    
    # 解析第一条消息
    try:
        standard_msg1 = adapter.parse_message(urit_message1, connection_info)
        print(f"消息1解析成功:")
        print(f"  消息类型: {standard_msg1.message_type}")
        print(f"  消息ID: {standard_msg1.message_id}")
        print(f"  设备类型: {standard_msg1.device_info.device_type}")
        print(f"  设备型号: {standard_msg1.device_info.model}")
        print(f"  原始样本ID: {standard_msg1.orders[0].metadata.get('raw_sample_id', '')}")
        print(f"  处理后样本ID: {standard_msg1.patient.sample_id}")
        print(f"  结果数量: {len(standard_msg1.orders[0].results)}")
        
        # 验证样本ID处理结果
        expected_sample_id = "1"  # 201801010001 -> 0001 -> 1
        actual_sample_id = standard_msg1.patient.sample_id
        assert actual_sample_id == expected_sample_id, f"样本ID处理错误: 预期{expected_sample_id}, 实际{actual_sample_id}"
        print(f"  ✓ 样本ID处理正确: 201801010001 -> {actual_sample_id}")
        
        # 检查结果详情
        for i, result in enumerate(standard_msg1.orders[0].results):
            print(f"  结果{i+1}: {result.test_code}={result.value} {result.unit} (参考范围: {result.reference_range})")
        
    except Exception as e:
        print(f"消息1解析失败: {e}")
        return False
    
    # 解析第二条消息
    try:
        standard_msg2 = adapter.parse_message(urit_message2, connection_info)
        print(f"\n消息2解析成功:")
        print(f"  消息ID: {standard_msg2.message_id}")
        print(f"  原始样本ID: {standard_msg2.orders[0].metadata.get('raw_sample_id', '')}")
        print(f"  处理后样本ID: {standard_msg2.patient.sample_id}")
        
        # 验证第二条消息的样本ID处理
        expected_sample_id2 = "2"  # 201801010002 -> 0002 -> 2
        actual_sample_id2 = standard_msg2.patient.sample_id
        assert actual_sample_id2 == expected_sample_id2, f"样本ID处理错误: 预期{expected_sample_id2}, 实际{actual_sample_id2}"
        print(f"  ✓ 样本ID处理正确: 201801010002 -> {actual_sample_id2}")
        
        for i, result in enumerate(standard_msg2.orders[0].results):
            print(f"  结果{i+1}: {result.test_code}={result.value} {result.unit}")
        
    except Exception as e:
        print(f"消息2解析失败: {e}")
        return False
    
    # 测试YQ自定义功能
    print("\n3. 测试YQ自定义功能:")
    
    # 设置YQ配置
    device_info = standard_msg1.device_info
    device_info.yq_config = {
        'yq_source': 'custom',
        'yq_value': 'URIT8030'
    }
    standard_msg1.device_info = device_info
    
    # 测试YQ处理
    original_yq = "8030"  # 消息中的原始YQ
    effective_yq = adapter.get_yq_value(standard_msg1, original_yq)
    print(f"原始YQ: {original_yq}")
    print(f"有效YQ: {effective_yq}")
    
    assert effective_yq == "URIT8030", f"预期URIT8030，实际{effective_yq}"
    
    # 测试响应生成
    print("\n4. 测试响应生成:")
    response = adapter.generate_response(standard_msg1)
    response_str = response.decode('utf-8')
    print(f"生成的ACK响应:\n{response_str}")
    
    assert "ACK^R01" in response_str, "响应应包含ACK^R01"
    assert "AA" in response_str, "响应应包含AA确认"
    
    print("\n✓ URIT设备消息解析测试全部通过!")
    return True

def test_database_integration():
    """测试数据库集成（模拟）"""
    print("\n=== 测试数据库集成模拟 ===")
    
    # 模拟数据库操作的SQL语句（使用处理后的样本ID）
    sample_data = {
        'wyh': '201801010001',
        'jyrq': '2018-01-01 00:00:00.000',
        'yq': 'URIT8030',  # 使用自定义YQ值
        'ybh': '1',        # 处理后的样本ID：201801010001 -> 1
        'yblx': '',
        'fb': '',
        'ksdh': '',
        'cwh': '',
        'brxm': '',
        'xb': '',
        'nl': '',
        'brly': '',
        'brdh': '',
        'zd': '',
        'bz': '',
        'bz1': ''
    }
    
    result_data = {
        'wyh': '201801010001',
        'jyrq': '2018-01-01 00:00:00.000',
        'yq': 'URIT8030',
        'ybh': '1',        # 处理后的样本ID：201801010001 -> 1
        'xmdh': 'ALT',
        'csjg': '14.7',
        'instrid': '0',
        'refs': '0.0-40.0',
        'result_flag': 'N',
        'bz1': '',
        'bz2': '',
        'textresult': '',
        'num_result': '14.7'
    }
    
    # 模拟SQL语句
    patient_sql = """
    INSERT INTO lis_pat (wyh, jyrq, yq, ybh, yblx, fb, ksdh, cwh, brxm, xb, nl, brly, brdh, zd, bz, bz1)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    result_sql = """
    INSERT INTO lis_result (wyh, jyrq, yq, ybh, xmdh, csjg, instrid, refs, result_flag, bz1, bz2, textresult, num_result)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    print("1. 患者信息插入SQL:")
    print(patient_sql)
    print(f"参数: {list(sample_data.values())}")
    
    print("\n2. 结果信息插入SQL:")
    print(result_sql)
    print(f"参数: {list(result_data.values())}")
    
    # 验证关键字段
    print(f"\n3. 关键字段验证:")
    print(f"YQ字段: {sample_data['yq']} (自定义值)")
    print(f"样本号: {sample_data['ybh']} (原始: 201801010001 -> 处理后: 1)")
    print(f"项目代码: {result_data['xmdh']}")
    print(f"结果值: {result_data['csjg']}")
    print(f"参考范围: {result_data['refs']}")
    
    # 额外测试样本ID处理的各种情况
    print(f"\n4. 样本ID处理示例:")
    test_cases = [
        ("201801010001", "1"),
        ("201801010002", "2"),
        ("201801010123", "123"),
        ("201801010000", "0"),
        ("201801015678", "5678"),
        ("12340999", "999")
    ]
    
    for original, expected in test_cases:
        # 模拟样本ID处理
        last_4 = original[-4:]
        processed = str(int(last_4))
        print(f"  {original} -> {last_4} -> {processed} (预期: {expected})")
        assert processed == expected, f"处理错误: {original}"
    
    print("\n✓ 数据库集成模拟测试通过!")

def show_configuration_example():
    """显示配置示例"""
    print("\n=== URIT设备配置示例 ===")
    
    config_example = """
# 在config_multi_device.ini中添加以下配置:

[DEVICE_urit_lab1]
device_type = URIT_Analyzer
manufacturer = URIT
model = URIT-8030
protocol = HL7
adapter = urit
# 根据您的网络环境设置IP范围
ip_range = *************-*************
ports = 22010,22011
message_patterns = MSH|^~\\\\&|urit|,|urit|,ORU^R01,|2.3.1|
priority = 15
table_mapping = lis_pat,lis_result
# YQ配置 - 可根据需要修改
yq_source = custom
yq_value = URIT8030
enabled = true

# 如果需要使用映射模式:
# yq_source = mapping
# yq_mapping = {"8030": "URIT8030", "8031": "URIT8031"}

# 如果需要使用设备ID模式:
# yq_source = device_id
# yq_prefix = URIT_
"""
    
    print(config_example)

if __name__ == "__main__":
    try:
        # 运行所有测试
        success = test_urit_message_parsing()
        if success:
            test_database_integration()
            show_configuration_example()
            
            print("\n" + "="*60)
            print("🎉 URIT设备集成测试全部通过！")
            print("="*60)
            print("\n操作步骤:")
            print("1. 确保config_multi_device.ini中已配置URIT设备")
            print("2. 根据实际网络环境调整IP地址范围")
            print("3. 启动LIS系统: python lis_system.py")
            print("4. URIT设备数据将自动解码并写入数据库")
            print("\n注意:")
            print("- YQ值将使用配置的自定义值 'URIT8030'")
            print("- 数据会写入lis_pat和lis_result表")
            print("- 系统会自动识别URIT消息格式")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)