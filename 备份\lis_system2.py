import socket
import threading
import time
import json
import logging
import configparser
import pyodbc
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass
from typing import List, Optional
import os
import pystray
from PIL import Image, ImageDraw
import sys

# 数据类定义
@dataclass
class TestResult:
    test_name: str          # 检验项目名称
    value: float            # 检验结果值
    unit: str              # 单位
    reference_range: str    # 参考范围
    abnormal_flag: str      # 异常标志
    observation_time: str   # 观察时间
    instrument_id: str      # 仪器ID
    reagent_lot: str       # 试剂批号
    reagent_expiry: str    # 试剂有效期
    control_id: str        # 质控ID

@dataclass
class Order:
    order_id: str          # 医嘱ID
    sample_id: str         # 样本ID
    test_type: str         # 检验类型
    order_time: str        # 医嘱时间
    report_time: str       # 报告时间
    priority: str          # 优先级
    results: List[TestResult]  # 检验结果列表

@dataclass
class Patient:
    patient_id: str        # 患者ID
    sex: str              # 性别
    age: str              # 年龄
    name: str             # 姓名
    birth_date: str       # 出生日期
    department: str       # 科室
    bed_no: str          # 床号

@dataclass
class HL7Message:
    message_type: str      # 消息类型
    message_control_id: str # 消息控制ID
    sending_facility: str  # 发送机构
    receiving_facility: str # 接收机构
    message_time: str     # 消息时间
    patient: Patient      # 患者信息
    orders: List[Order]   # 医嘱列表

class LISSystem:
    def __init__(self, config_file="config.ini"):
        """
        初始化LIS系统
        :param config_file: 配置文件路径
        """
        # 定义配置文件可能存在的路径列表
        possible_paths = [
            os.path.abspath(config_file),  # 当前工作目录
            os.path.join(os.path.dirname(os.path.abspath(__file__)), config_file),  # 脚本所在目录
            os.path.join(r"C:\Lis2002\I2900", config_file),  # I2900主程序目录
            os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "I2900", config_file)  # 相对于脚本的I2900目录
        ]
        
        # 遍历所有可能的路径，找到第一个存在的配置文件
        config_path = None
        for path in possible_paths:
            print(f"尝试加载配置文件: {path}")  # 调试用
            if os.path.exists(path):
                config_path = path
                break
        
        if config_path is None:
            raise FileNotFoundError(f"未找到配置文件。尝试过以下路径:\n" + "\n".join(possible_paths))
            
        print(f"成功加载配置文件: {config_path}")
        self.config = configparser.ConfigParser()
        self.config.read(config_path, encoding='utf-8')
        if 'SYSTEM' not in self.config:
            raise KeyError(f"配置文件{config_path}缺少[SYSTEM]段")
        
        # 系统配置
        self.host = self.config['SYSTEM']['host']
        self.port = int(self.config['SYSTEM']['port'])
        self.raw_dir = Path(self.config['SYSTEM']['raw_dir'])
        self.processed_dir = Path(self.config['SYSTEM']['processed_dir'])
        self.processed_files = {}
        
        # 创建必要的目录
        self.raw_dir.mkdir(exist_ok=True)
        self.processed_dir.mkdir(exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config['SYSTEM']['log_file'], encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # 加载已处理文件记录
        self._load_processed_files()
        
        # 初始化数据库连接
        self._init_database()

    def _init_database(self):
        """初始化数据库连接"""
        try:
            conn_str = (
                f"DRIVER={{{self.config['DATABASE']['driver']}}};"
                f"SERVER={self.config['DATABASE']['server']};"
                f"DATABASE={self.config['DATABASE']['database']};"
                f"UID={self.config['DATABASE']['username']};"
                f"PWD={self.config['DATABASE']['password']}"
            )
            self.db_conn = pyodbc.connect(conn_str)
            self.logger.info("数据库连接成功")
        except Exception as e:
            self.logger.error(f"数据库连接失败: {str(e)}")
            raise

    def _save_to_database(self, hl7_message: HL7Message):
        """保存数据到数据库（支持brdh查ybh并按xmdh更新结果）"""
        cursor = None
        try:
            cursor = self.db_conn.cursor()
            
            # 保存每个样本（每个Order）到lis_pat表
            pat_sql = f"""
            INSERT INTO {self.config['TABLE_MAPPING']['pat_table']} (
                wyh, jyrq, yq, ybh, yblx, fb, ksdh, sjys, yhdh,
                brdh, brxm, brxb, brly, ch, nl, nldw, sqh, sqsj, dysj,
                dybz, jgbz, bbbz, zd, bz, bz1, zjf, cyrq, bq, czybh
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            for order in hl7_message.orders:
                # jyrq用OBR第7列 Observation Date/Time
                jyrq_raw = order.order_time
                if jyrq_raw and len(jyrq_raw) >= 8:
                    try:
                        jyrq_fmt = datetime.strptime(jyrq_raw[:14], '%Y%m%d%H%M%S') if len(jyrq_raw) >= 14 else datetime.strptime(jyrq_raw[:8], '%Y%m%d')
                        jyrq = jyrq_fmt.strftime('%Y-%m-%d 00:00:00.000')
                    except Exception:
                        jyrq = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
                else:
                    jyrq = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
                
                # 判断是否已存在相同jyrq、yq、ybh的数据
                check_pat_sql = f"SELECT COUNT(1) FROM {self.config['TABLE_MAPPING']['pat_table']} WHERE jyrq=? AND yq=? AND ybh=?"
                cursor.execute(check_pat_sql, (jyrq, hl7_message.sending_facility, order.sample_id))
                exists = cursor.fetchone()[0]
                if exists:
                    self.logger.info(f"已存在jyrq={jyrq}, yq={hl7_message.sending_facility}, ybh={order.sample_id}的患者信息，不再插入。")
                    continue
                
                pat_data = (
                    hl7_message.message_control_id,  # wyh
                    jyrq,                           # jyrq
                    hl7_message.sending_facility,   # yq
                    order.sample_id,                # ybh（样本编号，取自OBR第4列）
                    '',                             # yblx
                    '',                             # fb
                    hl7_message.patient.department, # ksdh
                    '',                             # sjys
                    '',                             # yhdh
                    order.order_id,                 # brdh（样本条码号，取自OBR第2列）
                    hl7_message.patient.name,       # brxm
                    hl7_message.patient.sex,        # brxb
                    '',                             # brly
                    hl7_message.patient.bed_no,     # ch
                    '',                             # nl
                    '1',                            # nldw
                    '',                             # sqh
                    jyrq,                           # sqsj
                    jyrq,                           # dysj
                    'N',                            # dybz
                    'N',                            # jgbz
                    'N',                            # bbbz
                    '',                             # zd
                    '',                             # bz
                    '',                             # bz1
                    '',                             # zjf
                    None,                           # cyrq
                    '',                             # bq
                    ''                              # czybh
                )
                self.logger.info("执行患者信息插入SQL:")
                self.logger.info(f"SQL: {pat_sql}")
                self.logger.info(f"参数: {pat_data}")
                cursor.execute(pat_sql, pat_data)
                self.logger.info("患者信息插入成功")
            
            # 保存检验结果到lis_result表
            for order in hl7_message.orders:
                # jyrq用OBR第7列 Observation Date/Time
                jyrq_raw = order.order_time
                if jyrq_raw and len(jyrq_raw) >= 8:
                    try:
                        jyrq_fmt = datetime.strptime(jyrq_raw[:14], '%Y%m%d%H%M%S') if len(jyrq_raw) >= 14 else datetime.strptime(jyrq_raw[:8], '%Y%m%d')
                        jyrq = jyrq_fmt.strftime('%Y-%m-%d 00:00:00.000')
                    except Exception:
                        jyrq = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
                else:
                    jyrq = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
                
                # --- 新增：如brdh不为空，查找当天lis_pat表有无相同brdh，有则用其ybh ---
                ybh = order.sample_id
                if order.order_id:
                    find_ybh_sql = f"SELECT ybh FROM {self.config['TABLE_MAPPING']['pat_table']} WHERE brdh=? AND jyrq=?"
                    cursor.execute(find_ybh_sql, (order.order_id, jyrq))
                    row = cursor.fetchone()
                    if row and row[0]:
                        ybh = row[0]
                        self.logger.info(f"通过brdh={order.order_id}查到ybh={ybh}，用于结果入库")
                
                for result in order.results:
                    # 查找xmdh
                    select_sql = "SELECT xmdh FROM xm_inter WHERE yq=? AND tdh=?"
                    cursor.execute(select_sql, (hl7_message.sending_facility, result.test_name))
                    row = cursor.fetchone()
                    xmdh = row[0] if row else result.test_name
                    if not row:
                        self.logger.warning(f"未在xm_inter表中找到yq={hl7_message.sending_facility}, tdh={result.test_name}的xmdh，使用原值。")
                    
                    # --- 新增：如已存在(jyrq, yq, ybh, xmdh)，则UPDATE，否则INSERT ---
                    check_result_sql = f"SELECT COUNT(1) FROM {self.config['TABLE_MAPPING']['result_table']} WHERE jyrq=? AND yq=? AND ybh=? AND xmdh=?"
                    cursor.execute(check_result_sql, (jyrq, hl7_message.sending_facility, ybh, xmdh))
                    exists_result = cursor.fetchone()[0]
                    
                    if exists_result:
                        update_sql = f"UPDATE {self.config['TABLE_MAPPING']['result_table']} SET csjg=?, result_flag=?, refs=?, num_result=? WHERE jyrq=? AND yq=? AND ybh=? AND xmdh=?"
                        update_data = (
                            str(result.value),
                            result.abnormal_flag,
                            result.reference_range,
                            str(result.value),
                            jyrq,
                            hl7_message.sending_facility,
                            ybh,
                            xmdh
                        )
                        self.logger.info(f"更新检验结果SQL: {update_sql}")
                        self.logger.info(f"参数: {update_data}")
                        cursor.execute(update_sql, update_data)
                        self.logger.info("检验结果更新成功")
                    else:
                        result_sql = f"""
                        INSERT INTO {self.config['TABLE_MAPPING']['result_table']} (
                            wyh, jyrq, yq, ybh, xmdh, csjg,instrid,od, operna,refs,
                            result_flag, bz1, bz2, textresult,num_result
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """
                        result_data = (
                            hl7_message.message_control_id,  # wyh
                            jyrq,                           # jyrq
                            hl7_message.sending_facility,   # yq
                            ybh,                            # ybh（用brdh查到的ybh或原始）
                            xmdh,                           # xmdh（通过xm_inter表查找）
                            str(result.value),              # csjg
                            '0',                            # instrid
                            '',                             # od
                            '仪器',                         # operna
                            result.reference_range,         # refs
                            result.abnormal_flag,           # result_flag
                            '',                             # bz1
                            '',                             # bz2
                            '',                             # textresult
                            str(result.value)               # num_result
                        )
                        self.logger.info("插入检验结果SQL:")
                        self.logger.info(f"SQL: {result_sql}")
                        self.logger.info(f"参数: {result_data}")
                        cursor.execute(result_sql, result_data)
                        self.logger.info("检验结果插入成功")
            
            # 确保提交事务
            self.db_conn.commit()
            self.logger.info("数据库事务已提交")
            
        except Exception as e:
            if cursor:
                self.db_conn.rollback()
            self.logger.error(f"保存数据到数据库时出错: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()

    def _load_processed_files(self):
        """加载已处理文件记录，记录每个文件的行数"""
        log_file = self.config['SYSTEM']['processed_files']
        if Path(log_file).exists():
            try:
                with open(log_file, 'r', encoding='utf-8') as f:
                    self.processed_files = json.load(f)
            except json.JSONDecodeError:
                self.processed_files = {}
        else:
            self.processed_files = {}

    def _save_processed_files(self):
        """保存已处理文件记录，包含行数信息"""
        with open(self.config['SYSTEM']['processed_files'], 'w', encoding='utf-8') as f:
            json.dump(self.processed_files, f, ensure_ascii=False, indent=2)

    def _get_file_line_count(self, file_path):
        """获取文件的行数"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return sum(1 for _ in f)
        except Exception as e:
            self.logger.error(f"获取文件行数失败 {file_path}: {str(e)}")
            return 0

    def _get_today_file(self):
        """获取今天的文件名"""
        return self.raw_dir / f"{datetime.now().strftime('%y%m%d')}.txt"

    def parse_hl7_message(self, message: str) -> HL7Message:
        """解析HL7消息为结构化数据（所有fields[x]访问加越界保护）"""
        try:
            # 打印原始消息用于调试
            self.logger.info("开始解析消息:")
            self.logger.info("-" * 50)
            self.logger.info(message)
            self.logger.info("-" * 50)
            
            # 清理消息：移除多余的空格和换行，确保每个段都在一行
            cleaned_message = []
            for line in message.split('\r'):
                line = line.strip()
                if line:  # 只忽略空行
                    cleaned_message.append(line)
                    self.logger.info(f"处理段: {line}")  # 打印每个处理后的段
            
            # 按行分割消息
            segments = cleaned_message
            self.logger.info(f"总共找到 {len(segments)} 个段")
            
            # 解析MSH段
            msh = segments[0].split('|')
            message_type = msh[8] if len(msh) > 8 else ''
            message_control_id = msh[9] if len(msh) > 9 else ''
            sending_facility = msh[3] if len(msh) > 3 else ''
            receiving_facility = msh[4] if len(msh) > 4 else ''
            message_time = msh[6] if len(msh) > 6 else ''
            
            # 解析PID段
            pid = None
            for segment in segments:
                if segment.startswith('PID|'):
                    pid = segment.split('|')
                    self.logger.info(f"找到PID段: {pid}")  # 打印找到的PID段
                    break
            
            if not pid:
                self.logger.warning("未找到PID段")
                pid = []
            
            patient = Patient(
                patient_id=pid[2] if len(pid) > 2 else "",
                sex=pid[8] if len(pid) > 8 else "",
                age=pid[31] if len(pid) > 31 else "",
                name=pid[5] if len(pid) > 5 else "",
                birth_date=pid[7] if len(pid) > 7 else "",
                department=pid[10] if len(pid) > 10 else "",
                bed_no=pid[11] if len(pid) > 11 else ""
            )
            
            # 解析OBR和OBX段
            orders = []
            current_order = None
            
            for segment in segments:
                fields = segment.split('|')
                segment_type = fields[0] if len(fields) > 0 else ''
                
                if segment_type == 'OBR':
                    if current_order:
                        orders.append(current_order)
                    
                    current_order = Order(
                        order_id=fields[2] if len(fields) > 2 else '',
                        sample_id=fields[3] if len(fields) > 3 else '',  # 样本编号
                        test_type=fields[4] if len(fields) > 4 else '',
                        order_time=fields[7] if len(fields) > 7 else '',  # OBR第7列 Observation Date/Time
                        report_time=fields[22] if len(fields) > 22 else '',
                        priority=fields[5] if len(fields) > 5 else "",
                        results=[]
                    )
                    
                elif segment_type == 'OBX' and current_order:
                    test_result = TestResult(
                        test_name=fields[3].split('^')[0] if len(fields) > 3 else '',
                        value=float(fields[4]) if len(fields) > 4 and fields[4] else 0.0,
                        unit=fields[5] if len(fields) > 5 else '',
                        reference_range=fields[6] if len(fields) > 6 else '',
                        abnormal_flag=fields[7] if len(fields) > 7 else '',
                        observation_time=fields[14] if len(fields) > 14 else '',
                        instrument_id=fields[18] if len(fields) > 18 else '',
                        reagent_lot=fields[19] if len(fields) > 19 else '',
                        reagent_expiry=fields[20] if len(fields) > 20 else '',
                        control_id=fields[21] if len(fields) > 21 else ''
                    )
                    current_order.results.append(test_result)
            
            if current_order:
                orders.append(current_order)
            
            # 打印解析结果用于调试
            self.logger.info("解析结果:")
            self.logger.info(f"消息类型: {message_type}")
            self.logger.info(f"患者信息: {patient}")
            self.logger.info(f"医嘱数量: {len(orders)}")
            
            return HL7Message(
                message_type=message_type,
                message_control_id=message_control_id,
                sending_facility=sending_facility,
                receiving_facility=receiving_facility,
                message_time=message_time,
                patient=patient,
                orders=orders
            )
            
        except Exception as e:
            self.logger.error(f"解析HL7消息时出错: {str(e)}")
            raise

    def format_message(self, hl7_message: HL7Message) -> str:
        """将HL7消息格式化为可读文本"""
        output = []
        output.append("=== HL7消息信息 ===")
        output.append(f"消息类型: {hl7_message.message_type}")
        output.append(f"消息控制ID: {hl7_message.message_control_id}")
        output.append(f"发送机构: {hl7_message.sending_facility}")
        output.append(f"接收机构: {hl7_message.receiving_facility}")
        output.append(f"消息时间: {hl7_message.message_time}")
        
        output.append("\n=== 患者信息 ===")
        output.append(f"患者ID: {hl7_message.patient.patient_id}")
        output.append(f"姓名: {hl7_message.patient.name}")
        output.append(f"性别: {hl7_message.patient.sex}")
        output.append(f"年龄: {hl7_message.patient.age}")
        output.append(f"出生日期: {hl7_message.patient.birth_date}")
        output.append(f"科室: {hl7_message.patient.department}")
        output.append(f"床号: {hl7_message.patient.bed_no}")
        
        for i, order in enumerate(hl7_message.orders, 1):
            output.append(f"\n=== 医嘱 {i} ===")
            output.append(f"医嘱ID: {order.order_id}")
            output.append(f"样本ID: {order.sample_id}")
            output.append(f"检验类型: {order.test_type}")
            output.append(f"优先级: {order.priority}")
            output.append(f"医嘱时间: {order.order_time}")
            output.append(f"报告时间: {order.report_time}")
            
            output.append("\n检验结果:")
            for result in order.results:
                output.append(f"  {result.test_name}:")
                output.append(f"    结果值: {result.value} {result.unit}")
                output.append(f"    参考范围: {result.reference_range}")
                output.append(f"    异常标志: {result.abnormal_flag}")
                output.append(f"    观察时间: {result.observation_time}")
                output.append(f"    仪器ID: {result.instrument_id}")
                output.append(f"    试剂批号: {result.reagent_lot}")
                output.append(f"    试剂有效期: {result.reagent_expiry}")
                output.append(f"    质控ID: {result.control_id}")
        
        return "\n".join(output)

    def _handle_client(self, client_socket, address):
        """处理客户端连接，支持结果上传和QRY^Q02样本项目查询"""
        self.logger.info(f"接受来自 {address} 的连接")
        buffer = b''

        try:
            while True:
                data = client_socket.recv(4096)
                if not data:
                    break

                buffer += data

                # 检查是否收到完整的消息（以VT开始，FS结束）
                while buffer.startswith(b'\x0b') and b'\x1c' in buffer:
                    end_index = buffer.index(b'\x1c') + 1
                    message = buffer[:end_index]  # 提取完整消息
                    buffer = buffer[end_index:]  # 剩余部分保留

                    if message.startswith(b'\x0b'):
                        message = message[1:]  # 移除VT
                        try:
                            # 解码消息
                            decoded_message = message.decode('utf-8')

                            # 判断消息类型
                            if 'ORU^R01' in decoded_message:
                                # 保存ORU^R01原始报文到raw目录
                                try:
                                    raw_dir = 'raw'
                                    os.makedirs(raw_dir, exist_ok=True)
                                    raw_file = os.path.join(raw_dir, f'{datetime.now():%y%m%d}.txt')
                                    with open(raw_file, 'ab') as f:
                                        f.write(b'\x0b' + message + b'\x1c\r')
                                    self.logger.info(f"ORU^R01原始报文已保存到: {raw_file}")
                                    
                                    # 打印原始消息内容
                                    self.logger.info("原始消息内容:")
                                    self.logger.info("-" * 50)
                                    self.logger.info(decoded_message)
                                    self.logger.info("-" * 50)
                                    
                                    # 立即解析消息并保存到数据库
                                    parsed_message = self.parse_hl7_message(decoded_message)
                                    
                                    # 打印解析后的消息内容
                                    self.logger.info("解析后的消息内容:")
                                    self.logger.info("-" * 50)
                                    self.logger.info(self.format_message(parsed_message))
                                    self.logger.info("-" * 50)
                                    
                                    self._save_to_database(parsed_message)
                                    self.logger.info("ORU^R01消息已解析并保存到数据库")
                                    
                                    # 格式化解析结果并保存
                                    formatted_output = self.format_message(parsed_message)
                                    output_filename = f"decoded_{datetime.now():%y%m%d}.txt"
                                    output_path = self.processed_dir / output_filename
                                    with open(output_path, 'a', encoding='utf-8') as f:
                                        f.write(f"\n{'='*80}\n")
                                        f.write(formatted_output)
                                        f.write(f"\n{'='*80}\n")
                                    self.logger.info(f"解析结果已追加至: {output_path}")
                                    
                                    # 更新processed_files记录
                                    file_key = str(raw_file)
                                    current_lines = self._get_file_line_count(raw_file)
                                    self.processed_files[file_key] = {
                                        "processed_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                                        "output_file": str(output_path),
                                        "line_count": current_lines
                                    }
                                    self._save_processed_files()
                                    
                                except Exception as e:
                                    self.logger.error(f"处理ORU^R01消息时出错: {str(e)}")
                                
                                # 发送ACK应答
                                ack = self._handle_oru_ack(decoded_message)
                                client_socket.send(ack)
                                self.logger.info("已应答ORU^R01样本结果上传请求")

                            elif 'QRY^Q02' in decoded_message:
                                # QRY^Q02等交互日志保存到log目录
                                try:
                                    log_dir = 'log'
                                    os.makedirs(log_dir, exist_ok=True)
                                    log_file = os.path.join(log_dir, f'{datetime.now():%y%m%d}.txt')
                                    with open(log_file, 'a', encoding='utf-8') as f:
                                        f.write(decoded_message + '\n')
                                    self.logger.info(f"QRY^Q02交互日志已保存到: {log_file}")
                                except Exception as e:
                                    self.logger.error(f"保存QRY^Q02交互日志到{log_file}失败: {e}")
                                response = self._handle_query(decoded_message)
                                client_socket.send(response)
                                self.logger.info("已应答QRY^Q02样本项目查询请求")
                                continue
                            else:
                                # 其他类型消息也保存到log目录
                                try:
                                    log_dir = 'log'
                                    os.makedirs(log_dir, exist_ok=True)
                                    log_file = os.path.join(log_dir, f'{datetime.now():%y%m%d}.txt')
                                    with open(log_file, 'a', encoding='utf-8') as f:
                                        f.write(decoded_message + '\n')
                                    self.logger.info(f"其他交互日志已保存到: {log_file}")
                                except Exception as e:
                                    self.logger.error(f"保存其他交互日志到{log_file}失败: {e}")

                        except UnicodeDecodeError:
                            self.logger.error("消息解码错误")
                            # 发送AR应答
                            try:
                                client_socket.send(self._build_ack_r01('', '', '', '', msa_code='AR', err_msg='消息解码错误', err_code='206'))
                            except Exception:
                                pass
                        except Exception as e:
                            self.logger.error(f"处理消息时出错: {str(e)}")
                            # 发送AR应答
                            try:
                                client_socket.send(self._build_ack_r01('', '', '', '', msa_code='AR', err_msg=str(e), err_code='206'))
                            except Exception:
                                pass

        except Exception as e:
            self.logger.error(f"处理客户端 {address} 时出错: {str(e)}")
        finally:
            client_socket.close()
            self.logger.info(f"关闭与 {address} 的连接")

    def _handle_query(self, message: str) -> bytes:
        """处理QRY^Q02样本项目查询请求，返回DSR^Q03或ACK^Q03应答（含详细调试日志）"""
        try:
            self.logger.info("收到QRY^Q02请求原始内容：\n" + message)
            # 解析QRY^Q02
            lines = [line for line in message.split('\r') if line.strip()]
            msh = lines[0].split('|')
            analyzer_id = msh[2]
            lis_id = msh[4]
            msg_time = msh[6]
            msg_id = msh[9]
            serial_number = msh[11] if len(msh) > 11 else ''
            # QRD段
            qrd = next((l for l in lines if l.startswith('QRD|')), None)
            if not qrd:
                self.logger.error("未找到QRD段，返回AR错误")
                return self._build_ack_q03(analyzer_id, lis_id, msg_time, msg_id, msa_code='AR', err_msg='无QRD段', err_code='206')
            qrd_fields = qrd.split('|')
            query_mode = qrd_fields[2]  # BC/SN
            sample_no = qrd_fields[8]
            rack_pos = qrd_fields[10]   # 4^1
            dilute = qrd_fields[11]     # N/Y
            self.logger.info(f"解析字段：query_mode={query_mode}, sample_no={sample_no}, rack_pos={rack_pos}, dilute={dilute}")
            # 查询主表
            today = datetime.now().strftime('%Y-%m-%d 00:00:00.000')
            conn = self.db_conn
            cursor = conn.cursor()
            if query_mode == 'BC':
                sql = "SELECT * FROM lis_pat WHERE brdh=? AND jyrq=?"
            else:
                sql = "SELECT * FROM lis_pat WHERE ybh=? AND jyrq=?"
            self.logger.info(f"主表查询SQL: {sql}")
            self.logger.info(f"主表查询参数: ({sample_no}, {today})")
            cursor.execute(sql, (sample_no, today))
            pat = cursor.fetchone()
            self.logger.info(f"主表查询结果: {pat}")
            if not pat:
                self.logger.warning("未查到主表数据，返回NF应答")
                return self._build_ack_q03(analyzer_id, lis_id, msg_time, msg_id, msa_code='NF')
            # 字段索引需根据实际表结构调整
            # 例如: brdh=10, bed_no=14, name=11, sex=12, age=15, ybh=3, yq=2, jyrq=1
            # 查询结果表
            result_sql = "SELECT xmdh FROM lis_result WHERE jyrq=? AND yq=? AND ybh=?"
            result_params = (pat[1], pat[2], pat[3])
            self.logger.info(f"结果表查询SQL: {result_sql}")
            self.logger.info(f"结果表查询参数: {result_params}")
            cursor.execute(result_sql, result_params)
            results = cursor.fetchall()
            self.logger.info(f"结果表查询结果: {results}")
            xmdh_list = [r[0] for r in results]
            # 组装DSR^Q03应答
            response_bytes = self._build_dsr_q03(analyzer_id, lis_id, msg_time, msg_id, pat, xmdh_list, rack_pos, dilute)
            try:
                self.logger.info(f"应答报文内容：\n{response_bytes.decode('utf-8', errors='replace')}")
            except Exception as e:
                self.logger.error(f"应答报文内容打印失败: {e}")
            return response_bytes
        except Exception as e:
            self.logger.error(f"QRY^Q02应答处理异常: {str(e)}")
            return self._build_ack_q03('','','','','AR',str(e),'206')

    def _build_dsr_q03(self, analyzer_id, lis_id, msg_time, msg_id, pat, xmdh_list, rack_pos, dilute) -> bytes:
        """组装DSR^Q03应答报文，DSP1和DSP2严格按协议示例输出"""
        now = datetime.now().strftime('%Y%m%d%H%M%S')
        print(f"pat: {pat}")
        # 字段索引需根据实际表结构调整
        brdh = pat[10] if len(pat) > 9 else ''      # 样本条码
        ybh = pat[4] if len(pat) > 10 else ''       # 样本编号
        bed_no = pat[14] if len(pat) > 14 else ''  # 床号
        name = pat[11] if len(pat) > 10 else ''    # 姓名
        #去掉性别包含的空格 性别的原始数据是1用M/2用F/3用O
        sex_raw = pat[12].strip()
        sex = 'M' if sex_raw == '1' else 'F' if sex_raw == '2' else 'O' if sex_raw == '3' else ''
        nl = pat[15] if len(pat) > 13 else ''      # 年龄
        #处理年龄单位如果是1则Y
        nldw = 'Y' if pat[16] == '1' else '岁' if pat[16] == '2' else ''
        ksdh = pat[7] if len(pat) > 6 else ''      # 科室
        # 检测时间、送检时间、报告时间都用now（可根据实际表结构调整）
        test_time = now
        send_time = now
        report_time = now
        # 组装报文
        msh = f"MSH|^~\\&|{analyzer_id}|I2900|{lis_id}||{now}||DSR^Q03|{msg_id}|P|2.3.1||||0||UNICODE|||"
        msa = f"MSA|OK|{msg_id}||||0|"
        dsp1 = f"DSP|1||{brdh}|{bed_no}|{name}|||{sex}|||||||||||||||||||||{nl}^{nldw}|"
        dsp2 = f"DSP|2|{brdh}|{ybh}|{analyzer_id}|N||{test_time}|N|1|{rack_pos}||{dilute}||{send_time}|0|||||||{ksdh}|{report_time}||||||||||||||||||||||||||"
        dsp3 = f"DSP|3|{len(xmdh_list)}|{'^'.join(xmdh_list)}|||"
        msg = f"\x0b{msh}\r{msa}\r{dsp1}\r{dsp2}\r{dsp3}\r\x1c\r"
        # 明确打印完整应答报文内容
        try:
            self.logger.info("完整应答报文：\n" + msg.replace('\r', '\n'))
        except Exception as e:
            self.logger.error(f"完整应答报文打印失败: {e}")
        return msg.encode('utf-8')

    def _build_ack_q03(self, analyzer_id, lis_id, msg_time, msg_id, msa_code='NF', err_msg='', err_code='') -> bytes:
        """组装ACK^Q03应答报文"""
        now = datetime.now().strftime('%Y%m%d%H%M%S')
        msh = f"MSH|^~\\&|{analyzer_id}|I2900|{lis_id}||{now}||ACK^Q03|{msg_id}|P|2.3.1||||0||UNICODE|||"
        msa = f"MSA|{msa_code}|{msg_id}|{err_msg}|||{err_code}|"
        msg = f"\x0b{msh}\r{msa}\r\x1c\r"
        return msg.encode('utf-8')

    def _handle_oru_ack(self, message: str) -> bytes:
        """处理ORU^R01消息，组装ACK^R01应答（不再校验年龄和单位）"""
        try:
            lines = [line for line in message.split('\r') if line.strip()]
            msh = lines[0].split('|')
            analyzer_id = msh[2] if len(msh) > 2 else ''
            lis_id = msh[4] if len(msh) > 4 else ''
            msg_time = msh[6] if len(msh) > 6 else ''
            msg_id = msh[9] if len(msh) > 9 else ''
            # 不再校验年龄和单位，直接返回AA
            return self._build_ack_r01(analyzer_id, lis_id, msg_time, msg_id, msa_code='AA')
        except Exception as e:
            return self._build_ack_r01('', '', '', '', msa_code='AR', err_msg=str(e), err_code='206')

    def _build_ack_r01(self, analyzer_id, lis_id, msg_time, msg_id, msa_code='AA', err_msg='', err_code='0') -> bytes:
        """组装ACK^R01应答报文"""
        now = datetime.now().strftime('%Y%m%d%H%M%S')
        msh = f"MSH|^~\\&|{analyzer_id}|I2900|{lis_id}||{now}||ACK^R01|{msg_id}|P|2.3.1||||0||UNICODE|||"
        msa = f"MSA|{msa_code}|{msg_id}|{err_msg}|||{err_code}|"
        msg = f"\x0b{msh}\r{msa}\r\x1c\r"
        try:
            self.logger.info("ORU^R01应答报文：\n" + msg.replace('\r', '\n'))
        except Exception as e:
            self.logger.error(f"ORU^R01应答报文打印失败: {e}")
        return msg.encode('utf-8')

    def _process_raw_files(self):
        """处理raw目录下的所有文件"""
        try:
            raw_dir = Path('raw')
            if not raw_dir.exists():
                return
            
            for file_path in raw_dir.glob('*.txt'):
                file_key = str(file_path)
                current_lines = self._get_file_line_count(file_path)
                
                # 检查文件是否已处理或是否有新行
                if file_key not in self.processed_files or \
                   self.processed_files[file_key].get('line_count', 0) < current_lines:
                    
                    self.logger.info(f"处理raw目录下的文件: {file_path}")
                    
                    # 读取文件内容
                    with open(file_path, 'rb') as f:  # 使用二进制模式读取
                        content = f.read()
                    
                    # 按消息分隔符分割消息
                    messages = content.split(b'\x1c')
                    
                    for message in messages:
                        if not message.strip():
                            continue
                        
                        # 移除消息开始标记
                        if message.startswith(b'\x0b'):
                            message = message[1:]
                        
                        try:
                            # 解码消息
                            decoded_message = message.decode('utf-8')
                            
                            # 打印原始消息内容
                            self.logger.info("原始消息内容:")
                            self.logger.info("-" * 50)
                            self.logger.info(decoded_message)
                            self.logger.info("-" * 50)
                            
                            # 解析消息
                            parsed_message = self.parse_hl7_message(decoded_message)
                            
                            # 打印解析后的消息内容
                            self.logger.info("解析后的消息内容:")
                            self.logger.info("-" * 50)
                            self.logger.info(self.format_message(parsed_message))
                            self.logger.info("-" * 50)
                            
                            # 保存到数据库
                            self._save_to_database(parsed_message)
                            self.logger.info(f"消息已保存到数据库")
                            
                            # 格式化解析结果并保存
                            formatted_output = self.format_message(parsed_message)
                            output_filename = f"decoded_{datetime.now():%y%m%d}.txt"
                            output_path = self.processed_dir / output_filename
                            with open(output_path, 'a', encoding='utf-8') as f:
                                f.write(f"\n{'='*80}\n")
                                f.write(formatted_output)
                                f.write(f"\n{'='*80}\n")
                            self.logger.info(f"解析结果已追加至: {output_path}")
                            
                        except Exception as e:
                            self.logger.error(f"处理消息时出错: {str(e)}")
                            continue
                    
                    # 更新处理记录
                    self.processed_files[file_key] = {
                        "processed_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "output_file": str(output_path),
                        "line_count": current_lines
                    }
                    self._save_processed_files()
                    
                    self.logger.info(f"文件 {file_path} 处理完成")
                    
        except Exception as e:
            self.logger.error(f"处理raw目录文件时出错: {str(e)}")

    def _monitor_files(self):
        """监控文件变化"""
        while True:
            try:
                # 处理raw目录下的所有文件
                self._process_raw_files()
                time.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                self.logger.error(f"监控文件时出错: {str(e)}")
                time.sleep(5)

    def start(self):
        """启动LIS系统"""
        # 启动时处理raw目录下的文件
        self._process_raw_files()
        
        # 启动文件监控线程
        monitor_thread = threading.Thread(target=self._monitor_files)
        monitor_thread.daemon = True
        monitor_thread.start()
        self.logger.info("文件监控器已启动")
        
        # 启动服务器
        server = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        
        try:
            server.bind((self.host, self.port))
            server.listen(5)
            self.logger.info(f"服务器启动，监听 {self.host}:{self.port}")
            
            while True:
                client_socket, address = server.accept()
                client_thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket, address)
                )
                client_thread.daemon = True
                client_thread.start()
                
        except Exception as e:
            self.logger.error(f"服务器错误: {str(e)}")
        finally:
            server.close()
            self.db_conn.close()

def run_lis():
    lis = LISSystem()
    lis.start()

def create_image():
    # 创建一个简单的托盘图标
    image = Image.new('RGB', (64, 64), color1 := (0, 128, 255))
    d = ImageDraw.Draw(image)
    d.rectangle([16, 16, 48, 48], fill=(255, 255, 255))
    return image

def on_exit(icon, item):
    icon.stop()
    sys.exit(0)

def main():
    # 启动LIS服务线程
    t = threading.Thread(target=run_lis, daemon=True)
    t.start()

    # 托盘菜单
    menu = pystray.Menu(pystray.MenuItem('退出', on_exit))
    icon = pystray.Icon("LIS系统", create_image(), "LIS系统", menu)
    icon.run()

if __name__ == "__main__":
    main()