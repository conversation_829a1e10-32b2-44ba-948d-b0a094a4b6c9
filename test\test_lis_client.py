#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
LIS系统测试客户端
用于测试HL7消息的发送和接收
"""

import socket
import time
import sys
import os
import ctypes
from ctypes import wintypes

# Windows控制台模式常量
ENABLE_QUICK_EDIT_MODE = 0x0040
ENABLE_INSERT_MODE = 0x0020
ENABLE_MOUSE_INPUT = 0x0010
STD_INPUT_HANDLE = -10

def send_hl7_message(server='127.0.0.1', port=22010, message=''):
    """发送HL7消息到LIS系统"""
    try:
        # 创建socket连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        print(f"正在连接到 {server}:{port}...")
        sock.connect((server, port))
        print("连接成功！")
        
        # 将换行符转换为HL7标准的\r分隔符
        hl7_message = message.replace('\n', '\r')
        
        # 构造完整的HL7消息（添加开始和结束标记）
        full_message = b'\x0b' + hl7_message.encode('utf-8') + b'\x1c\r'
        
        print("发送消息:")
        print(message)
        print(f"\n转换后的HL7消息 (\\r分隔):")
        print(repr(hl7_message))
        print("\n" + "="*50)
        
        # 发送消息
        sock.sendall(full_message)
        print("消息已发送，等待响应...")
        
        # 接收响应
        response = sock.recv(4096)
        if response:
            # 移除控制字符并格式化输出
            clean_response = response.decode('utf-8', errors='ignore')
            clean_response = clean_response.replace('\x0b', '').replace('\x1c', '').replace('\r', '\n')
            print("\n收到响应:")
            print(clean_response)
            return clean_response
        else:
            print("未收到响应")
            return None
            
    except socket.timeout:
        print("连接超时")
        return None
    except ConnectionRefusedError:
        print("连接被拒绝，请检查服务器是否运行")
        return None
    except Exception as e:
        print(f"发生错误: {e}")
        return None
    finally:
        try:
            sock.close()
        except:
            pass

def test_oru_r01():
    """测试ORU^R01消息（您提供的测试数据）"""
    message = """MSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164726||ORU^R01|4|P|2.3.1|Z80005000310|||0||UNICODE|||
PID|357|||||||O|||||||||||||||||||||||0^Y|
OBR|1319|285025671|13|I2900|N||20250610164720||1|029^1||N||20250610162315|0|||||||20250610164726||||||||||||||||||||||||||
OBX|NM|1|AFP^1|2.91|ng/mL|0.00-10.00|N||F||||20250610164719|||13924|20250301|5378|20250402|2799|20250402|2084|"""
    
    print("=== 测试ORU^R01消息（原始数据 - 患者信息为空）===")
    return send_hl7_message(message=message)

def test_oru_r01_with_patient():
    """测试包含完整患者信息的ORU^R01消息"""
    message = """MSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164726||ORU^R01|5|P|2.3.1|Z80005000310|||0||UNICODE|||
PID|1||P123456||张^三^先生||19850315|M||汉族|内科病房^A^301||13800138000||||||||||||||||||30^Y|
OBR|1|B285025671|P123456|I2900|N||20250610164720||1|029^1||N||20250610162315|0|||||||20250610164726||||||||||||||||||||||||||
OBX|NM|1|AFP^1|2.91|ng/mL|0.00-10.00|N||F||||20250610164719|||13924|20250301|5378|20250402|2799|20250402|2084|"""
    
    print("=== 测试ORU^R01消息（包含完整患者信息）===")
    return send_hl7_message(message=message)

def test_simple_oru():
    """测试简单的ORU^R01消息"""
    message = """MSH|^~\\&|TestAnalyzer|TestLab|LIS||20250610120000||ORU^R01|123|P|2.3.1|||0||UNICODE|||
PID|1||12345||张^三||19900101|M|||||||||||||||||||||||30^Y|
OBR|1|TEST001|TEST001|CBC|N||20250610120000||1|||N||20250610120000|||||||||||||||||||||||||||||
OBX|NM|1|WBC^白细胞计数|7.5|10^9/L|4.0-10.0|N||F||||20250610120000|||||||
OBX|NM|2|RBC^红细胞计数|4.5|10^12/L|4.0-5.5|N||F||||20250610120000|||||||"""
    
    print("=== 测试简单ORU^R01消息 ===")
    return send_hl7_message(message=message)

def test_qry_q02():
    """测试QRY^Q02消息"""
    message = """MSH|^~\\&|Analyzer id|I2901|LIS ID||20250610164726||QRY^Q02|1|P|2.3.1|||0||UNICODE|||
QRD|20250610164726|R|I|||10^RD|285025671|||BC|4^1|N||||||"""
    
    print("=== 测试QRY^Q02消息 ===")
    return send_hl7_message(message=message)

def disable_console_quick_edit():
    """禁用Windows控制台的快速编辑模式，避免程序意外暂停"""
    try:
        # 只在Windows系统上执行
        if os.name != 'nt':
            return
        
        # 获取标准输入句柄
        kernel32 = ctypes.windll.kernel32
        h_stdin = kernel32.GetStdHandle(STD_INPUT_HANDLE)
        
        if h_stdin == -1:  # INVALID_HANDLE_VALUE
            return
        
        # 获取当前控制台模式
        mode = wintypes.DWORD()
        if not kernel32.GetConsoleMode(h_stdin, ctypes.byref(mode)):
            return
        
        # 移除快速编辑模式、插入模式和鼠标输入
        new_mode = mode.value
        new_mode &= ~ENABLE_QUICK_EDIT_MODE  # 移除快速编辑模式
        new_mode &= ~ENABLE_INSERT_MODE      # 移除插入模式
        new_mode &= ~ENABLE_MOUSE_INPUT      # 移除鼠标输入
        
        # 设置新的控制台模式
        kernel32.SetConsoleMode(h_stdin, new_mode)
        print("已禁用控制台快速编辑模式，避免程序意外暂停")
        
    except Exception as e:
        print(f"设置控制台模式时出错: {e}")
        # 即使失败也不影响程序继续运行

def main():
    """主函数"""
    # 禁用控制台快速编辑模式
    disable_console_quick_edit()
    
    print("LIS系统测试客户端")
    print("==================")
    
    while True:
        print("\n请选择测试类型:")
        print("1. 测试您提供的ORU^R01数据（患者信息为空）")
        print("2. 测试包含完整患者信息的ORU^R01数据")
        print("3. 测试简单的ORU^R01数据") 
        print("4. 测试QRY^Q02查询")
        print("5. 自定义消息")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == '0':
            print("程序结束")
            break
        elif choice == '1':
            test_oru_r01()
        elif choice == '2':
            test_oru_r01_with_patient()
        elif choice == '3':
            test_simple_oru()
        elif choice == '4':
            test_qry_q02()
        elif choice == '5':
            print("请输入自定义HL7消息（以空行结束）:")
            lines = []
            while True:
                line = input()
                if not line:
                    break
                lines.append(line)
            
            if lines:
                custom_message = '\r'.join(lines)
                print("=== 测试自定义消息 ===")
                send_hl7_message(message=custom_message)
            else:
                print("未输入消息内容")
        else:
            print("无效的选择，请重试")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main() 