# YQ自定义配置使用指南

## 概述

YQ（仪器标识）自定义功能允许您为不同设备配置不同的YQ值处理策略，实现更灵活的设备管理和数据隔离。

## 配置方式

### 1. 自定义YQ值模式 (custom)

**适用场景**: 希望为特定设备固定使用某个YQ值，不管消息中的原始值是什么。

```ini
[DEVICE_yingkai_lab1]
yq_source = custom
yq_value = I2900
```

**效果**: 不管HL7消息MSH段第3字段是什么值，数据库中的YQ字段都会使用 `I2900`。

### 2. 设备ID模式 (device_id)

**适用场景**: 希望使用设备ID作为YQ值，可以添加前缀。

```ini
[DEVICE_yingkai_lab2]
yq_source = device_id
yq_prefix = LAB2_
```

**效果**: YQ值会是 `LAB2_yingkai_lab2`

### 3. 映射模式 (mapping)

**适用场景**: 根据消息中的原始YQ值映射到新的值。

```ini
[DEVICE_abbott_lab1]
yq_source = mapping
yq_mapping = {"ABBOTT": "ABT001", "iSTAT": "ABT002", "AUTO": "ABT_AUTO"}
```

**效果**:
- 原始YQ为"ABBOTT" → 数据库中使用"ABT001"
- 原始YQ为"iSTAT" → 数据库中使用"ABT002"
- 原始YQ为"MANUAL" → 数据库中使用"MANUAL"（未映射的保持原值）

### 4. 消息模式 (message) - 默认

**适用场景**: 使用消息中的原始YQ值，这是默认行为。

```ini
[DEVICE_default]
yq_source = message
# 或者不配置yq_source字段
```

**效果**: 直接使用HL7消息MSH段第3字段的值。

## 完整配置示例

```ini
# 多设备支持的LIS系统配置文件

[SYSTEM]
multi_device_enabled = true

[DEVICES]
configured = lab1,lab2,abbott1,roche1

[DEVICE_lab1]
device_type = YingKai_LIS
manufacturer = YingKai Bio
model = LIS_V112
protocol = HL7
adapter = yingkai
ip_range = *************-*************
ports = 22010
message_patterns = MSH|,OBR|,OBX|
priority = 10
# 自定义YQ配置
yq_source = custom
yq_value = I2900
enabled = true

[DEVICE_lab2] 
device_type = YingKai_LIS
manufacturer = YingKai Bio
model = LIS_V111
protocol = HL7
adapter = yingkai
ip_range = *************-*************
ports = 22010
message_patterns = MSH|,OBR|,OBX|
priority = 20
# 设备ID模式YQ配置
yq_source = device_id
yq_prefix = LAB2_
enabled = true

[DEVICE_abbott1]
device_type = Abbott_iSTAT
manufacturer = Abbott
model = i-STAT_1
protocol = ASTM
adapter = abbott
ip_range = *************-*************
ports = 23000
message_patterns = H|.*ABBOTT,P|,O|,R|
priority = 30
# 映射模式YQ配置
yq_source = mapping
yq_mapping = {"ABBOTT": "ABT001", "iSTAT": "ABT002"}
enabled = true

[DEVICE_roche1]
device_type = Roche_cobas
manufacturer = Roche
model = cobas_6000
protocol = Custom
adapter = roche
ip_range = *************-*************
ports = 24000
message_patterns = ROCHE,^STX,^ETX
priority = 40
# 默认模式（使用消息中的原始值）
yq_source = message
enabled = true
```

## 实际效果

### 数据库操作影响

所有涉及YQ字段的数据库操作都会使用处理后的YQ值：

1. **患者表查询**: `WHERE brdh=? AND yq=? AND jyrq=?`
2. **结果表查询**: `WHERE jyrq=? AND yq=? AND ybh=? AND xmdh=?`
3. **项目映射**: `SELECT xmdh FROM xm_inter WHERE yq=? AND tdh=?`
4. **数据插入**: 所有新插入的记录都使用有效YQ值

### 日志输出

系统会记录YQ值的处理过程：

```
2025-08-03 10:15:30 - INFO - 使用自定义YQ值: I2900 (原始值: AUTO)
2025-08-03 10:16:45 - INFO - 使用设备ID作为YQ值: LAB2_yingkai_lab2
2025-08-03 10:17:20 - INFO - YQ映射: ABBOTT -> ABT001
```

## 测试验证

运行测试文件验证YQ自定义功能：

```bash
python test_yq_custom.py
```

## 注意事项

1. **向后兼容**: 未配置YQ自定义的设备仍使用原始消息中的YQ值
2. **配置优先级**: 设备级YQ配置 > 适配器级配置 > 消息原始值
3. **数据一致性**: 确保YQ值在整个系统中保持一致，避免数据查询问题
4. **映射表格式**: mapping配置使用JSON格式，注意引号和逗号
5. **字段长度**: 确保自定义的YQ值符合数据库字段长度限制

## 故障排查

如果YQ自定义不生效，请检查：

1. 配置文件语法是否正确
2. 设备识别是否正常工作
3. 日志中是否有YQ处理相关的信息
4. 多设备模式是否已启用 (`multi_device_enabled = true`)