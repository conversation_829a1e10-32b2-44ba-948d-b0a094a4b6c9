#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web测试界面启动器
简化的启动脚本，避免编码问题
"""

import subprocess
import sys
import time
import webbrowser
from threading import Timer

def open_browser():
    """5秒后自动打开浏览器"""
    try:
        webbrowser.open('http://localhost:5000')
        print("已自动打开浏览器")
    except:
        print("请手动访问: http://localhost:5000")

def main():
    print("=" * 50)
    print("URIT设备LIS系统Web测试界面")
    print("=" * 50)
    print()
    print("正在启动Web服务器...")
    print("访问地址: http://localhost:5000")
    print()
    print("功能说明:")
    print("1. 系统初始化 - 加载URIT适配器和配置")
    print("2. 消息识别测试 - 测试URIT消息识别能力")
    print("3. 消息解析测试 - 解析HL7消息内容")
    print("4. 样本ID处理 - 测试样本ID截取和处理")
    print("5. YQ自定义测试 - 测试仪器标识自定义")
    print("6. 数据库模拟 - 模拟数据库操作SQL")
    print("7. 完整流程测试 - 端到端功能验证")
    print("8. 系统统计 - 查看系统运行状态")
    print()
    print("5秒后将自动打开浏览器...")
    print("按Ctrl+C停止服务器")
    print("=" * 50)
    
    # 5秒后打开浏览器
    Timer(5.0, open_browser).start()
    
    # 启动Flask应用
    try:
        from test_web_interface import app
        app.run(host='127.0.0.1', port=5000, debug=False, use_reloader=False)
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        print("请确保:")
        print("1. 端口5000未被占用")
        print("2. 已安装Flask: pip install flask")
        print("3. 所有必要文件存在")

if __name__ == '__main__':
    main()