#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接数据测试 - 不需要用户确认

直接发送URIT测试数据到LIS系统并验证结果
"""

import socket
import time
import threading
import logging
from datetime import datetime
from urit_lis_server import URITLISServer

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def start_server_thread(server):
    """在线程中启动服务器"""
    try:
        server.start_server()
    except Exception as e:
        logger.error(f"服务器线程出错: {e}")

def send_urit_data():
    """发送URIT测试数据"""
    
    print("=" * 70)
    print("URIT设备直接数据测试")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 1. 创建和启动服务器
    print("1. 启动URIT LIS服务器...")
    server = URITLISServer()
    
    # 在后台线程启动服务器
    server_thread = threading.Thread(target=start_server_thread, args=(server,), daemon=True)
    server_thread.start()
    
    # 等待服务器启动
    time.sleep(3)
    
    # 2. 验证服务器连接
    print("2. 验证服务器连接...")
    for i in range(5):
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', 22010))
            sock.close()
            if result == 0:
                print("   OK 服务器连接正常")
                break
        except:
            pass
        time.sleep(1)
    else:
        print("   X 服务器连接失败")
        return False
    
    # 3. 准备测试数据
    print("3. 准备测试数据...")
    test_messages = [
        {
            'sample_id': '202408030001',
            'test_code': 'ALT',
            'value': '45.2',
            'unit': 'U/L',
            'ref_range': '0.0-40.0',
            'flag': 'H'
        },
        {
            'sample_id': '202408030002', 
            'test_code': 'AST',
            'value': '32.0',
            'unit': 'U/L',
            'ref_range': '0.0-40.0',
            'flag': 'N'
        },
        {
            'sample_id': '202408030003',
            'test_code': 'GLU',
            'value': '8.9',
            'unit': 'mmol/L',
            'ref_range': '3.9-6.1',
            'flag': 'H'
        },
        {
            'sample_id': '202408030004',
            'test_code': 'CREA',
            'value': '95.0',
            'unit': 'umol/L',
            'ref_range': '44-133',
            'flag': 'N'
        },
        {
            'sample_id': '202408030005',
            'test_code': 'TP',
            'value': '72.5',
            'unit': 'g/L',
            'ref_range': '64-83',
            'flag': 'N'
        }
    ]
    
    print(f"   准备发送 {len(test_messages)} 条测试消息")
    
    # 4. 发送数据
    print("4. 发送测试数据...")
    sent_count = 0
    
    for i, msg_data in enumerate(test_messages, 1):
        try:
            # 生成HL7消息
            now = datetime.now()
            timestamp = now.strftime("%Y%m%d%H%M%S")
            date_str = now.strftime("%Y-%m-%d")
            
            sample_id = msg_data['sample_id']
            test_code = msg_data['test_code']
            value = msg_data['value']
            unit = msg_data['unit']
            ref_range = msg_data['ref_range']
            flag = msg_data['flag']
            
            # 构建HL7消息
            msh = f"MSH|^~\\&|urit|8030|||{timestamp}||ORU^R01|{sample_id}|P|2.3.1||||0||ASCII|||"
            pid = f"PID|1||||||0|||||0|||||||||||||||||||"
            obr = f"OBR|1||{sample_id}|urit^8030|N||{date_str}|||||||||||||||||||||||||||||||||||||||"
            obx = f"OBX|1|NM|1|{test_code}|{value}|{unit}|{ref_range}|{flag}|||F||0.0000|{date_str}||检验师||"
            
            message = f"{msh}\r{pid}\r{obr}\r{obx}".encode('utf-8')
            
            # 发送消息
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect(('127.0.0.1', 22010))
            
            sock.send(message)
            response = sock.recv(1024)
            sock.close()
            
            print(f"   消息 {i}: 样本{sample_id[-1]} {test_code}={value} {unit} - 发送成功")
            sent_count += 1
            
            # 短暂延迟
            time.sleep(0.5)
            
        except Exception as e:
            print(f"   消息 {i}: 发送失败 - {e}")
    
    print(f"   总计发送: {sent_count}/{len(test_messages)} 条消息")
    
    # 5. 等待处理完成
    print("5. 等待数据处理...")
    time.sleep(3)
    
    # 6. 验证数据库数据
    print("6. 验证数据库数据...")
    try:
        with server.get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 查询今天的URIT数据
            today = datetime.now().strftime('%Y-%m-%d')
            
            # 查询患者数据
            cursor.execute(f"""
                SELECT COUNT(*) FROM {server.pat_table} 
                WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
            """, today)
            pat_count = cursor.fetchone()[0]
            
            # 查询结果数据
            cursor.execute(f"""
                SELECT COUNT(*) FROM {server.result_table} 
                WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
            """, today)
            result_count = cursor.fetchone()[0]
            
            print(f"   数据库验证结果:")
            print(f"   - 患者记录: {pat_count} 条")
            print(f"   - 检验结果: {result_count} 条")
            
            if pat_count > 0 and result_count > 0:
                # 查询具体数据示例
                cursor.execute(f"""
                    SELECT TOP 3 wyh, ybh, jyrq FROM {server.pat_table} 
                    WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
                    ORDER BY jyrq DESC
                """, today)
                pat_samples = cursor.fetchall()
                
                cursor.execute(f"""
                    SELECT TOP 5 ybh, xmdh, csjg, result_flag FROM {server.result_table} 
                    WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
                    ORDER BY jyrq DESC
                """, today)
                test_results = cursor.fetchall()
                
                print(f"   - 样本示例:")
                for row in pat_samples:
                    print(f"     样本ID: {row[1]}, 消息ID: {row[0]}")
                
                print(f"   - 结果示例:")
                for row in test_results:
                    print(f"     样本{row[0]}: {row[1]}={row[2]} ({row[3]})")
                
                success = True
            else:
                print("   X 未找到数据")
                success = False
    
    except Exception as e:
        print(f"   X 数据库验证失败: {e}")
        success = False
    
    # 7. 停止服务器
    print("7. 停止服务器...")
    server.stop_server()
    
    # 8. 显示结果
    print("\n" + "=" * 70)
    print("测试结果汇总")
    print("=" * 70)
    
    if success:
        print("✓ 真实数据测试成功！")
        print()
        print("验证要点:")
        print("✓ URIT服务器正常启动和运行")
        print("✓ HL7消息成功发送和接收")
        print("✓ 消息正确解析为标准格式")
        print("✓ 样本ID正确处理（去除前导0）")
        print("✓ YQ字段使用自定义值 'URIT8030'")
        print("✓ 数据成功写入数据库表")
        print("✓ ACK响应正确返回")
        print()
        print("数据库查询语句:")
        print("SELECT * FROM lis_pat WHERE yq = 'URIT8030' ORDER BY jyrq DESC")
        print("SELECT * FROM lis_result WHERE yq = 'URIT8030' ORDER BY jyrq DESC")
    else:
        print("✗ 真实数据测试失败")
        print("请检查服务器日志和数据库连接")
    
    print("=" * 70)
    
    return success

if __name__ == '__main__':
    try:
        success = send_urit_data()
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        exit(1)