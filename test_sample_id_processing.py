#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
样本ID处理功能测试

专门测试URIT设备样本ID的截取和处理功能
"""

def process_sample_id(raw_sample_id: str) -> str:
    """处理样本ID：截取后4位并去除前导0
    
    Args:
        raw_sample_id: 原始样本ID
        
    Returns:
        处理后的样本ID
    """
    if not raw_sample_id:
        return raw_sample_id
    
    try:
        # 截取后4位
        last_4_digits = raw_sample_id[-4:]
        # 转换为整数再转回字符串，自动去除前导0
        processed_id = str(int(last_4_digits))
        return processed_id
    except (ValueError, IndexError):
        return raw_sample_id

def test_sample_id_processing():
    """测试样本ID处理功能"""
    print("=== 样本ID处理功能测试 ===\n")
    
    # 测试用例
    test_cases = [
        # (原始样本ID, 预期结果, 说明)
        ("201801010001", "1", "您的实际数据 - 消息1"),
        ("201801010002", "2", "您的实际数据 - 消息2"),
        ("201801010123", "123", "中间数值"),
        ("201801010000", "0", "全零情况"),
        ("201801015678", "5678", "无前导零"),
        ("999999990099", "99", "其他长度"),
        ("12340567", "567", "较短ID"),
        ("0001", "1", "已经是4位"),
        ("", "", "空字符串"),
        ("ABC", "ABC", "非数字（异常情况）"),
        ("12A4", "12A4", "包含字母（异常情况）")
    ]
    
    print("| 原始样本ID | 预期结果 | 实际结果 | 处理过程 | 状态 |")
    print("|-----------|----------|----------|----------|------|")
    
    all_passed = True
    
    for original, expected, description in test_cases:
        try:
            actual = process_sample_id(original)
            status = "✓ 通过" if actual == expected else "✗ 失败"
            
            if actual != expected:
                all_passed = False
            
            # 显示处理过程
            if original and len(original) >= 4 and original[-4:].isdigit():
                last_4 = original[-4:]
                process_desc = f"{original} -> {last_4} -> {actual}"
            else:
                process_desc = f"{original} -> {actual}"
            
            print(f"| {original:<11} | {expected:<8} | {actual:<8} | {process_desc:<20} | {status} |")
            
        except Exception as e:
            print(f"| {original:<11} | {expected:<8} | ERROR | {str(e):<20} | ✗ 异常 |")
            all_passed = False
    
    print("\n" + "="*70)
    if all_passed:
        print("🎉 所有样本ID处理测试通过！")
    else:
        print("❌ 部分测试失败，请检查实现")
    
    # 特殊说明
    print("\n特殊情况说明:")
    print("1. 您的URIT设备样本ID: 201801010001 -> 1, 201801010002 -> 2")
    print("2. 系统自动截取后4位数字并去除前导0")
    print("3. 非数字或长度不足的情况保持原值不变")
    print("4. 处理后的样本ID将作为数据库中的ybh字段值")

def demonstrate_real_usage():
    """演示实际使用场景"""
    print("\n=== 实际使用场景演示 ===\n")
    
    # 模拟您的URIT消息
    sample_messages = [
        {
            'original_message': 'OBR|1||201801010001|urit^8030|N||2018-01-01',
            'sample_id': '201801010001',
            'description': '第一条URIT消息'
        },
        {
            'original_message': 'OBR|1||201801010002|urit^8030|N||2018-01-01',
            'sample_id': '201801010002',
            'description': '第二条URIT消息'
        }
    ]
    
    print("处理您的实际URIT消息:")
    print("-" * 50)
    
    for i, msg in enumerate(sample_messages, 1):
        original_id = msg['sample_id']
        processed_id = process_sample_id(original_id)
        
        print(f"消息 {i}: {msg['description']}")
        print(f"  OBR段: {msg['original_message']}")
        print(f"  原始样本ID: {original_id}")
        print(f"  处理后样本ID: {processed_id}")
        print(f"  数据库ybh字段: '{processed_id}'")
        print()
    
    print("数据库插入效果:")
    print("lis_pat表: ybh='1', ybh='2'")
    print("lis_result表: ybh='1', ybh='2'")

if __name__ == "__main__":
    test_sample_id_processing()
    demonstrate_real_usage()