#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
YingKai Bio LIS Protocol V1.12 适配器

该适配器实现了对YingKai Bio LIS系统的HL7协议支持，
包括ORU^R01结果上传和QRY^Q02样本查询两种消息类型的处理。

支持的功能：
- HL7 ORU^R01 消息解析和处理
- HL7 QRY^Q02 查询消息解析和响应
- 自动消息类型识别
- 数据验证和错误处理
- ACK/NAK 响应生成
"""

import re
from datetime import datetime
from typing import Dict, Any, List
from device_adapter import (
    ProtocolAdapter, StandardMessage, StandardPatient, 
    StandardOrder, StandardTestResult, DeviceInfo,
    ProtocolParseError, safe_get_field, format_datetime,
    clean_string
)

class YingKaiHL7Adapter(ProtocolAdapter):
    """YingKai Bio LIS Protocol V1.12 适配器"""
    
    @property
    def protocol_name(self) -> str:
        return "YingKai_HL7"
    
    @property
    def supported_versions(self) -> List[str]:
        return ["1.12", "1.11", "1.10"]
    
    @property
    def manufacturer(self) -> str:
        return "YingKai Bio"
    
    def _init_adapter(self):
        """初始化YingKai适配器特定配置"""
        self.field_length_limits = self.config.get('field_length_limits', {
            'patient_id': 50,
            'patient_name': 100,
            'sample_id': 50,
            'test_code': 20,
            'test_value': 200,
            'test_unit': 20
        })
        
        # HL7特殊字符映射
        self.hl7_special_chars = {
            '\\F\\': '|',    # 字段分隔符
            '\\S\\': '^',    # 组件分隔符  
            '\\T\\': '&',    # 转义字符
            '\\R\\': '~',    # 重复分隔符
            '\\E\\': '\\',   # 转义字符
        }
    
    def can_handle(self, message: bytes, connection_info: Dict[str, Any]) -> bool:
        """检查是否为YingKai HL7消息"""
        try:
            text = message.decode('utf-8', errors='ignore')
            
            # 检查HL7消息特征
            hl7_indicators = [
                'MSH|',      # HL7消息头
                'OBR|',      # 观察请求
                'OBX|',      # 观察结果  
                'QRD|',      # 查询定义
                'ORU^R01',   # 结果消息
                'QRY^Q02'    # 查询消息
            ]
            
            return any(indicator in text for indicator in hl7_indicators)
            
        except Exception:
            return False
    
    def parse_message(self, message: bytes, connection_info: Dict[str, Any]) -> StandardMessage:
        """解析YingKai HL7消息"""
        try:
            text = message.decode('utf-8', errors='replace')
            
            # 创建设备信息
            device_info = self._create_device_info(connection_info)
            
            # 提取消息类型
            message_type = self._extract_message_type(text)
            
            self.logger.info(f"解析YingKai消息类型: {message_type}")
            
            if message_type == "QRY":
                return self._parse_qry_message(text, device_info, message)
            elif message_type == "ORU":
                return self._parse_oru_message(text, device_info, message)
            else:
                raise ProtocolParseError(f"不支持的YingKai消息类型: {message_type}")
                
        except Exception as e:
            self.logger.error(f"解析YingKai消息失败: {e}")
            raise ProtocolParseError(f"YingKai消息解析失败: {str(e)}")
    
    def _create_device_info(self, connection_info: Dict[str, Any]) -> DeviceInfo:
        """创建设备信息对象"""
        address = connection_info.get('address', ('unknown', 0))
        if isinstance(address, (list, tuple)) and len(address) >= 2:
            ip_address = str(address[0])
            port = int(address[1]) if len(address) > 1 else 0
        else:
            ip_address = "unknown"
            port = 0
        
        return DeviceInfo(
            device_id=f"yingkai_{ip_address}_{port}",
            device_type="YingKai_LIS",
            manufacturer="YingKai Bio",
            model="LIS",
            protocol="HL7",
            version="2.5",
            ip_address=ip_address,
            port=port,
            metadata={'adapter': 'yingkai'}
        )
    
    def _extract_message_type(self, text: str) -> str:
        """提取消息类型"""
        # QRY^Q02 查询
        if 'QRD|' in text:
            return "QRY"
        # ORU^R01 结果
        elif 'MSH|' in text and 'ORU^R01' in text:
            return "ORU"
        elif 'OBR|' in text or 'OBX|' in text:
            return "ORU"  # 简化的结果消息
        else:
            return "UNKNOWN"
    
    def _parse_qry_message(self, text: str, device_info: DeviceInfo, raw_message: bytes) -> StandardMessage:
        """解析QRY^Q02查询消息
        
        QRY消息格式示例:
        QRD|timestamp|R|I|||10^RD|sample_number|||BC|rack_position|dilution_flag|
        """
        try:
            lines = text.split('\r')
            qrd_line = None
            
            # 查找QRD段
            for line in lines:
                if line.startswith('QRD|'):
                    qrd_line = line
                    break
            
            if not qrd_line:
                raise ProtocolParseError("QRY消息中未找到QRD段")
            
            # 解析QRD段
            fields = qrd_line.split('|')
            
            # 提取查询信息
            timestamp = safe_get_field(fields, 1)
            query_priority = safe_get_field(fields, 2, 'R')
            query_id = safe_get_field(fields, 3, 'I')
            sample_number = safe_get_field(fields, 6, '').split('^')[0]  # 格式: 10^RD
            query_mode = safe_get_field(fields, 10, 'BC')  # BC或SN模式
            rack_position = safe_get_field(fields, 11)
            dilution_flag = safe_get_field(fields, 12)
            
            # 创建患者信息（查询消息中患者信息有限）
            patient = StandardPatient(
                patient_id="",
                sample_id=sample_number,
                metadata={
                    'query_mode': query_mode,
                    'rack_position': rack_position,
                    'dilution_flag': dilution_flag
                }
            )
            
            # 创建查询医嘱
            order = StandardOrder(
                order_id=f"QRY_{sample_number}_{timestamp}",
                sample_id=sample_number,
                test_type=query_mode,
                order_time=timestamp,
                priority=query_priority,
                metadata={
                    'query_id': query_id,
                    'rack_position': rack_position,
                    'dilution_flag': dilution_flag
                }
            )
            
            # 创建标准消息
            message = StandardMessage(
                message_type="QRY",
                message_id=f"QRY_{device_info.device_id}_{timestamp}",
                timestamp=datetime.now(),
                device_info=device_info,
                patient=patient,
                orders=[order],
                raw_message=raw_message,
                metadata={
                    'protocol_version': '1.12',
                    'query_mode': query_mode,
                    'original_timestamp': timestamp
                }
            )
            
            self.logger.info(f"成功解析QRY消息 - 样本号: {sample_number}, 模式: {query_mode}")
            return message
            
        except Exception as e:
            raise ProtocolParseError(f"解析QRY消息失败: {str(e)}")
    
    def _parse_oru_message(self, text: str, device_info: DeviceInfo, raw_message: bytes) -> StandardMessage:
        """解析ORU^R01结果消息
        
        ORU消息格式示例:
        MSH|^~\\&|LIS|Hospital|LIS|Hospital|20231201120000||ORU^R01|12345|P|2.5|||
        OBR|1|sample_id|request_id|instrument_code|priority||test_time||retest_count|rack_position||dilution||submission_time|sample_type|||||||report_time|
        OBX|data_type|sequence|test_code|result|unit|reference_range|abnormal_flag||result_status||||observation_time|||reagent_lot|reagent_expiry|...
        """
        try:
            lines = text.split('\r')
            
            # 解析MSH段（消息头）
            msh_info = self._parse_msh_segment(lines)
            
            # 解析OBR段（观察请求）
            obr_info = self._parse_obr_segments(lines)
            
            # 解析OBX段（观察结果）
            obx_results = self._parse_obx_segments(lines)
            
            # 创建患者信息
            patient = StandardPatient(
                patient_id=obr_info.get('patient_id', ''),
                sample_id=obr_info.get('sample_id', ''),
                sample_type=obr_info.get('sample_type', ''),
                metadata=obr_info
            )
            
            # 创建检验结果列表
            test_results = []
            for obx in obx_results:
                result = StandardTestResult(
                    test_code=obx.get('test_code', ''),
                    test_name=obx.get('test_name', ''),
                    value=self._limit_field_length(obx.get('value', ''), 'test_value'),
                    unit=obx.get('unit', ''),
                    reference_range=obx.get('reference_range', ''),
                    abnormal_flag=obx.get('abnormal_flag', ''),
                    result_status=obx.get('result_status', ''),
                    observation_time=obx.get('observation_time', ''),
                    instrument_id=obx.get('instrument_id', ''),
                    reagent_lot=obx.get('reagent_lot', ''),
                    reagent_expiry=obx.get('reagent_expiry', ''),
                    metadata=obx
                )
                test_results.append(result)
            
            # 创建医嘱
            order = StandardOrder(
                order_id=obr_info.get('request_id', ''),
                sample_id=obr_info.get('sample_id', ''),
                test_type=obr_info.get('instrument_code', ''),
                order_time=obr_info.get('test_time', ''),
                report_time=obr_info.get('report_time', ''),
                priority=obr_info.get('priority', ''),
                results=test_results,
                metadata=obr_info
            )
            
            # 创建标准消息
            message = StandardMessage(
                message_type="ORU",
                message_id=msh_info.get('message_control_id', ''),
                timestamp=datetime.now(),
                device_info=device_info,
                patient=patient,
                orders=[order],
                raw_message=raw_message,
                metadata={
                    'protocol_version': msh_info.get('version_id', '2.5'),
                    'sending_application': msh_info.get('sending_application', ''),
                    'sending_facility': msh_info.get('sending_facility', ''),  # 保存原始YQ值
                    'results_count': len(test_results)
                }
            )
            
            # 计算并添加有效的YQ值
            effective_yq = self.get_yq_value(message, msh_info.get('sending_facility', ''))
            message.metadata['effective_yq'] = effective_yq
            
            self.logger.info(f"成功解析ORU消息 - 样本: {patient.sample_id}, 结果数: {len(test_results)}")
            return message
            
        except Exception as e:
            raise ProtocolParseError(f"解析ORU消息失败: {str(e)}")
    
    def _parse_msh_segment(self, lines: List[str]) -> Dict[str, str]:
        """解析MSH消息头段"""
        msh_info = {}
        
        for line in lines:
            if line.startswith('MSH|'):
                fields = line.split('|')
                msh_info = {
                    'field_separator': safe_get_field(fields, 1, '|'),
                    'encoding_characters': safe_get_field(fields, 2, '^~\\&'),
                    'sending_application': safe_get_field(fields, 3),
                    'sending_facility': safe_get_field(fields, 4),
                    'receiving_application': safe_get_field(fields, 5),
                    'receiving_facility': safe_get_field(fields, 6),
                    'date_time': safe_get_field(fields, 7),
                    'message_type': safe_get_field(fields, 9),
                    'message_control_id': safe_get_field(fields, 10),
                    'processing_id': safe_get_field(fields, 11),
                    'version_id': safe_get_field(fields, 12, '2.5')
                }
                break
        
        return msh_info
    
    def _parse_obr_segments(self, lines: List[str]) -> Dict[str, str]:
        """解析OBR观察请求段"""
        obr_info = {}
        
        for line in lines:
            if line.startswith('OBR|'):
                fields = line.split('|')
                obr_info = {
                    'sequence': safe_get_field(fields, 1),
                    'sample_id': self._limit_field_length(safe_get_field(fields, 2), 'sample_id'),
                    'request_id': safe_get_field(fields, 3),
                    'instrument_code': safe_get_field(fields, 4),
                    'priority': safe_get_field(fields, 5),
                    'test_time': safe_get_field(fields, 7),
                    'retest_count': safe_get_field(fields, 9),
                    'rack_position': safe_get_field(fields, 10),
                    'dilution': safe_get_field(fields, 12),
                    'submission_time': safe_get_field(fields, 14),
                    'sample_type': safe_get_field(fields, 15),
                    'report_time': safe_get_field(fields, 22),
                    'patient_id': self._limit_field_length(safe_get_field(fields, 2), 'patient_id')  # 简化处理
                }
                break
        
        return obr_info
    
    def _parse_obx_segments(self, lines: List[str]) -> List[Dict[str, str]]:
        """解析OBX观察结果段"""
        obx_results = []
        
        for line in lines:
            if line.startswith('OBX|'):
                fields = line.split('|')
                
                # 处理特殊字符
                test_value = self._decode_hl7_special_chars(safe_get_field(fields, 5))
                
                obx_result = {
                    'value_type': safe_get_field(fields, 2),
                    'sequence': safe_get_field(fields, 3),
                    'test_code': safe_get_field(fields, 4),
                    'value': test_value,
                    'unit': safe_get_field(fields, 6),
                    'reference_range': safe_get_field(fields, 7),
                    'abnormal_flag': safe_get_field(fields, 8),
                    'result_status': safe_get_field(fields, 11),
                    'observation_time': safe_get_field(fields, 14),
                    'instrument_id': safe_get_field(fields, 18),
                    'reagent_lot': safe_get_field(fields, 19),
                    'reagent_expiry': safe_get_field(fields, 20),
                    'test_name': safe_get_field(fields, 4)  # 简化处理，使用test_code作为name
                }
                
                obx_results.append(obx_result)
        
        return obx_results
    
    def _decode_hl7_special_chars(self, text: str) -> str:
        """解码HL7特殊字符"""
        if not text:
            return text
        
        result = text
        for encoded, decoded in self.hl7_special_chars.items():
            result = result.replace(encoded, decoded)
        
        return result
    
    def _limit_field_length(self, value: str, field_type: str) -> str:
        """限制字段长度"""
        if not value:
            return value
        
        max_length = self.field_length_limits.get(field_type, 255)
        if len(value) > max_length:
            self.logger.warning(f"字段 {field_type} 长度超限: {len(value)} > {max_length}")
            return value[:max_length]
        
        return value
    
    def generate_response(self, request: StandardMessage) -> bytes:
        """生成HL7响应消息"""
        try:
            if request.message_type == "QRY":
                return self._generate_qry_response(request)
            elif request.message_type == "ORU":
                return self._generate_ack_response(request)
            else:
                return b"NAK\r"
                
        except Exception as e:
            self.logger.error(f"生成YingKai响应失败: {e}")
            return b"NAK\r"
    
    def _generate_qry_response(self, request: StandardMessage) -> bytes:
        """生成QRY查询响应
        
        对于查询请求，需要根据样本号查询数据库并返回结果
        这里返回基础的响应格式，具体的数据库查询由上层业务逻辑处理
        """
        try:
            # 提取查询信息
            if not request.orders:
                return b"NAK\r"
            
            order = request.orders[0]
            sample_id = order.sample_id
            query_mode = order.metadata.get('query_mode', 'BC')
            
            # 构建基础响应（实际数据由业务层填充）
            timestamp = format_datetime()
            response_lines = []
            
            # 这里应该查询数据库获取实际结果
            # 目前返回基础的ACK格式
            ack_line = f"MSH|^~\\&|LIS|Hospital|YingKai|Lab|{timestamp}||ACK^R01|{timestamp}|P|2.5"
            response_lines.append(ack_line)
            
            response = '\r'.join(response_lines) + '\r'
            return response.encode('utf-8')
            
        except Exception as e:
            self.logger.error(f"生成QRY响应失败: {e}")
            return b"NAK\r"
    
    def _generate_ack_response(self, request: StandardMessage) -> bytes:
        """生成ORU的ACK响应"""
        try:
            timestamp = format_datetime()
            message_id = request.message_id or timestamp
            
            # 构建ACK消息
            ack_line = f"MSH|^~\\&|LIS|Hospital|YingKai|Lab|{timestamp}||ACK^R01|{message_id}|P|2.5"
            msa_line = f"MSA|AA|{message_id}|Message accepted"
            
            response = f"{ack_line}\r{msa_line}\r"
            return response.encode('utf-8')
            
        except Exception as e:
            self.logger.error(f"生成ACK响应失败: {e}")
            return b"NAK\r"