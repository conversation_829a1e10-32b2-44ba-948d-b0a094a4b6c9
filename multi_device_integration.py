#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多设备系统集成示例

该文件展示了如何将多设备支持集成到现有的LIS系统中，
同时保持向后兼容性。

主要功能：
1. 演示多设备路由器的使用
2. 展示适配器的动态加载
3. 提供集成测试示例
4. 向后兼容性验证
"""

import logging
import threading
import time
from typing import Dict, Any

# 导入多设备支持组件
from config_manager import ConfigManager, get_config_manager
from message_router import MessageRouter
from device_adapter import StandardMessage, ProtocolParseError, UnsupportedProtocolError
from adapters.yingkai_adapter import YingKaiHL7Adapter

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class MultiDeviceLISSystem:
    """多设备LIS系统集成类
    
    该类展示了如何将多设备支持集成到现有的LIS系统中
    """
    
    def __init__(self, config_file: str = None):
        """初始化多设备LIS系统
        
        Args:
            config_file: 配置文件路径
        """
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        self.config_manager = get_config_manager(config_file)
        
        # 检查是否启用多设备模式
        self.multi_device_enabled = self.config_manager.is_multi_device_enabled()
        
        # 初始化消息路由器
        self.message_router = None
        if self.multi_device_enabled:
            self._init_message_router()
        
        # 初始化单设备适配器（向后兼容）
        self.legacy_adapter = None
        self._init_legacy_adapter()
        
        # 统计信息
        self.stats = {
            'messages_processed': 0,
            'messages_failed': 0,
            'multi_device_messages': 0,
            'legacy_messages': 0
        }
        
        self.logger.info(f"多设备LIS系统初始化完成 - 多设备模式: {self.multi_device_enabled}")
    
    def _init_message_router(self):
        """初始化消息路由器"""
        try:
            # 构建路由器配置
            router_config = {
                'adapters': self.config_manager.get_adapters_config(),
                'devices': self.config_manager.get_devices_config(),
                'router': self.config_manager.get_router_config()
            }
            
            # 创建消息路由器
            self.message_router = MessageRouter(router_config)
            
            # 手动注册YingKai适配器（确保向后兼容）
            yingkai_config = self.config_manager.get_section('ADAPTER_yingkai')
            if yingkai_config:
                yingkai_adapter = YingKaiHL7Adapter(yingkai_config)
                self.message_router.register_adapter('yingkai', yingkai_adapter)
            
            self.logger.info("消息路由器初始化成功")
            
        except Exception as e:
            self.logger.error(f"消息路由器初始化失败: {e}")
            self.multi_device_enabled = False
    
    def _init_legacy_adapter(self):
        """初始化传统适配器（向后兼容）"""
        try:
            yingkai_config = self.config_manager.get_section('ADAPTER_yingkai')
            if not yingkai_config:
                yingkai_config = {}  # 使用默认配置
            
            self.legacy_adapter = YingKaiHL7Adapter(yingkai_config)
            self.logger.info("传统适配器初始化成功")
            
        except Exception as e:
            self.logger.error(f"传统适配器初始化失败: {e}")
    
    def process_message(self, message: bytes, connection_info: Dict[str, Any]) -> bytes:
        """处理设备消息
        
        Args:
            message: 原始消息字节
            connection_info: 连接信息
            
        Returns:
            响应消息字节
        """
        try:
            self.stats['messages_processed'] += 1
            
            if self.multi_device_enabled and self.message_router:
                # 使用多设备路由器处理
                return self._process_with_router(message, connection_info)
            else:
                # 使用传统单设备处理
                return self._process_with_legacy(message, connection_info)
                
        except Exception as e:
            self.stats['messages_failed'] += 1
            self.logger.error(f"消息处理失败: {e}")
            return b"NAK\r"
    
    def _process_with_router(self, message: bytes, connection_info: Dict[str, Any]) -> bytes:
        """使用多设备路由器处理消息"""
        try:
            # 路由消息到合适的适配器
            standard_message = self.message_router.route_message(message, connection_info)
            
            # 处理标准化消息
            self._handle_standard_message(standard_message)
            
            # 生成响应
            response = self.message_router.generate_response(standard_message)
            
            self.stats['multi_device_messages'] += 1
            
            self.logger.info(
                f"多设备路由处理成功 - 设备: {standard_message.device_info.device_type}, "
                f"消息类型: {standard_message.message_type}"
            )
            
            return response
            
        except UnsupportedProtocolError as e:
            self.logger.warning(f"不支持的协议，尝试传统处理: {e}")
            return self._process_with_legacy(message, connection_info)
            
        except Exception as e:
            self.logger.error(f"多设备路由处理失败: {e}")
            raise
    
    def _process_with_legacy(self, message: bytes, connection_info: Dict[str, Any]) -> bytes:
        """使用传统方式处理消息（向后兼容）"""
        try:
            if not self.legacy_adapter:
                return b"NAK\r"
            
            # 检查是否能处理
            if not self.legacy_adapter.can_handle(message, connection_info):
                return b"NAK\r"
            
            # 解析消息
            standard_message = self.legacy_adapter.parse_message(message, connection_info)
            
            # 处理标准化消息
            self._handle_standard_message(standard_message)
            
            # 生成响应
            response = self.legacy_adapter.generate_response(standard_message)
            
            self.stats['legacy_messages'] += 1
            
            self.logger.info(f"传统方式处理成功 - 消息类型: {standard_message.message_type}")
            
            return response
            
        except Exception as e:
            self.logger.error(f"传统方式处理失败: {e}")
            raise
    
    def _handle_standard_message(self, message: StandardMessage):
        """处理标准化消息（业务逻辑）
        
        这里应该包含实际的业务处理逻辑，比如：
        - 数据库存储
        - 数据验证
        - 业务规则处理
        - 通知发送等
        """
        try:
            if message.message_type == "ORU":
                self._handle_result_message(message)
            elif message.message_type == "QRY":
                self._handle_query_message(message)
            else:
                self.logger.warning(f"未知消息类型: {message.message_type}")
        
        except Exception as e:
            self.logger.error(f"业务逻辑处理失败: {e}")
            raise
    
    def _handle_result_message(self, message: StandardMessage):
        """处理结果消息（ORU）"""
        # 这里应该实现实际的结果存储逻辑
        # 目前只是记录日志
        
        patient = message.patient
        orders = message.orders
        
        self.logger.info(f"处理结果消息 - 患者: {patient.patient_id}, 样本: {patient.sample_id}")
        
        for order in orders:
            self.logger.info(f"  医嘱: {order.order_id}, 结果数: {len(order.results)}")
            for result in order.results:
                self.logger.info(f"    {result.test_code}: {result.value} {result.unit}")
    
    def _handle_query_message(self, message: StandardMessage):
        """处理查询消息（QRY）"""
        # 这里应该实现实际的查询逻辑
        # 目前只是记录日志
        
        patient = message.patient
        orders = message.orders
        
        self.logger.info(f"处理查询消息 - 样本: {patient.sample_id}")
        
        if orders:
            order = orders[0]
            query_mode = order.metadata.get('query_mode', 'BC')
            self.logger.info(f"  查询模式: {query_mode}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        stats = self.stats.copy()
        
        # 添加路由器统计信息
        if self.message_router:
            router_stats = self.message_router.get_statistics()
            stats['router_stats'] = router_stats
        
        # 添加配置信息
        stats['config_summary'] = self.config_manager.get_config_summary()
        
        return stats
    
    def get_available_adapters(self) -> Dict[str, Any]:
        """获取可用适配器信息"""
        if self.message_router:
            return self.message_router.get_available_adapters()
        return {}

# =============================================================================
# 测试和示例代码
# =============================================================================

def test_multi_device_system():
    """测试多设备系统"""
    print("=" * 60)
    print("多设备LIS系统测试")
    print("=" * 60)
    
    # 初始化系统
    try:
        # 尝试使用多设备配置
        system = MultiDeviceLISSystem('config_multi_device.ini')
    except:
        # 回退到默认配置
        system = MultiDeviceLISSystem('config.ini')
    
    # 打印系统信息
    stats = system.get_statistics()
    print(f"多设备模式: {system.multi_device_enabled}")
    print(f"配置摘要: {stats['config_summary']}")
    
    # 获取适配器信息
    adapters = system.get_available_adapters()
    print(f"可用适配器: {list(adapters.keys())}")
    
    # 测试消息处理
    test_messages = [
        # YingKai HL7 ORU消息示例
        (
            b"MSH|^~\\&|LIS|Hospital|LIS|Hospital|20231201120000||ORU^R01|12345|P|2.5\r"
            b"OBR|1|S001|R001|GLU||||||||||||||||||20231201120000\r"
            b"OBX|NM|1|GLU|6.5|mmol/L|3.9-6.1|H||F||||20231201120000\r",
            {'address': ('*************', 22010)}
        ),
        # YingKai HL7 QRY消息示例
        (
            b"QRD|20231201120000|R|I|||10^RD|S002|||BC|A01||\r",
            {'address': ('*************', 22010)}
        ),
        # 未知协议消息
        (
            b"UNKNOWN_PROTOCOL_MESSAGE",
            {'address': ('192.168.999.999', 9999)}
        )
    ]
    
    print("\n" + "=" * 40)
    print("测试消息处理")
    print("=" * 40)
    
    for i, (message, connection_info) in enumerate(test_messages, 1):
        print(f"\n测试消息 {i}:")
        print(f"  来源: {connection_info['address']}")
        print(f"  长度: {len(message)} 字节")
        
        try:
            response = system.process_message(message, connection_info)
            print(f"  响应: {response[:50]}..." if len(response) > 50 else f"  响应: {response}")
        except Exception as e:
            print(f"  错误: {e}")
    
    # 显示最终统计信息
    final_stats = system.get_statistics()
    print("\n" + "=" * 40)
    print("最终统计信息")
    print("=" * 40)
    for key, value in final_stats.items():
        if isinstance(value, dict):
            print(f"{key}:")
            for sub_key, sub_value in value.items():
                print(f"  {sub_key}: {sub_value}")
        else:
            print(f"{key}: {value}")

def demo_adapter_loading():
    """演示适配器动态加载"""
    print("\n" + "=" * 60)
    print("适配器动态加载演示")
    print("=" * 60)
    
    config_manager = get_config_manager('config_multi_device.ini')
    
    # 显示适配器配置
    adapters_config = config_manager.get_adapters_config()
    print("配置的适配器:")
    for name, config in adapters_config.items():
        print(f"  {name}: {config.get('class', 'Unknown')} ({config.get('protocol', 'Unknown')})")
    
    # 显示设备配置
    devices_config = config_manager.get_devices_config()
    print("\n配置的设备:")
    for name, config in devices_config.items():
        print(f"  {name}: {config.get('device_type', 'Unknown')} -> {config.get('adapter', 'Unknown')}")
    
    # 演示路由器创建
    try:
        router_config = {
            'adapters': adapters_config,
            'devices': devices_config,
            'router': config_manager.get_router_config()
        }
        
        router = MessageRouter(router_config)
        
        print("\n路由器统计:")
        router_stats = router.get_statistics()
        for key, value in router_stats.items():
            print(f"  {key}: {value}")
            
    except Exception as e:
        print(f"\n路由器创建失败: {e}")

if __name__ == "__main__":
    # 运行测试
    test_multi_device_system()
    
    # 运行演示
    demo_adapter_loading()