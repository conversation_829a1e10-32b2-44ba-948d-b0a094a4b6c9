#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
直接消息处理测试 - 绕过网络，直接测试消息处理和数据库写入
"""

from datetime import datetime
from urit_lis_server import URITLISServer

def test_direct_message_processing():
    """直接测试消息处理功能"""
    print("URIT消息直接处理测试")
    print("=" * 50)
    
    # 1. 创建服务器实例
    print("1. 创建服务器实例...")
    server = URITLISServer()
    print("   OK 服务器创建成功")
    
    # 2. 准备测试消息
    print("2. 准备测试消息...")
    now = datetime.now()
    timestamp = now.strftime("%Y%m%d%H%M%S")
    date_str = now.strftime("%Y-%m-%d")
    
    # 构建URIT HL7消息
    sample_id = "202408040010"
    test_code = "ALT"
    value = "48.5"
    unit = "U/L"
    ref_range = "0.0-40.0"
    flag = "H"
    
    msh = f"MSH|^~\\&|urit|8030|||{timestamp}||ORU^R01|{sample_id}|P|2.3.1||||0||ASCII|||"
    pid = f"PID|1||||||0|||||0|||||||||||||||||||"
    obr = f"OBR|1||{sample_id}|urit^8030|N||{date_str}|||||||||||||||||||||||||||||||||||||||"
    obx = f"OBX|1|NM|1|{test_code}|{value}|{unit}|{ref_range}|{flag}|||F||0.0000|{date_str}||检验师||"
    
    message = f"{msh}\r{pid}\r{obr}\r{obx}".encode('utf-8')
    
    print(f"   消息内容: 样本{sample_id[-2:]} {test_code}={value} {unit} ({flag})")
    print(f"   消息长度: {len(message)} 字节")
    
    # 3. 直接处理消息
    print("3. 直接处理消息...")
    try:
        client_address = ('127.0.0.1', 12345)  # 模拟客户端地址
        response = server.process_message(message, client_address)
        
        print(f"   OK 消息处理成功")
        print(f"   响应: {response.decode('utf-8', errors='ignore')}")
        
        # 4. 检查统计信息
        print("4. 检查处理统计...")
        print(f"   接收消息: {server.stats['messages_received']}")
        print(f"   处理成功: {server.stats['messages_processed']}")
        print(f"   处理失败: {server.stats['messages_failed']}")
        print(f"   数据库写入: {server.stats['database_writes']}")
        print(f"   数据库错误: {server.stats['database_errors']}")
        
        # 5. 验证数据库数据
        print("5. 验证数据库数据...")
        with server.get_db_connection() as conn:
            cursor = conn.cursor()
            
            today = datetime.now().strftime('%Y-%m-%d')
            
            # 查询刚写入的数据
            cursor.execute(f"""
                SELECT COUNT(*) FROM {server.pat_table} 
                WHERE yq = 'URIT8030' AND wyh = ?
            """, sample_id)
            pat_count = cursor.fetchone()[0]
            
            cursor.execute(f"""
                SELECT COUNT(*) FROM {server.result_table} 
                WHERE yq = 'URIT8030' AND wyh = ?
            """, sample_id)
            result_count = cursor.fetchone()[0]
            
            print(f"   患者记录: {pat_count} 条")
            print(f"   检验结果: {result_count} 条")
            
            if pat_count > 0 and result_count > 0:
                # 查询具体数据
                cursor.execute(f"""
                    SELECT ybh, jyrq FROM {server.pat_table} 
                    WHERE yq = 'URIT8030' AND wyh = ?
                """, sample_id)
                pat_data = cursor.fetchone()
                
                cursor.execute(f"""
                    SELECT ybh, xmdh, csjg, result_flag FROM {server.result_table} 
                    WHERE yq = 'URIT8030' AND wyh = ?
                """, sample_id)
                result_data = cursor.fetchone()
                
                print(f"   患者数据: 样本ID={pat_data[0]}, 时间={pat_data[1]}")
                print(f"   结果数据: 样本{result_data[0]} {result_data[1]}={result_data[2]} ({result_data[3]})")
                
                # 验证样本ID处理
                expected_sample_id = "10"  # 202408040010 -> 0010 -> 10
                actual_sample_id = pat_data[0]
                
                if actual_sample_id == expected_sample_id:
                    print(f"   OK 样本ID处理正确: {sample_id} -> {actual_sample_id}")
                else:
                    print(f"   X 样本ID处理错误: 预期{expected_sample_id}, 实际{actual_sample_id}")
                
                return True
            else:
                print("   X 未找到写入的数据")
                return False
        
    except Exception as e:
        print(f"   X 消息处理失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_messages():
    """测试多条消息处理"""
    print("\n多条消息处理测试")
    print("=" * 30)
    
    server = URITLISServer()
    
    # 准备多条测试消息
    test_cases = [
        ("202408040020", "AST", "35.2", "U/L", "0.0-40.0", "N"),
        ("202408040021", "GLU", "6.8", "mmol/L", "3.9-6.1", "H"),
        ("202408040022", "CREA", "88.0", "umol/L", "44-133", "N"),
    ]
    
    processed_count = 0
    
    for sample_id, test_code, value, unit, ref_range, flag in test_cases:
        try:
            # 构建消息
            now = datetime.now()
            timestamp = now.strftime("%Y%m%d%H%M%S")
            date_str = now.strftime("%Y-%m-%d")
            
            msh = f"MSH|^~\\&|urit|8030|||{timestamp}||ORU^R01|{sample_id}|P|2.3.1||||0||ASCII|||"
            pid = f"PID|1||||||0|||||0|||||||||||||||||||"
            obr = f"OBR|1||{sample_id}|urit^8030|N||{date_str}|||||||||||||||||||||||||||||||||||||||"
            obx = f"OBX|1|NM|1|{test_code}|{value}|{unit}|{ref_range}|{flag}|||F||0.0000|{date_str}||检验师||"
            
            message = f"{msh}\r{pid}\r{obr}\r{obx}".encode('utf-8')
            
            # 处理消息
            client_address = ('127.0.0.1', 12345)
            response = server.process_message(message, client_address)
            
            if b"ACK" in response:
                print(f"   样本{sample_id[-2:]}: {test_code}={value} {unit} - OK")
                processed_count += 1
            else:
                print(f"   样本{sample_id[-2:]}: {test_code}={value} {unit} - 失败")
        
        except Exception as e:
            print(f"   样本{sample_id[-2:]}: 处理出错 - {e}")
    
    print(f"处理结果: {processed_count}/{len(test_cases)} 条消息成功")
    
    # 验证数据库
    try:
        with server.get_db_connection() as conn:
            cursor = conn.cursor()
            
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute(f"""
                SELECT COUNT(*) FROM {server.result_table} 
                WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
            """, today)
            total_results = cursor.fetchone()[0]
            
            print(f"数据库中今天的URIT结果总数: {total_results}")
            
    except Exception as e:
        print(f"数据库验证失败: {e}")

def main():
    """主测试函数"""
    print("URIT设备直接消息处理与数据库写入测试")
    print("=" * 60)
    print()
    
    # 单条消息测试
    success1 = test_direct_message_processing()
    
    # 多条消息测试
    test_multiple_messages()
    
    print("\n" + "=" * 60)
    if success1:
        print("真实数据处理测试成功！")
        print()
        print("验证要点:")
        print("- URIT HL7消息正确解析")
        print("- 样本ID正确处理（去除前导0）")  
        print("- YQ字段使用自定义值 'URIT8030'")
        print("- 数据成功写入 lis_pat 和 lis_result 表")
        print("- ACK响应正确生成")
        print()
        print("数据库验证SQL:")
        print("SELECT * FROM lis_pat WHERE yq = 'URIT8030' ORDER BY jyrq DESC")
        print("SELECT * FROM lis_result WHERE yq = 'URIT8030' ORDER BY jyrq DESC")
    else:
        print("真实数据处理测试失败，请检查错误信息")
    
    print("=" * 60)

if __name__ == '__main__':
    main()