#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多设备协议适配器核心接口和数据结构定义

该模块定义了支持多设备的核心接口和标准化数据结构，
为不同厂商设备的协议适配提供统一的抽象层。
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

# =============================================================================
# 标准化数据结构定义
# =============================================================================

@dataclass
class DeviceInfo:
    """设备信息"""
    device_id: str              # 设备唯一标识
    device_type: str            # 设备类型 (<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, etc.)
    manufacturer: str           # 制造商
    model: str                  # 型号
    protocol: str               # 协议类型 (HL7, ASTM, Custom)
    version: str                # 协议版本
    ip_address: str = ""        # IP地址
    port: int = 0               # 端口号
    yq_config: Dict[str, Any] = field(default_factory=dict)  # YQ配置
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据

@dataclass
class StandardPatient:
    """标准化患者信息"""
    patient_id: str             # 患者ID (必填)
    name: str = ""              # 姓名
    sex: str = ""               # 性别 (M/F/U)
    age: str = ""               # 年龄
    birth_date: str = ""        # 出生日期 (YYYYMMDD)
    department: str = ""        # 科室
    bed_no: str = ""            # 床号
    sample_id: str = ""         # 样本ID
    sample_type: str = ""       # 样本类型
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外字段

@dataclass
class StandardTestResult:
    """标准化检验结果"""
    test_code: str              # 检验项目代码 (必填)
    test_name: str = ""         # 检验项目名称
    value: str = ""             # 检验结果值
    unit: str = ""              # 单位
    reference_range: str = ""   # 参考范围
    abnormal_flag: str = ""     # 异常标志 (N/H/L/etc.)
    result_status: str = ""     # 结果状态 (F/P/C/etc.)
    observation_time: str = ""  # 观察时间
    instrument_id: str = ""     # 仪器ID
    reagent_lot: str = ""       # 试剂批号
    reagent_expiry: str = ""    # 试剂有效期
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外字段

@dataclass
class StandardOrder:
    """标准化医嘱"""
    order_id: str               # 医嘱ID (必填)
    sample_id: str              # 样本ID (必填)
    test_type: str = ""         # 检验类型
    order_time: str = ""        # 医嘱时间
    report_time: str = ""       # 报告时间
    priority: str = ""          # 优先级 (R/S/A/etc.)
    status: str = ""            # 状态
    results: List[StandardTestResult] = field(default_factory=list)  # 检验结果列表
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外字段

@dataclass
class StandardMessage:
    """标准化消息"""
    message_type: str           # 消息类型 (ORU, QRY, ACK, etc.)
    message_id: str             # 消息ID
    timestamp: datetime         # 消息时间戳
    device_info: DeviceInfo     # 设备信息
    patient: StandardPatient    # 患者信息
    orders: List[StandardOrder] = field(default_factory=list)  # 医嘱列表
    raw_message: bytes = b""    # 原始消息
    metadata: Dict[str, Any] = field(default_factory=dict)  # 额外元数据

# =============================================================================
# 协议适配器基类
# =============================================================================

class ProtocolAdapter(ABC):
    """协议适配器基类
    
    所有设备协议适配器都必须继承此类并实现相应的抽象方法。
    适配器负责将设备特定的协议格式转换为统一的标准格式。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """初始化适配器
        
        Args:
            config: 适配器配置字典
        """
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self._init_adapter()
    
    def _init_adapter(self):
        """适配器初始化，子类可重写此方法进行特定初始化"""
        pass
    
    @property
    @abstractmethod
    def protocol_name(self) -> str:
        """协议名称
        
        Returns:
            协议名称字符串 (如: "HL7", "ASTM", "Custom")
        """
        pass
    
    @property
    @abstractmethod
    def supported_versions(self) -> List[str]:
        """支持的协议版本列表
        
        Returns:
            版本字符串列表 (如: ["2.5", "2.4"])
        """
        pass
    
    @property
    @abstractmethod
    def manufacturer(self) -> str:
        """设备制造商
        
        Returns:
            制造商名称 (如: "YingKai", "Abbott", "Roche")
        """
        pass
    
    @abstractmethod
    def can_handle(self, message: bytes, connection_info: Dict[str, Any]) -> bool:
        """判断是否能处理该消息
        
        Args:
            message: 原始消息字节
            connection_info: 连接信息 (IP, Port, etc.)
            
        Returns:
            True if 可以处理, False otherwise
        """
        pass
    
    @abstractmethod
    def parse_message(self, message: bytes, connection_info: Dict[str, Any]) -> StandardMessage:
        """解析消息为标准格式
        
        Args:
            message: 原始消息字节
            connection_info: 连接信息
            
        Returns:
            标准化消息对象
            
        Raises:
            ProtocolParseError: 解析失败时抛出
        """
        pass
    
    @abstractmethod
    def generate_response(self, request: StandardMessage) -> bytes:
        """生成响应消息
        
        Args:
            request: 请求的标准化消息
            
        Returns:
            响应消息字节
        """
        pass
    
    def get_yq_value(self, message: StandardMessage, raw_yq: str = "") -> str:
        """获取YQ值，支持自定义映射
        
        Args:
            message: 标准化消息
            raw_yq: 原始YQ值（从消息中提取）
            
        Returns:
            处理后的YQ值
        """
        device_info = message.device_info
        yq_config = device_info.yq_config
        
        if not yq_config:
            return raw_yq
        
        # 根据配置的来源类型处理YQ
        yq_source = yq_config.get('yq_source', 'message')
        
        if yq_source == 'custom':
            # 使用自定义YQ值
            return yq_config.get('yq_value', raw_yq)
        elif yq_source == 'device_id':
            # 使用设备ID作为YQ
            prefix = yq_config.get('yq_prefix', '')
            return f"{prefix}{device_info.device_id}"
        elif yq_source == 'mapping':
            # 使用映射表
            mapping = yq_config.get('yq_mapping', {})
            return mapping.get(raw_yq, raw_yq)
        
        # 默认返回原始值
        return raw_yq
    
    def validate_message(self, message: StandardMessage) -> List[str]:
        """验证消息完整性
        
        Args:
            message: 标准化消息
            
        Returns:
            错误信息列表，空列表表示验证通过
        """
        errors = []
        
        # 基础验证
        if not message.message_type:
            errors.append("消息类型不能为空")
        
        if not message.message_id:
            errors.append("消息ID不能为空")
        
        if not message.patient.patient_id:
            errors.append("患者ID不能为空")
        
        if message.message_type in ["ORU"] and not message.orders:
            errors.append("结果消息必须包含医嘱列表")
        
        # 检查每个医嘱
        for i, order in enumerate(message.orders):
            if not order.order_id:
                errors.append(f"医嘱[{i}]的order_id不能为空")
            
            if not order.sample_id:
                errors.append(f"医嘱[{i}]的sample_id不能为空")
            
            # 检查结果
            for j, result in enumerate(order.results):
                if not result.test_code:
                    errors.append(f"医嘱[{i}]结果[{j}]的test_code不能为空")
        
        return errors
    
    def get_adapter_info(self) -> Dict[str, Any]:
        """获取适配器信息
        
        Returns:
            适配器信息字典
        """
        return {
            "protocol_name": self.protocol_name,
            "supported_versions": self.supported_versions,
            "manufacturer": self.manufacturer,
            "class_name": self.__class__.__name__,
            "config": self.config
        }

# =============================================================================
# 异常定义
# =============================================================================

class ProtocolError(Exception):
    """协议处理基础异常"""
    pass

class ProtocolParseError(ProtocolError):
    """协议解析异常"""
    pass

class UnsupportedProtocolError(ProtocolError):
    """不支持的协议异常"""
    pass

class MessageValidationError(ProtocolError):
    """消息验证异常"""
    pass

class DeviceConnectionError(ProtocolError):
    """设备连接异常"""
    pass

# =============================================================================
# 工具函数
# =============================================================================

def safe_get_field(fields: List[str], index: int, default: str = "") -> str:
    """安全获取字段值，避免越界异常
    
    Args:
        fields: 字段列表
        index: 索引
        default: 默认值
        
    Returns:
        字段值或默认值
    """
    try:
        if 0 <= index < len(fields):
            return fields[index].strip()
        return default
    except (IndexError, AttributeError):
        return default

def format_datetime(dt: datetime = None, format_str: str = "%Y%m%d%H%M%S") -> str:
    """格式化日期时间
    
    Args:
        dt: 日期时间对象，None则使用当前时间
        format_str: 格式字符串
        
    Returns:
        格式化后的日期时间字符串
    """
    if dt is None:
        dt = datetime.now()
    return dt.strftime(format_str)

def parse_datetime(date_str: str, format_str: str = "%Y%m%d%H%M%S") -> Optional[datetime]:
    """解析日期时间字符串
    
    Args:
        date_str: 日期时间字符串
        format_str: 格式字符串
        
    Returns:
        日期时间对象或None
    """
    try:
        return datetime.strptime(date_str, format_str)
    except (ValueError, TypeError):
        return None

def clean_string(text: str) -> str:
    """清理字符串，移除特殊字符
    
    Args:
        text: 输入字符串
        
    Returns:
        清理后的字符串
    """
    if not text:
        return ""
    
    # 移除HL7特殊字符
    special_chars = {
        '^': '',      # 组件分隔符
        '&': '',      # 转义字符
        '~': '',      # 重复分隔符
        '\\': '',     # 转义字符
        '|': '',      # 字段分隔符
    }
    
    result = text
    for char, replacement in special_chars.items():
        result = result.replace(char, replacement)
    
    return result.strip()

# =============================================================================
# 日志配置
# =============================================================================

def setup_adapter_logging(logger_name: str, level: int = logging.INFO) -> logging.Logger:
    """设置适配器日志
    
    Args:
        logger_name: 日志器名称
        level: 日志级别
        
    Returns:
        配置好的日志器
    """
    logger = logging.getLogger(logger_name)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        logger.setLevel(level)
    
    return logger