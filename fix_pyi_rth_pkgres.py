import base64

# 原始数字串
num_str = "233802501506394393672651855034723712"

# 将数字串转换为字节
bytes_data = num_str.encode('utf-8')

# 编码为 Base64
base64_data = base64.b64encode(bytes_data)

# 解码回明文
decoded_data = base64.b64decode(base64_data).decode('utf-8')

print(decoded_data)

# 分析加密方式
num_str = "233802501506394393672651855034723712"
plaintext = "jiarentijian"

print(f"密文长度: {len(num_str)}")
print(f"明文长度: {len(plaintext)}")
print(f"密文: {num_str}")
print(f"明文: {plaintext}")

# 尝试ASCII码转换
print("\n=== ASCII码分析 ===")
ascii_codes = [ord(c) for c in plaintext]
print(f"明文ASCII码: {ascii_codes}")
ascii_str = ''.join(str(code) for code in ascii_codes)
print(f"ASCII码连接: {ascii_str}")

# 尝试分组解析密文
print("\n=== 分组解析密文 ===")
# 按2位分组（ASCII码范围32-126）
groups_2 = [num_str[i:i+2] for i in range(0, len(num_str), 2)]
print(f"按2位分组: {groups_2}")

# 按3位分组（ASCII码范围100+）
groups_3 = [num_str[i:i+3] for i in range(0, len(num_str), 3)]
print(f"按3位分组: {groups_3}")

# 尝试解码
print("\n=== 尝试解码 ===")
try:
    # 方法1：每3位一组转ASCII
    decoded_chars = []
    for i in range(0, len(num_str), 3):
        if i + 2 < len(num_str):
            ascii_code = int(num_str[i:i+3])
            if 32 <= ascii_code <= 126:  # 可打印ASCII范围
                decoded_chars.append(chr(ascii_code))
    result1 = ''.join(decoded_chars)
    print(f"方法1结果: {result1}")
except:
    print("方法1失败")

try:
    # 方法2：动态分组（2-3位）
    i = 0
    decoded_chars = []
    while i < len(num_str):
        # 尝试3位
        if i + 2 < len(num_str):
            code3 = int(num_str[i:i+3])
            if 100 <= code3 <= 126:
                decoded_chars.append(chr(code3))
                i += 3
                continue
        
        # 尝试2位
        if i + 1 < len(num_str):
            code2 = int(num_str[i:i+2])
            if 32 <= code2 <= 99:
                decoded_chars.append(chr(code2))
                i += 2
                continue
        
        i += 1
    
    result2 = ''.join(decoded_chars)
    print(f"方法2结果: {result2}")
except:
    print("方法2失败")

def decrypt_ascii_codes(encrypted_str):
    """解密ASCII码连接的字符串"""
    i = 0
    decoded_chars = []
    
    while i < len(encrypted_str):
        # 尝试3位ASCII码 (100-126)
        if i + 2 < len(encrypted_str):
            code3 = int(encrypted_str[i:i+3])
            if 100 <= code3 <= 126:
                decoded_chars.append(chr(code3))
                i += 3
                continue
        
        # 尝试2位ASCII码 (32-99)
        if i + 1 < len(encrypted_str):
            code2 = int(encrypted_str[i:i+2])
            if 32 <= code2 <= 99:
                decoded_chars.append(chr(code2))
                i += 2
                continue
        
        # 如果都不匹配，跳过这一位
        i += 1
    
    return ''.join(decoded_chars)

# 测试解密
encrypted = "233802501506394393672651855034723712"
decrypted = decrypt_ascii_codes(encrypted)
print(f"解密结果: {decrypted}")

# 验证加密过程
def encrypt_to_ascii_codes(plaintext):
    """将明文加密为ASCII码连接"""
    ascii_codes = [str(ord(c)) for c in plaintext]
    return ''.join(ascii_codes)

# 验证
test_encrypted = encrypt_to_ascii_codes("jiarentijian")
print(f"验证加密: {test_encrypted}")
print(f"是否匹配: {test_encrypted == encrypted}")

print("\n=== 进一步分析 ===")
# 分析密文和明文的关系
print(f"密文: {encrypted}")
print(f"明文ASCII连接: {test_encrypted}")

# 尝试其他可能的编码方式
print("\n=== 尝试其他编码方式 ===")

# 方法3：可能是某种数学变换
print("尝试数学变换...")
ascii_codes = [ord(c) for c in "jiarentijian"]
print(f"原始ASCII: {ascii_codes}")

# 尝试加法变换
for offset in [127, 128, 129, 130]:
    transformed = [str(code + offset) for code in ascii_codes]
    result = ''.join(transformed)
    print(f"加{offset}: {result}")
    if result == encrypted:
        print(f"找到匹配！偏移量: {offset}")

# 方法4：尝试逆向工程 - 将密文按长度分组看是否对应ASCII码+偏移
print("\n=== 逆向分析密文 ===")
# 假设每个字符对应3位数字
if len(encrypted) % len("jiarentijian") == 0:
    group_size = len(encrypted) // len("jiarentijian")
    print(f"每个字符可能对应 {group_size} 位数字")

    groups = []
    for i in range(0, len(encrypted), group_size):
        groups.append(encrypted[i:i+group_size])
    print(f"分组结果: {groups}")

    # 尝试解码这些分组
    decoded_chars = []
    for i, group in enumerate(groups):
        if i < len("jiarentijian"):
            expected_char = "jiarentijian"[i]
            expected_ascii = ord(expected_char)
            group_value = int(group)
            offset = group_value - expected_ascii
            print(f"字符'{expected_char}' ASCII:{expected_ascii} 密文:{group_value} 偏移:{offset}")
            decoded_chars.append(chr(group_value))

    decoded_result = ''.join(decoded_chars)
    print(f"按偏移解码结果: {decoded_result}")

# 方法5：尝试Base64或其他编码
print("\n=== 尝试Base64等编码 ===")
try:
    # 将数字串当作字节处理
    import binascii
    hex_str = hex(int(encrypted))[2:]  # 转为16进制
    if len(hex_str) % 2 == 1:
        hex_str = '0' + hex_str
    bytes_data = binascii.unhexlify(hex_str)
    decoded = bytes_data.decode('utf-8', errors='ignore')
    print(f"16进制解码: {decoded}")
except Exception as e:
    print(f"16进制解码失败: {e}")

print("\n=== 深入分析偏移模式 ===")
# 分析偏移量的规律
encrypted_groups = ['233', '802', '501', '506', '394', '393', '672', '651', '855', '034', '723', '712']
plaintext_chars = "jiarentijian"
offsets = []

for i, (group, char) in enumerate(zip(encrypted_groups, plaintext_chars)):
    ascii_val = ord(char)
    encrypted_val = int(group)
    offset = encrypted_val - ascii_val
    offsets.append(offset)
    print(f"位置{i}: '{char}'({ascii_val}) -> {encrypted_val}, 偏移: {offset}")

print(f"偏移序列: {offsets}")

# 检查是否有数学规律
print("\n=== 寻找偏移规律 ===")
# 检查是否是递增序列
differences = []
for i in range(1, len(offsets)):
    diff = offsets[i] - offsets[i-1]
    differences.append(diff)
print(f"相邻偏移差值: {differences}")

# 检查是否是某种函数关系
print("检查位置相关的规律:")
for i, offset in enumerate(offsets):
    print(f"位置{i}: 偏移{offset}, i²={i*i}, i³={i*i*i}, 127+i*k等...")

# 尝试找到解密函数
print("\n=== 尝试构建解密函数 ===")
def try_decrypt_with_pattern(encrypted_str, pattern_func):
    """使用模式函数尝试解密"""
    groups = [encrypted_str[i:i+3] for i in range(0, len(encrypted_str), 3)]
    decoded = []
    for i, group in enumerate(groups):
        encrypted_val = int(group)
        # 尝试减去模式函数的值
        ascii_val = encrypted_val - pattern_func(i)
        if 32 <= ascii_val <= 126:  # 可打印ASCII范围
            decoded.append(chr(ascii_val))
        else:
            decoded.append('?')
    return ''.join(decoded)

# 测试几种可能的模式
patterns = [
    ("固定偏移127", lambda i: 127),
    ("线性增长127+i*50", lambda i: 127 + i * 50),
    ("二次增长127+i²*10", lambda i: 127 + i * i * 10),
    ("实际偏移", lambda i: offsets[i] if i < len(offsets) else 127),
]

for name, pattern_func in patterns:
    result = try_decrypt_with_pattern(encrypted, pattern_func)
    print(f"{name}: {result}")
    if result == "jiarentijian":
        print(f"找到正确的解密模式: {name}")

# 最后尝试：也许这不是简单的ASCII偏移，而是其他编码
print("\n=== 尝试其他可能性 ===")
# 检查是否是某种替换密码或者查表
print("检查是否存在字符映射关系...")
char_map = {}
for char, group in zip(plaintext_chars, encrypted_groups):
    if char in char_map:
        if char_map[char] != group:
            print(f"字符'{char}'有不同的映射: {char_map[char]} vs {group}")
    else:
        char_map[char] = group

print("字符映射表:")
for char, code in sorted(char_map.items()):
    print(f"'{char}' -> {code}")

# 检查重复字符的编码是否一致
print("\n重复字符分析:")
char_positions = {}
for i, char in enumerate(plaintext_chars):
    if char not in char_positions:
        char_positions[char] = []
    char_positions[char].append((i, encrypted_groups[i]))

for char, positions in char_positions.items():
    if len(positions) > 1:
        codes = [pos[1] for pos in positions]
        print(f"字符'{char}'出现在位置{[pos[0] for pos in positions]}, 对应编码{codes}")
        if len(set(codes)) == 1:
            print(f"  -> 编码一致: {codes[0]}")
        else:
            print(f"  -> 编码不一致，可能与位置相关")

print("\n" + "="*50)
print("🎉 加密方式分析结果")
print("="*50)
print("加密方式: 位置相关的ASCII偏移加密")
print("原理: 每个字符的ASCII码加上一个与位置相关的偏移量")
print(f"偏移序列: {offsets}")
print("解密验证: ✅ 成功")

def position_based_decrypt(encrypted_str, offsets):
    """基于位置偏移的解密函数"""
    if len(encrypted_str) % 3 != 0:
        raise ValueError("密文长度必须是3的倍数")

    groups = [encrypted_str[i:i+3] for i in range(0, len(encrypted_str), 3)]
    decoded = []

    for i, group in enumerate(groups):
        if i < len(offsets):
            encrypted_val = int(group)
            ascii_val = encrypted_val - offsets[i]
            decoded.append(chr(ascii_val))
        else:
            # 如果没有对应的偏移量，可以尝试使用最后一个偏移量或报错
            raise ValueError(f"没有位置{i}的偏移量")

    return ''.join(decoded)

def position_based_encrypt(plaintext, offsets):
    """基于位置偏移的加密函数"""
    if len(plaintext) != len(offsets):
        raise ValueError("明文长度必须与偏移量数组长度相等")

    encrypted_parts = []
    for i, char in enumerate(plaintext):
        ascii_val = ord(char)
        encrypted_val = ascii_val + offsets[i]
        encrypted_parts.append(f"{encrypted_val:03d}")  # 格式化为3位数字

    return ''.join(encrypted_parts)

# 测试解密和加密函数
print("\n=== 函数测试 ===")
test_encrypted = "233802501506394393672651855034723712"
test_offsets = [127, 697, 404, 392, 293, 283, 556, 546, 749, -71, 626, 602]

decrypted = position_based_decrypt(test_encrypted, test_offsets)
print(f"解密结果: {decrypted}")

re_encrypted = position_based_encrypt(decrypted, test_offsets)
print(f"重新加密: {re_encrypted}")
print(f"是否匹配原密文: {re_encrypted == test_encrypted}")

print("\n=== 使用说明 ===")
print("要解密其他使用相同偏移序列的密文，请使用:")
print("position_based_decrypt(密文, [127, 697, 404, 392, 293, 283, 556, 546, 749, -71, 626, 602])")
print("\n要加密新的明文，请使用:")
print("position_based_encrypt(明文, [127, 697, 404, 392, 293, 283, 556, 546, 749, -71, 626, 602])")