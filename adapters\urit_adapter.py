#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
URIT设备HL7协议适配器

该适配器实现了对URIT系列设备的HL7协议支持，
专门处理URIT设备的特殊消息格式。

支持的功能：
- HL7 ORU^R01 消息解析和处理
- URIT设备特定的字段映射
- 自动消息类型识别
- 数据验证和错误处理
- ACK响应生成
"""

import re
from datetime import datetime
from typing import Dict, Any, List
from device_adapter import (
    ProtocolAdapter, StandardMessage, StandardPatient, 
    StandardOrder, StandardTestResult, DeviceInfo,
    ProtocolParseError, safe_get_field, format_datetime,
    clean_string
)

class URITAdapter(ProtocolAdapter):
    """URIT设备HL7协议适配器"""
    
    @property
    def protocol_name(self) -> str:
        return "URIT_HL7"
    
    @property
    def supported_versions(self) -> List[str]:
        return ["2.3.1", "2.4", "2.5"]
    
    @property
    def manufacturer(self) -> str:
        return "URIT"
    
    def _init_adapter(self):
        """初始化URIT适配器特定配置"""
        self.field_length_limits = self.config.get('field_length_limits', {
            'patient_id': 50,
            'patient_name': 100,
            'sample_id': 50,
            'test_code': 20,
            'test_value': 200,
            'test_unit': 20
        })
        
        # URIT设备的特殊字段映射
        self.urit_test_mapping = self.config.get('test_mapping', {
            'ALT': 'ALT',
            'AST': 'AST',
            'TBIL': 'TBIL',
            'DBIL': 'DBIL',
            'TP': 'TP',
            'ALB': 'ALB',
            'GLU': 'GLU',
            'BUN': 'BUN',
            'CREA': 'CREA',
            'UA': 'UA'
        })
    
    def can_handle(self, message: bytes, connection_info: Dict[str, Any]) -> bool:
        """检查是否为URIT HL7消息"""
        try:
            text = message.decode('utf-8', errors='ignore')
            
            # 检查URIT特征
            urit_indicators = [
                'MSH|^~\\&|urit|',     # URIT消息头
                '|urit|',              # 发送应用为urit
                'OBR|',                # 观察请求
                'OBX|',                # 观察结果  
                'ORU^R01',             # 结果消息
                '|2.3.1|'              # 版本号
            ]
            
            return any(indicator in text for indicator in urit_indicators)
            
        except Exception:
            return False
    
    def parse_message(self, message: bytes, connection_info: Dict[str, Any]) -> StandardMessage:
        """解析URIT HL7消息"""
        try:
            text = message.decode('utf-8', errors='replace')
            
            # 创建设备信息
            device_info = self._create_device_info(connection_info, text)
            
            # 提取消息类型
            message_type = self._extract_message_type(text)
            
            self.logger.info(f"解析URIT消息类型: {message_type}")
            
            if message_type == "ORU":
                return self._parse_oru_message(text, device_info, message)
            else:
                raise ProtocolParseError(f"不支持的URIT消息类型: {message_type}")
                
        except Exception as e:
            self.logger.error(f"解析URIT消息失败: {e}")
            raise ProtocolParseError(f"URIT消息解析失败: {str(e)}")
    
    def _create_device_info(self, connection_info: Dict[str, Any], text: str) -> DeviceInfo:
        """创建设备信息对象"""
        address = connection_info.get('address', ('unknown', 0))
        if isinstance(address, (list, tuple)) and len(address) >= 2:
            ip_address = str(address[0])
            port = int(address[1]) if len(address) > 1 else 0
        else:
            ip_address = "unknown"
            port = 0
        
        # 从MSH段提取设备型号
        device_model = "URIT"
        lines = text.split('\r')
        if lines and lines[0].startswith('MSH|'):
            msh_fields = lines[0].split('|')
            if len(msh_fields) > 4:
                device_model = msh_fields[4] or "URIT"  # MSH第4字段是设备型号
        
        return DeviceInfo(
            device_id=f"urit_{ip_address}_{port}",
            device_type="URIT_Analyzer",
            manufacturer="URIT",
            model=device_model,
            protocol="HL7",
            version="2.3.1",
            ip_address=ip_address,
            port=port,
            metadata={'adapter': 'urit'}
        )
    
    def _extract_message_type(self, text: str) -> str:
        """提取消息类型"""
        # URIT主要发送ORU^R01结果消息
        if 'MSH|' in text and 'ORU^R01' in text:
            return "ORU"
        elif 'OBR|' in text or 'OBX|' in text:
            return "ORU"  # 简化的结果消息
        else:
            return "UNKNOWN"
    
    def _parse_oru_message(self, text: str, device_info: DeviceInfo, raw_message: bytes) -> StandardMessage:
        """解析URIT ORU^R01结果消息
        
        URIT消息格式示例:
        MSH|^~\\&|urit|8030|||20180101010440||ORU^R01|201801010001|P|2.3.1||||0||ASCII|||
        PID|1||||||0|||||0|||||||||||||||||||
        OBR|1||201801010001|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||
        OBX|1|NM|1|ALT|14.7|U/L|0.0-40.0|N|||F||-0.0049|2018-01-01||杨玉清||
        """
        try:
            lines = text.split('\r')
            
            # 解析MSH段（消息头）
            msh_info = self._parse_msh_segment(lines)
            
            # 解析PID段（患者信息）
            pid_info = self._parse_pid_segment(lines)
            
            # 解析OBR段（观察请求）
            obr_info = self._parse_obr_segments(lines)
            
            # 解析OBX段（观察结果）
            obx_results = self._parse_obx_segments(lines)
            
            # 创建患者信息
            patient = StandardPatient(
                patient_id=obr_info.get('sample_id', ''),  # URIT使用样本ID作为患者ID
                sample_id=obr_info.get('sample_id', ''),
                name=pid_info.get('patient_name', ''),
                sex=pid_info.get('sex', ''),
                age=pid_info.get('age', ''),
                metadata={**pid_info, **obr_info}
            )
            
            # 创建检验结果列表
            test_results = []
            for obx in obx_results:
                # 映射URIT测试代码
                original_code = obx.get('test_code', '')
                mapped_code = self.urit_test_mapping.get(original_code, original_code)
                
                result = StandardTestResult(
                    test_code=mapped_code,
                    test_name=obx.get('test_name', original_code),
                    value=self._limit_field_length(obx.get('value', ''), 'test_value'),
                    unit=obx.get('unit', ''),
                    reference_range=obx.get('reference_range', ''),
                    abnormal_flag=obx.get('abnormal_flag', ''),
                    result_status=obx.get('result_status', ''),
                    observation_time=obx.get('observation_time', ''),
                    instrument_id=device_info.model,
                    metadata=obx
                )
                test_results.append(result)
            
            # 创建医嘱
            order = StandardOrder(
                order_id=obr_info.get('sample_id', ''),
                sample_id=obr_info.get('sample_id', ''),
                test_type=obr_info.get('universal_service_id', ''),
                order_time=obr_info.get('observation_date_time', ''),
                report_time=msh_info.get('date_time', ''),
                priority=obr_info.get('priority', ''),
                results=test_results,
                metadata=obr_info
            )
            
            # 创建标准消息
            message = StandardMessage(
                message_type="ORU",
                message_id=msh_info.get('message_control_id', ''),
                timestamp=datetime.now(),
                device_info=device_info,
                patient=patient,
                orders=[order],
                raw_message=raw_message,
                metadata={
                    'protocol_version': msh_info.get('version_id', '2.3.1'),
                    'sending_application': msh_info.get('sending_application', ''),
                    'sending_facility': msh_info.get('sending_facility', ''),
                    'results_count': len(test_results)
                }
            )
            
            # 计算并添加有效的YQ值
            effective_yq = self.get_yq_value(message, msh_info.get('sending_facility', ''))
            message.metadata['effective_yq'] = effective_yq
            
            self.logger.info(f"成功解析URIT消息 - 样本: {patient.sample_id}, 结果数: {len(test_results)}")
            return message
            
        except Exception as e:
            raise ProtocolParseError(f"解析URIT ORU消息失败: {str(e)}")
    
    def _parse_msh_segment(self, lines: List[str]) -> Dict[str, str]:
        """解析MSH消息头段"""
        msh_info = {}
        
        for line in lines:
            if line.startswith('MSH|'):
                fields = line.split('|')
                msh_info = {
                    'field_separator': safe_get_field(fields, 1, '|'),
                    'encoding_characters': safe_get_field(fields, 2, '^~\\&'),
                    'sending_application': safe_get_field(fields, 3),
                    'sending_facility': safe_get_field(fields, 4),
                    'receiving_application': safe_get_field(fields, 5),
                    'receiving_facility': safe_get_field(fields, 6),
                    'date_time': safe_get_field(fields, 7),
                    'message_type': safe_get_field(fields, 9),
                    'message_control_id': safe_get_field(fields, 10),
                    'processing_id': safe_get_field(fields, 11),
                    'version_id': safe_get_field(fields, 12, '2.3.1')
                }
                break
        
        return msh_info
    
    def _parse_pid_segment(self, lines: List[str]) -> Dict[str, str]:
        """解析PID患者信息段"""
        pid_info = {}
        
        for line in lines:
            if line.startswith('PID|'):
                fields = line.split('|')
                pid_info = {
                    'set_id': safe_get_field(fields, 1),
                    'patient_id': safe_get_field(fields, 2),
                    'patient_name': safe_get_field(fields, 5),
                    'birth_date': safe_get_field(fields, 7),
                    'sex': safe_get_field(fields, 8),
                    'race': safe_get_field(fields, 10),
                    'address': safe_get_field(fields, 11)
                }
                break
        
        return pid_info
    
    def _process_sample_id(self, raw_sample_id: str) -> str:
        """处理样本ID：截取后4位并去除前导0
        
        Args:
            raw_sample_id: 原始样本ID（如: 201801010001）
            
        Returns:
            处理后的样本ID（如: 1）
        """
        if not raw_sample_id:
            return raw_sample_id
        
        try:
            # 截取后4位
            last_4_digits = raw_sample_id[-4:]
            # 转换为整数再转回字符串，自动去除前导0
            processed_id = str(int(last_4_digits))
            
            self.logger.info(f"样本ID处理: {raw_sample_id} -> {processed_id}")
            return processed_id
            
        except (ValueError, IndexError) as e:
            self.logger.warning(f"样本ID处理失败，使用原值: {raw_sample_id}, 错误: {e}")
            return raw_sample_id

    def _parse_obr_segments(self, lines: List[str]) -> Dict[str, str]:
        """解析OBR观察请求段"""
        obr_info = {}
        
        for line in lines:
            if line.startswith('OBR|'):
                fields = line.split('|')
                raw_sample_id = safe_get_field(fields, 3)
                processed_sample_id = self._process_sample_id(raw_sample_id)
                
                obr_info = {
                    'set_id': safe_get_field(fields, 1),
                    'placer_order_number': safe_get_field(fields, 2),
                    'raw_sample_id': raw_sample_id,  # 保存原始样本ID
                    'sample_id': processed_sample_id,  # 处理后的样本ID
                    'universal_service_id': safe_get_field(fields, 4),
                    'priority': safe_get_field(fields, 5),
                    'observation_date_time': safe_get_field(fields, 7),
                    'specimen_source': safe_get_field(fields, 15),
                    'ordering_provider': safe_get_field(fields, 16)
                }
                break
        
        return obr_info
    
    def _parse_obx_segments(self, lines: List[str]) -> List[Dict[str, str]]:
        """解析OBX观察结果段"""
        obx_results = []
        
        for line in lines:
            if line.startswith('OBX|'):
                fields = line.split('|')
                
                obx_result = {
                    'set_id': safe_get_field(fields, 1),
                    'value_type': safe_get_field(fields, 2),
                    'observation_identifier': safe_get_field(fields, 3),
                    'test_code': safe_get_field(fields, 4),  # 项目代码
                    'value': safe_get_field(fields, 5),      # 结果值
                    'unit': safe_get_field(fields, 6),       # 单位
                    'reference_range': safe_get_field(fields, 7),  # 参考范围
                    'abnormal_flag': safe_get_field(fields, 8),    # 异常标志
                    'observation_result_status': safe_get_field(fields, 11),
                    'observation_date_time': safe_get_field(fields, 14),
                    'producer_id': safe_get_field(fields, 16),
                    'test_name': safe_get_field(fields, 4)  # 使用test_code作为name
                }
                
                obx_results.append(obx_result)
        
        return obx_results
    
    def _limit_field_length(self, value: str, field_type: str) -> str:
        """限制字段长度"""
        if not value:
            return value
        
        max_length = self.field_length_limits.get(field_type, 255)
        if len(value) > max_length:
            self.logger.warning(f"字段 {field_type} 长度超限: {len(value)} > {max_length}")
            return value[:max_length]
        
        return value
    
    def generate_response(self, request: StandardMessage) -> bytes:
        """生成HL7响应消息"""
        try:
            if request.message_type == "ORU":
                return self._generate_ack_response(request)
            else:
                return b"NAK\r"
                
        except Exception as e:
            self.logger.error(f"生成URIT响应失败: {e}")
            return b"NAK\r"
    
    def _generate_ack_response(self, request: StandardMessage) -> bytes:
        """生成ORU的ACK响应"""
        try:
            timestamp = format_datetime()
            message_id = request.message_id or timestamp
            
            # 构建ACK消息
            ack_line = f"MSH|^~\\&|LIS|Hospital|urit|{request.device_info.model}|{timestamp}||ACK^R01|{message_id}|P|2.3.1"
            msa_line = f"MSA|AA|{message_id}|Message accepted"
            
            response = f"{ack_line}\r{msa_line}\r"
            return response.encode('utf-8')
            
        except Exception as e:
            self.logger.error(f"生成URIT ACK响应失败: {e}")
            return b"NAK\r"