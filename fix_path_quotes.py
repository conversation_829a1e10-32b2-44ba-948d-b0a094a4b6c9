#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复Windows环境变量Path中的双引号问题
Fix quotes in Windows PATH environment variable
"""

import os
import sys
import subprocess
import winreg

def check_path_quotes():
    """检查Path环境变量中是否包含双引号"""
    print("正在检查环境变量Path中的双引号...")
    
    # 获取当前Path环境变量
    current_path = os.environ.get('PATH', '')
    path_entries = current_path.split(';')
    
    problematic_entries = []
    for i, entry in enumerate(path_entries):
        if '"' in entry:
            problematic_entries.append((i, entry))
            print(f"发现问题路径 #{i}: {entry}")
    
    if not problematic_entries:
        print("✅ 未发现Path中包含双引号的问题")
        return False
    else:
        print(f"❌ 发现 {len(problematic_entries)} 个包含双引号的路径条目")
        return True

def get_registry_path():
    """从注册表获取系统Path环境变量"""
    try:
        # 打开系统环境变量注册表项
        key = winreg.OpenKey(
            winreg.HKEY_LOCAL_MACHINE,
            r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
            0,
            winreg.KEY_READ
        )
        
        # 读取Path值
        path_value, _ = winreg.QueryValueEx(key, "Path")
        winreg.CloseKey(key)
        
        return path_value
    except Exception as e:
        print(f"无法从注册表读取Path: {e}")
        return None

def fix_path_quotes_registry():
    """通过注册表修复Path中的双引号（需要管理员权限）"""
    try:
        # 获取当前系统Path
        current_path = get_registry_path()
        if not current_path:
            return False
        
        # 移除双引号
        fixed_path = current_path.replace('"', '')
        
        if current_path == fixed_path:
            print("✅ 系统Path中没有双引号需要修复")
            return True
        
        print("正在修复系统Path环境变量...")
        print(f"原始Path: {current_path}")
        print(f"修复后Path: {fixed_path}")
        
        # 打开注册表项进行写入（需要管理员权限）
        key = winreg.OpenKey(
            winreg.HKEY_LOCAL_MACHINE,
            r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
            0,
            winreg.KEY_SET_VALUE
        )
        
        # 设置新的Path值
        winreg.SetValueEx(key, "Path", 0, winreg.REG_EXPAND_SZ, fixed_path)
        winreg.CloseKey(key)
        
        print("✅ 系统Path环境变量已修复")
        print("⚠️  请重启计算机或重新登录以使更改生效")
        return True
        
    except PermissionError:
        print("❌ 需要管理员权限来修改系统环境变量")
        print("请以管理员身份运行此脚本")
        return False
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def show_manual_fix_instructions():
    """显示手动修复说明"""
    print("\n" + "="*60)
    print("手动修复说明:")
    print("="*60)
    print("1. 按 Win + R 打开运行对话框")
    print("2. 输入 'sysdm.cpl' 并按回车")
    print("3. 点击'高级'选项卡")
    print("4. 点击'环境变量'按钮")
    print("5. 在'系统变量'部分找到'Path'变量")
    print("6. 选中并点击'编辑'")
    print("7. 检查每个路径条目，删除包含双引号的条目或移除双引号")
    print("8. 点击'确定'保存更改")
    print("9. 重启VSCode或重新登录Windows")
    print("="*60)

def generate_powershell_fix():
    """生成PowerShell修复脚本"""
    ps_script = '''
# PowerShell脚本：修复Path环境变量中的双引号
# 需要以管理员身份运行PowerShell

Write-Host "正在检查系统Path环境变量..." -ForegroundColor Yellow

# 获取当前系统Path
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")

if ($currentPath -match '"') {
    Write-Host "发现Path中包含双引号，正在修复..." -ForegroundColor Red
    
    # 移除双引号
    $fixedPath = $currentPath -replace '"', ''
    
    # 设置新的Path
    [Environment]::SetEnvironmentVariable("Path", $fixedPath, "Machine")
    
    Write-Host "✅ Path环境变量已修复" -ForegroundColor Green
    Write-Host "请重启计算机或重新登录以使更改生效" -ForegroundColor Yellow
} else {
    Write-Host "✅ Path中未发现双引号问题" -ForegroundColor Green
}

Write-Host "按任意键退出..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
'''
    
    with open('fix_path_quotes.ps1', 'w', encoding='utf-8') as f:
        f.write(ps_script)
    
    print(f"✅ 已生成PowerShell修复脚本: fix_path_quotes.ps1")
    print("使用方法:")
    print("1. 以管理员身份打开PowerShell")
    print("2. 运行: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser")
    print("3. 运行: .\\fix_path_quotes.ps1")

def main():
    print("Windows Path环境变量双引号修复工具")
    print("="*50)
    
    # 检查是否存在问题
    has_quotes = check_path_quotes()
    
    if not has_quotes:
        print("✅ 没有发现问题，无需修复")
        return
    
    print("\n选择修复方法:")
    print("1. 尝试自动修复（需要管理员权限）")
    print("2. 生成PowerShell修复脚本")
    print("3. 显示手动修复说明")
    print("4. 退出")
    
    while True:
        choice = input("\n请选择 (1-4): ").strip()
        
        if choice == '1':
            if fix_path_quotes_registry():
                print("修复完成！")
            break
        elif choice == '2':
            generate_powershell_fix()
            break
        elif choice == '3':
            show_manual_fix_instructions()
            break
        elif choice == '4':
            print("退出程序")
            break
        else:
            print("无效选择，请输入1-4")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
