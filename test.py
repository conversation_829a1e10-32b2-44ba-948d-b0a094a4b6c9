#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
URIT设备LIS系统集成测试

该测试脚本验证URIT设备从消息接收到数据库写入的完整流程
"""

import logging
import sys
import os
from datetime import datetime
import traceback

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

def test_imports():
    """测试模块导入"""
    print("=" * 60)
    print("1. 测试模块导入")
    print("=" * 60)
    
    try:
        from adapters.urit_adapter import URITAdapter
        print("OK URITAdapter 导入成功")
        
        from device_adapter import DeviceInfo, StandardMessage
        print("OK DeviceInfo, StandardMessage 导入成功")
        
        from message_router import MessageRouter, DeviceIdentifier
        print("OK MessageRouter, DeviceIdentifier 导入成功")
        
        from config_manager import ConfigManager
        print("OK ConfigManager 导入成功")
        
        print("\nOK 所有必要模块导入成功")
        return True
        
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_urit_adapter():
    """测试URIT适配器功能"""
    print("\n" + "=" * 60)
    print("2. 测试URIT适配器功能")
    print("=" * 60)
    
    try:
        from adapters.urit_adapter import URITAdapter
        
        # 创建适配器
        config = {
            'field_length_limits': {
                'patient_id': 50,
                'sample_id': 50,
                'test_code': 20,
                'test_value': 200
            },
            'test_mapping': {
                'ALT': 'ALT',
                'AST': 'AST'
            }
        }
        
        adapter = URITAdapter(config)
        print(f"OK URIT适配器创建成功")
        print(f"  协议名称: {adapter.protocol_name}")
        print(f"  制造商: {adapter.manufacturer}")
        print(f"  支持版本: {adapter.supported_versions}")
        
        return adapter
        
    except Exception as e:
        print(f"✗ URIT适配器测试失败: {e}")
        traceback.print_exc()
        return None

def test_message_recognition(adapter):
    """测试消息识别功能"""
    print("\n" + "=" * 60)
    print("3. 测试消息识别功能")
    print("=" * 60)
    
    # 您提供的实际URIT消息
    test_messages = [
        {
            'name': 'URIT消息1',
            'data': b"MSH|^~\\&|urit|8030|||20180101010440||ORU^R01|201801010001|P|2.3.1||||0||ASCII|||\rPID|1||||||0|||||0|||||||||||||||||||\rOBR|1||201801010001|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||\rOBX|1|NM|1|ALT|14.7|U/L|0.0-40.0|N|||F||-0.0049|2018-01-01||"
        },
        {
            'name': 'URIT消息2',
            'data': b"MSH|^~\\&|urit|8030|||20180101010441||ORU^R01|201801010002|P|2.3.1||||0||ASCII|||\rPID|1||||||0|||||0|||||||||||||||||||\rOBR|1||201801010002|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||\rOBX|1|NM|1|ALT|13.8|U/L|0.0-40.0|N|||F||-0.0046|2018-01-01||"
        },
        {
            'name': '非URIT消息',
            'data': b"MSH|^~\\&|other|device|||20180101||ADT^A01|123|P|2.5"
        }
    ]
    
    connection_info = {'address': ('*************', 22010)}
    
    for msg in test_messages:
        can_handle = adapter.can_handle(msg['data'], connection_info)
        status = "OK 识别成功" if can_handle else "X 无法识别"
        expected = "应该识别" if msg['name'].startswith("URIT") else "应该忽略"
        
        
        print(f"{msg['name']}: {status} ({expected})")
        
        # 验证识别结果
        if msg['name'].startswith("URIT") and not can_handle:
            print(f"  X 错误: URIT消息应该被识别")
            return False
        elif msg['name'].startswith("非URIT") and can_handle:
            print(f"  X 错误: 非URIT消息不应该被识别")
            return False
        else:
            print(f"  OK 识别结果正确")
    
    print("\n✓ 消息识别功能测试通过")
    return True

def test_message_parsing(adapter):
    """测试消息解析功能"""
    print("\n" + "=" * 60)
    print("4. 测试消息解析功能")  
    print("=" * 60)
    
    urit_message = b"MSH|^~\\&|urit|8030|||20180101010440||ORU^R01|201801010001|P|2.3.1||||0||ASCII|||\rPID|1||||||0|||||0|||||||||||||||||||\rOBR|1||201801010001|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||\rOBX|1|NM|1|ALT|14.7|U/L|0.0-40.0|N|||F||-0.0049|2018-01-01||"
    
    connection_info = {'address': ('*************', 22010)}
    
    try:
        standard_msg = adapter.parse_message(urit_message, connection_info)
        
        print("✓ 消息解析成功")
        print(f"  消息类型: {standard_msg.message_type}")
        print(f"  消息ID: {standard_msg.message_id}")
        print(f"  设备类型: {standard_msg.device_info.device_type}")
        print(f"  设备型号: {standard_msg.device_info.model}")
        print(f"  设备IP: {standard_msg.device_info.ip_address}")
        
        # 测试样本ID处理
        original_sample_id = standard_msg.orders[0].metadata.get('raw_sample_id', '')
        processed_sample_id = standard_msg.patient.sample_id
        print(f"  原始样本ID: {original_sample_id}")
        print(f"  处理后样本ID: {processed_sample_id}")
        
        # 验证样本ID处理
        if original_sample_id == "201801010001" and processed_sample_id == "1":
            print("  ✓ 样本ID处理正确: 201801010001 -> 1")
        else:
            print(f"  ✗ 样本ID处理错误: {original_sample_id} -> {processed_sample_id}")
            return False, None
        
        # 测试检验结果
        if standard_msg.orders and standard_msg.orders[0].results:
            result = standard_msg.orders[0].results[0]  
            print(f"  检验项目: {result.test_code}")
            print(f"  检验结果: {result.value} {result.unit}")
            print(f"  参考范围: {result.reference_range}")
            print(f"  异常标志: {result.abnormal_flag}")
            
            # 验证检验结果
            if result.test_code == "ALT" and result.value == "14.7" and result.unit == "U/L":
                print("  ✓ 检验结果解析正确")
            else:
                print("  ✗ 检验结果解析错误")
                return False, None
        else:
            print("  ✗ 未找到检验结果")
            return False, None
        
        print("\n✓ 消息解析功能测试通过")
        return True, standard_msg
        
    except Exception as e:
        print(f"✗ 消息解析失败: {e}")
        traceback.print_exc()
        return False, None

def test_yq_customization(adapter, standard_msg):
    """测试YQ自定义功能"""
    print("\n" + "=" * 60)
    print("5. 测试YQ自定义功能")
    print("=" * 60)
    
    try:
        # 设置YQ配置
        device_info = standard_msg.device_info
        device_info.yq_config = {
            'yq_source': 'custom',
            'yq_value': 'URIT8030'
        }
        
        # 测试YQ处理
        original_yq = "8030"
        effective_yq = adapter.get_yq_value(standard_msg, original_yq)
        
        print(f"原始YQ值: {original_yq}")
        print(f"有效YQ值: {effective_yq}")
        
        if effective_yq == "URIT8030":
            print("✓ YQ自定义功能正常")
            return True
        else:
            print(f"✗ YQ自定义功能异常: 预期URIT8030, 实际{effective_yq}")
            return False
            
    except Exception as e:
        print(f"✗ YQ自定义测试失败: {e}")
        traceback.print_exc()
        return False

def test_response_generation(adapter, standard_msg):
    """测试响应生成功能"""
    print("\n" + "=" * 60)
    print("6. 测试响应生成功能")
    print("=" * 60)
    
    try:
        response = adapter.generate_response(standard_msg)
        response_str = response.decode('utf-8')
        
        print("生成的响应消息:")
        print("-" * 40)
        print(response_str)
        print("-" * 40)
        
        # 验证响应内容
        if "ACK^R01" in response_str and "AA" in response_str:
            print("✓ 响应生成功能正常")
            return True
        else:
            print("✗ 响应格式异常")
            return False
            
    except Exception as e:
        print(f"✗ 响应生成失败: {e}")
        traceback.print_exc()
        return False

def test_database_simulation(standard_msg):
    """测试数据库操作模拟"""
    print("\n" + "=" * 60)
    print("7. 测试数据库操作模拟")
    print("=" * 60)
    
    try:
        # 模拟获取有效YQ值
        effective_yq = "URIT8030"  # 从YQ自定义功能获取
        
        # 患者数据
        patient_data = {
            'wyh': standard_msg.message_id,
            'jyrq': datetime.now().strftime('%Y-%m-%d 00:00:00.000'),
            'yq': effective_yq,
            'ybh': standard_msg.patient.sample_id,
            'yblx': '',
            'fb': '', 'ksdh': '', 'cwh': '', 'brxm': '', 'xb': '', 'nl': '',
            'brly': '', 'brdh': '', 'zd': '', 'bz': '', 'bz1': ''
        }
        
        # 结果数据
        if standard_msg.orders and standard_msg.orders[0].results:
            result = standard_msg.orders[0].results[0]
            result_data = {
                'wyh': standard_msg.message_id,
                'jyrq': datetime.now().strftime('%Y-%m-%d 00:00:00.000'),
                'yq': effective_yq,
                'ybh': standard_msg.patient.sample_id,
                'xmdh': result.test_code,
                'csjg': result.value,
                'instrid': '0',
                'refs': result.reference_range,
                'result_flag': result.abnormal_flag,
                'bz1': '', 'bz2': '', 'textresult': '',
                'num_result': result.value
            }
        else:
            print("✗ 无检验结果数据")
            return False
        
        # 显示模拟的数据库操作
        print("模拟患者表插入:")
        print(f"  INSERT INTO lis_pat (...) VALUES (")
        print(f"    wyh='{patient_data['wyh']}',")
        print(f"    yq='{patient_data['yq']}',")
        print(f"    ybh='{patient_data['ybh']}',")
        print(f"    jyrq='{patient_data['jyrq']}'")
        print(f"  )")
        
        print("\n模拟结果表插入:")
        print(f"  INSERT INTO lis_result (...) VALUES (")
        print(f"    wyh='{result_data['wyh']}',")
        print(f"    yq='{result_data['yq']}',")
        print(f"    ybh='{result_data['ybh']}',")
        print(f"    xmdh='{result_data['xmdh']}',")
        print(f"    csjg='{result_data['csjg']}',")
        print(f"    refs='{result_data['refs']}',")
        print(f"    jyrq='{result_data['jyrq']}'")
        print(f"  )")
        
        # 验证关键字段
        print("\n关键字段验证:")
        print(f"  ✓ YQ字段: {effective_yq} (自定义值)")
        print(f"  ✓ 样本ID: {patient_data['ybh']} (处理后)")
        print(f"  ✓ 项目代码: {result_data['xmdh']}")
        print(f"  ✓ 结果值: {result_data['csjg']}")
        
        print("\n✓ 数据库操作模拟测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 数据库操作模拟失败: {e}")
        traceback.print_exc()
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n" + "=" * 60)
    print("8. 测试配置加载")
    print("=" * 60)
    
    try:
        from config_manager import ConfigManager
        
        # 检查配置文件是否存在
        config_file = "config_multi_device.ini"
        if not os.path.exists(config_file):
            print(f"✗ 配置文件不存在: {config_file}")
            return False
        
        # 加载配置
        config_manager = ConfigManager(config_file)
        
        # 检查多设备模式
        multi_device_enabled = config_manager.is_multi_device_enabled()
        print(f"多设备模式: {'启用' if multi_device_enabled else '禁用'}")
        
        # 检查URIT设备配置
        devices_config = config_manager.get_devices_config()
        urit_found = False
        for device_id, device_config in devices_config.items():
            if 'urit' in device_id.lower():
                urit_found = True
                print(f"✓ 找到URIT设备配置: {device_id}")
                print(f"  设备类型: {device_config.get('device_type')}")
                print(f"  适配器: {device_config.get('adapter')}")
                print(f"  YQ配置: {device_config.get('yq_source')} = {device_config.get('yq_value')}")
                break
        
        if not urit_found:
            print("✗ 未找到URIT设备配置")
            return False
        
        # 检查适配器配置
        adapters_config = config_manager.get_adapters_config()
        if 'urit' in adapters_config:
            print("✓ 找到URIT适配器配置")
            urit_adapter_config = adapters_config['urit']
            print(f"  模块: {urit_adapter_config.get('module')}")
            print(f"  类名: {urit_adapter_config.get('class')}")
        else:
            print("✗ 未找到URIT适配器配置")
            return False
        
        print("\n✓ 配置加载测试通过")  
        return True
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        traceback.print_exc()
        return False

def test_integration():
    """完整集成测试"""
    print("\n" + "=" * 60)
    print("9. 完整集成测试")
    print("=" * 60)
    
    try:
        from message_router import MessageRouter
        from config_manager import ConfigManager
        
        # 加载配置
        config_manager = ConfigManager("config_multi_device.ini")
        router_config = {
            'adapters': config_manager.get_adapters_config(),
            'devices': config_manager.get_devices_config(),
            'router': config_manager.get_router_config()
        }
        
        # 创建消息路由器
        router = MessageRouter(router_config)
        print("✓ 消息路由器创建成功")
        
        # 测试消息路由
        urit_message = b"MSH|^~\\&|urit|8030|||20180101010440||ORU^R01|201801010001|P|2.3.1||||0||ASCII|||\rPID|1||||||0|||||0|||||||||||||||||||\rOBR|1||201801010001|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||\rOBX|1|NM|1|ALT|14.7|U/L|0.0-40.0|N|||F||-0.0049|2018-01-01||"
        
        connection_info = {'address': ('*************', 22010)}
        
        # 路由消息
        standard_msg = router.route_message(urit_message, connection_info)
        print("✓ 消息路由成功")
        print(f"  识别为设备: {standard_msg.device_info.device_type}")
        print(f"  使用适配器: {standard_msg.device_info.metadata.get('adapter')}")
        
        # 生成响应
        response = router.generate_response(standard_msg)
        print("✓ 响应生成成功")
        
        # 获取统计信息
        stats = router.get_statistics()
        print(f"✓ 路由统计: 成功{stats['messages_routed']}条, 失败{stats['messages_failed']}条")
        
        print("\n✓ 完整集成测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 集成测试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("URIT设备LIS系统集成测试")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试模块导入
    test_results.append(("模块导入", test_imports()))
    
    if not test_results[-1][1]:
        print("\n❌ 基础模块导入失败，无法继续测试")
        return False
    
    # 2. 测试适配器
    adapter = test_urit_adapter()
    test_results.append(("URIT适配器", adapter is not None))
    
    if adapter is None:
        print("\n❌ 适配器创建失败，无法继续测试")
        return False
    
    # 3. 测试消息识别
    test_results.append(("消息识别", test_message_recognition(adapter)))
    
    # 4. 测试消息解析
    parsing_success, standard_msg = test_message_parsing(adapter)
    test_results.append(("消息解析", parsing_success))
    
    if not parsing_success or standard_msg is None:
        print("\n❌ 消息解析失败，跳过后续测试")
    else:
        # 5. 测试YQ自定义
        test_results.append(("YQ自定义", test_yq_customization(adapter, standard_msg)))
        
        # 6. 测试响应生成
        test_results.append(("响应生成", test_response_generation(adapter, standard_msg)))
        
        # 7. 测试数据库模拟
        test_results.append(("数据库模拟", test_database_simulation(standard_msg)))
    
    # 8. 测试配置加载
    test_results.append(("配置加载", test_config_loading()))
    
    # 9. 完整集成测试
    test_results.append(("集成测试", test_integration()))
    
    # 显示测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:<12}: {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print("-" * 60)
    print(f"总计: {len(test_results)}项测试, {passed}项通过, {failed}项失败")
    
    if failed == 0:
        print("\n🎉 所有测试通过！URIT设备集成功能正常")
        print("\n可以开始使用LIS系统接收URIT设备数据了:")
        print("1. 确保URIT设备网络配置正确")
        print("2. 启动LIS系统: python lis_system.py")
        print("3. URIT设备数据将自动解码并写入数据库")
        return True
    else:
        print(f"\n❌ 有{failed}项测试失败，请检查配置和代码")
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n测试过程中发生未预期的错误: {e}")
        traceback.print_exc()
        sys.exit(1)