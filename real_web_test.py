#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
URIT设备Web测试界面真实测试脚本
"""

import sys
import json
from test_web_interface import app

def run_real_test():
    """运行真实的Web API测试"""
    
    # 创建测试客户端
    client = app.test_client()
    
    print('开始URIT设备Web测试界面真实测试')
    print('=' * 60)
    
    test_results = []
    
    # 测试1: 系统初始化
    print('\n测试1: 系统初始化')
    try:
        response = client.post('/api/init')
        data = response.get_json()
        if data['success']:
            print('OK 系统初始化成功')
            print(f'   消息: {data["message"]}')
            test_results.append(('系统初始化', True))
        else:
            print('X 系统初始化失败')
            print(f'   错误: {data["error"]}')
            test_results.append(('系统初始化', False))
    except Exception as e:
        print(f'X 系统初始化异常: {e}')
        test_results.append(('系统初始化', False))
    
    # 测试2: 消息识别
    print('\n测试2: 消息识别测试')
    try:
        response = client.post('/api/test-recognition')
        data = response.get_json()
        if data['success']:
            print('OK 消息识别测试通过')
            details = data["details"].split('\n')
            for detail in details:
                if detail.strip():
                    print(f'   {detail}')
            test_results.append(('消息识别', True))
        else:
            print('X 消息识别测试失败')
            print(f'   错误: {data["error"]}')
            test_results.append(('消息识别', False))
    except Exception as e:
        print(f'X 消息识别异常: {e}')
        test_results.append(('消息识别', False))
    
    # 测试3: 消息解析
    print('\n测试3: 消息解析测试')
    try:
        test_message = 'MSH|^~\\&|urit|8030|||20180101010440||ORU^R01|201801010001|P|2.3.1||||0||ASCII|||\rPID|1||||||0|||||0|||||||||||||||||||\rOBR|1||201801010001|urit^8030|N||2018-01-01|||||||||||||||||||||||||||||||||||||||\rOBX|1|NM|1|ALT|14.7|U/L|0.0-40.0|N|||F||-0.0049|2018-01-01||'
        response = client.post('/api/test-parsing', json={'message': test_message})
        data = response.get_json()
        if data['success']:
            print('OK 消息解析测试通过')
            details = data["details"].split('\n')
            for detail in details:
                if detail.strip():
                    print(f'   {detail}')
            test_results.append(('消息解析', True))
        else:
            print('X 消息解析测试失败')
            print(f'   错误: {data["error"]}')
            test_results.append(('消息解析', False))
    except Exception as e:
        print(f'X 消息解析异常: {e}')
        test_results.append(('消息解析', False))
    
    # 测试4: 样本ID处理
    print('\n测试4: 样本ID处理测试')
    try:
        test_samples = ['201801010001', '201801010002', '201801010123', '201801010000']
        all_passed = True
        
        for sample_id in test_samples:
            response = client.post('/api/test-sample-id', json={'sample_id': sample_id})
            data = response.get_json()
            if data['success']:
                details_lines = data['details'].split('\n')
                processed_line = [line for line in details_lines if '处理后样本ID:' in line]
                if processed_line:
                    processed = processed_line[0].split('处理后样本ID: ')[1]
                    print(f'   {sample_id} -> {processed}')
                else:
                    print(f'   {sample_id} -> 解析结果失败')
                    all_passed = False
            else:
                print(f'   {sample_id} -> 错误: {data["error"]}')
                all_passed = False
        
        if all_passed:
            print('OK 样本ID处理测试通过')
            test_results.append(('样本ID处理', True))
        else:
            print('X 部分样本ID处理失败')
            test_results.append(('样本ID处理', False))
            
    except Exception as e:
        print(f'X 样本ID处理异常: {e}')
        test_results.append(('样本ID处理', False))
    
    # 测试5: YQ自定义
    print('\n测试5: YQ自定义测试')
    try:
        response = client.post('/api/test-yq', json={'mode': 'custom', 'value': 'URIT8030'})
        data = response.get_json()
        if data['success']:
            print('OK YQ自定义测试通过')
            details = data["details"].split('\n')
            for detail in details:
                if detail.strip():
                    print(f'   {detail}')
            test_results.append(('YQ自定义', True))
        else:
            print('X YQ自定义测试失败')
            print(f'   错误: {data["error"]}')
            test_results.append(('YQ自定义', False))
    except Exception as e:
        print(f'X YQ自定义异常: {e}')
        test_results.append(('YQ自定义', False))
    
    # 测试6: 数据库模拟
    print('\n测试6: 数据库操作模拟')
    try:
        response = client.post('/api/test-database')
        data = response.get_json()
        if data['success']:
            print('OK 数据库模拟测试通过')
            print('   生成的SQL语句 (摘要):')
            sql_lines = data['sql_statements'].split('\n')
            for i, line in enumerate(sql_lines):
                if i >= 5:  # 只显示前5行
                    break
                if line.strip() and not line.startswith('INSERT'):
                    print(f'   {line}')
                elif line.startswith('INSERT'):
                    print(f'   {line[:50]}...')
            test_results.append(('数据库模拟', True))
        else:
            print('X 数据库模拟测试失败')
            print(f'   错误: {data["error"]}')
            test_results.append(('数据库模拟', False))
    except Exception as e:
        print(f'X 数据库模拟异常: {e}')
        test_results.append(('数据库模拟', False))
    
    # 测试7: 完整流程
    print('\n测试7: 完整流程测试')
    try:
        response = client.post('/api/test-complete')
        data = response.get_json()
        if data['success']:
            print('OK 完整流程测试通过')
            details = data["details"].split('\n')
            for i, detail in enumerate(details):
                if i >= 8:  # 只显示前8行
                    break
                if detail.strip():
                    print(f'   {detail}')
            test_results.append(('完整流程', True))
        else:
            print('X 完整流程测试失败')
            print(f'   错误: {data["error"]}')
            test_results.append(('完整流程', False))
    except Exception as e:
        print(f'X 完整流程异常: {e}')
        test_results.append(('完整流程', False))
    
    # 测试8: 系统统计
    print('\n测试8: 系统统计')
    try:
        response = client.post('/api/stats')
        data = response.get_json()
        if data['success']:
            print('OK 系统统计获取成功')
            stats_lines = data['stats'].split('\n')
            for i, line in enumerate(stats_lines):
                if i >= 8:  # 只显示前8行
                    break
                if line.strip():
                    print(f'   {line}')
            test_results.append(('系统统计', True))
        else:
            print('X 系统统计获取失败')
            print(f'   错误: {data["error"]}')
            test_results.append(('系统统计', False))
    except Exception as e:
        print(f'X 系统统计异常: {e}')
        test_results.append(('系统统计', False))
    
    # 汇总测试结果
    print('\n' + '=' * 60)
    print('Web测试界面真实测试结果汇总')
    print('=' * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = 'OK 通过' if result else 'X 失败'
        print(f'{test_name:<12}: {status}')
        if result:
            passed += 1
        else:
            failed += 1
    
    print('-' * 60)
    print(f'总计: {len(test_results)}项测试, {passed}项通过, {failed}项失败')
    
    if failed == 0:
        print('\n所有Web API测试通过！Web界面功能正常')
        print('现在可以启动Web服务器进行手动测试:')
        print('  python web_test_runner.py')
        print('  访问: http://localhost:5000')
        return True
    else:
        print(f'\n有{failed}项测试失败，请检查相关功能')
        return False

if __name__ == '__main__':
    try:
        success = run_real_test()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f'测试过程中发生错误: {e}')
        import traceback
        traceback.print_exc()
        sys.exit(1)