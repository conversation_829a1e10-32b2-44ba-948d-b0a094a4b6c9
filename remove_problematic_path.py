#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除Path环境变量中的特定问题路径
Remove specific problematic path from PATH environment variable
"""

import os
import sys
import winreg
import subprocess

def get_system_path():
    """从注册表获取系统Path环境变量"""
    try:
        key = winreg.OpenKey(
            winreg.HKEY_LOCAL_MACHINE,
            r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
            0,
            winreg.KEY_READ
        )
        path_value, _ = winreg.QueryValueEx(key, "Path")
        winreg.CloseKey(key)
        return path_value
    except Exception as e:
        print(f"无法从注册表读取Path: {e}")
        return None

def set_system_path(new_path):
    """设置系统Path环境变量"""
    try:
        key = winreg.OpenKey(
            winreg.HKEY_LOCAL_MACHINE,
            r"SYSTEM\CurrentControlSet\Control\Session Manager\Environment",
            0,
            winreg.KEY_SET_VALUE
        )
        winreg.SetValueEx(key, "Path", 0, winreg.REG_EXPAND_SZ, new_path)
        winreg.Close<PERSON><PERSON>(key)
        return True
    except PermissionError:
        print("❌ 需要管理员权限来修改系统环境变量")
        return False
    except Exception as e:
        print(f"❌ 设置Path失败: {e}")
        return False

def remove_problematic_path():
    """删除问题路径"""
    print("正在删除Path中的问题路径...")
    
    # 获取当前系统Path
    current_path = get_system_path()
    if not current_path:
        return False
    
    print(f"当前Path长度: {len(current_path)} 字符")
    
    # 分割路径条目
    path_entries = current_path.split(';')
    print(f"当前Path包含 {len(path_entries)} 个条目")
    
    # 要删除的问题路径
    problematic_paths = [
        '"c:\\lis2002\\common"',
        '"c:/lis2002/common"',
        'c:\\lis2002\\common',
        'c:/lis2002/common'
    ]
    
    # 查找并删除问题路径
    original_count = len(path_entries)
    cleaned_entries = []
    removed_paths = []
    
    for entry in path_entries:
        entry_clean = entry.strip()
        if entry_clean.lower() in [p.lower() for p in problematic_paths]:
            removed_paths.append(entry_clean)
            print(f"🗑️  删除问题路径: {entry_clean}")
        else:
            if entry_clean:  # 只保留非空条目
                cleaned_entries.append(entry_clean)
    
    if not removed_paths:
        print("✅ 未找到需要删除的问题路径")
        return True
    
    # 重新组合Path
    new_path = ';'.join(cleaned_entries)
    
    print(f"\n修改摘要:")
    print(f"原始条目数: {original_count}")
    print(f"删除条目数: {len(removed_paths)}")
    print(f"最终条目数: {len(cleaned_entries)}")
    print(f"删除的路径: {removed_paths}")
    
    # 设置新的Path
    if set_system_path(new_path):
        print("✅ 问题路径已成功删除")
        print("⚠️  请重启VSCode或重新登录以使更改生效")
        return True
    else:
        return False

def generate_powershell_script():
    """生成PowerShell删除脚本"""
    ps_script = '''
# PowerShell脚本：删除Path中的问题路径
# 需要以管理员身份运行

Write-Host "正在删除Path中的问题路径..." -ForegroundColor Yellow

# 获取当前系统Path
$currentPath = [Environment]::GetEnvironmentVariable("Path", "Machine")

# 分割路径条目
$pathEntries = $currentPath -split ';'
Write-Host "当前Path包含 $($pathEntries.Count) 个条目" -ForegroundColor Cyan

# 要删除的问题路径
$problematicPaths = @(
    '"c:\\lis2002\\common"',
    '"c:/lis2002/common"',
    'c:\\lis2002\\common',
    'c:/lis2002/common'
)

# 过滤掉问题路径
$cleanedEntries = @()
$removedPaths = @()

foreach ($entry in $pathEntries) {
    $entryClean = $entry.Trim()
    $isProblematic = $false
    
    foreach ($problematicPath in $problematicPaths) {
        if ($entryClean -ieq $problematicPath) {
            $removedPaths += $entryClean
            Write-Host "🗑️  删除问题路径: $entryClean" -ForegroundColor Red
            $isProblematic = $true
            break
        }
    }
    
    if (-not $isProblematic -and $entryClean -ne "") {
        $cleanedEntries += $entryClean
    }
}

if ($removedPaths.Count -eq 0) {
    Write-Host "✅ 未找到需要删除的问题路径" -ForegroundColor Green
} else {
    # 重新组合Path
    $newPath = $cleanedEntries -join ';'
    
    Write-Host "`n修改摘要:" -ForegroundColor Yellow
    Write-Host "原始条目数: $($pathEntries.Count)" -ForegroundColor Cyan
    Write-Host "删除条目数: $($removedPaths.Count)" -ForegroundColor Cyan
    Write-Host "最终条目数: $($cleanedEntries.Count)" -ForegroundColor Cyan
    Write-Host "删除的路径: $($removedPaths -join ', ')" -ForegroundColor Red
    
    # 设置新的Path
    try {
        [Environment]::SetEnvironmentVariable("Path", $newPath, "Machine")
        Write-Host "✅ 问题路径已成功删除" -ForegroundColor Green
        Write-Host "⚠️  请重启VSCode或重新登录以使更改生效" -ForegroundColor Yellow
    } catch {
        Write-Host "❌ 删除失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请确保以管理员身份运行此脚本" -ForegroundColor Yellow
    }
}

Write-Host "`n按任意键退出..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
'''
    
    with open('remove_problematic_path.ps1', 'w', encoding='utf-8') as f:
        f.write(ps_script)
    
    print("✅ 已生成PowerShell删除脚本: remove_problematic_path.ps1")

def main():
    print("删除Path环境变量中的问题路径")
    print("目标路径: \"c:\\lis2002\\common\"")
    print("="*50)
    
    print("\n选择操作:")
    print("1. 尝试直接删除（需要管理员权限）")
    print("2. 生成PowerShell删除脚本")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            if remove_problematic_path():
                print("\n🎉 删除完成！")
            break
        elif choice == '2':
            generate_powershell_script()
            print("\n使用方法:")
            print("1. 以管理员身份打开PowerShell")
            print("2. 运行: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser")
            print("3. 运行: .\\remove_problematic_path.ps1")
            break
        elif choice == '3':
            print("退出程序")
            break
        else:
            print("无效选择，请输入1-3")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")
