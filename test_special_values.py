#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特殊格式检验结果值的处理
测试 >1000.000, <0.001 等格式的值是否能正确处理
"""

def test_value_parsing():
    """测试各种格式的检验结果值解析"""
    
    test_cases = [
        # 正常数值
        "123.45",
        "0.001",
        "1000.000",
        
        # 包含比较符号的值
        ">1000.000",
        "<0.001",
        ">=500.0",
        "<=10.5",
        
        # 包含其他特殊符号的值
        "±5.2",
        "+123.4",
        "阴性",
        "阳性",
        "正常",
        
        # 边界情况
        "",
        " ",
        "无结果",
        "N/A"
    ]
    
    print("=== 检验结果值解析测试 ===\n")
    
    for raw_value in test_cases:
        print(f"原始值: '{raw_value}'")
        
        # 模拟修改后的解析逻辑
        parsed_value = raw_value  # 默认保持原始字符串
        
        if raw_value and raw_value.strip():
            # 尝试直接转换为数值
            try:
                parsed_value = float(raw_value.strip())
                print(f"  -> 数值解析成功: {parsed_value}")
            except (ValueError, TypeError):
                # 如果包含比较符号，保持原始字符串格式
                if any(symbol in raw_value for symbol in ['>', '<', '>=', '<=', '±', '+']):
                    parsed_value = raw_value.strip()
                    print(f"  -> 检测到特殊格式，保持原样: '{parsed_value}'")
                else:
                    # 其他无法解析的情况，保持原始值
                    parsed_value = raw_value.strip()
                    print(f"  -> 无法解析为数值，保持原样: '{parsed_value}'")
        else:
            # 空值情况
            parsed_value = ''
            print(f"  -> 空值: '{parsed_value}'")
        
        # 模拟数据库存储阶段的处理
        result_str = str(parsed_value)
        
        # 检查是否包含比较符号或特殊字符
        if any(symbol in result_str for symbol in ['>', '<', '>=', '<=', '±', '+']):
            print(f"  -> 数据库存储: 特殊格式，保持原样 '{result_str}'")
        else:
            # 尝试按数值格式化处理（假设小数位数为3）
            try:
                value = float(parsed_value)
                xsws = 3  # 假设小数位数为3
                rounded_value = round(value, xsws)
                formatted_result = f"{rounded_value:.{xsws}f}"
                print(f"  -> 数据库存储: 数值格式化 '{formatted_result}'")
            except (ValueError, TypeError):
                print(f"  -> 数据库存储: 非数值，保持原样 '{result_str}'")
        
        print()

def test_problematic_cases():
    """测试可能出现问题的特殊情况"""
    
    print("=== 问题案例测试 ===\n")
    
    problematic_cases = [
        ">1000.000",  # 您提到的具体案例
        "<0.001",
        ">=999.999",
        "<=0.005",
        ">10000",
        "<1",
        "±2.5",
        "+15.8"
    ]
    
    for case in problematic_cases:
        print(f"测试案例: '{case}'")
        
        # 旧的处理方式（会出问题）
        try:
            old_result = float(case)
            print(f"  旧方式: 成功转换为 {old_result}")
        except (ValueError, TypeError):
            print(f"  旧方式: 转换失败，会被设置为 0.0 ❌")
        
        # 新的处理方式
        if any(symbol in case for symbol in ['>', '<', '>=', '<=', '±', '+']):
            new_result = case.strip()
            print(f"  新方式: 检测到特殊格式，保持原样 '{new_result}' ✅")
        else:
            try:
                new_result = float(case)
                print(f"  新方式: 转换为数值 {new_result} ✅")
            except (ValueError, TypeError):
                new_result = case.strip()
                print(f"  新方式: 保持原样 '{new_result}' ✅")
        
        print()

if __name__ == "__main__":
    test_value_parsing()
    test_problematic_cases()
    
    print("=== 总结 ===")
    print("✅ 修改后的代码能够正确处理包含比较符号的检验结果值")
    print("✅ >1000.000 这样的值不会被错误地转换为 0.0")
    print("✅ 特殊格式的值会被保持原样存储到数据库")
    print("✅ 正常的数值仍然会按照小数位数要求进行格式化")
