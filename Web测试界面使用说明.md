# URIT设备LIS系统Web测试界面使用说明

## 🚀 快速启动

### 方法1: 使用启动脚本（推荐）
```bash
python web_test_runner.py
```

### 方法2: 直接启动
```bash
python test_web_interface.py
```

### 方法3: 使用批处理文件
```bash
启动Web测试界面.bat
```

## 📱 访问地址

启动成功后，在浏览器中访问：
- **本地访问**: http://localhost:5000
- **局域网访问**: http://您的IP地址:5000

## 🧪 测试功能详解

### 1. 系统初始化 📋
- **功能**: 加载URIT适配器和配置文件
- **作用**: 初始化必要组件，为后续测试做准备
- **状态指示**: 页面顶部显示系统状态（红色=未就绪，绿色=已就绪）

### 2. 消息识别测试 🔍
- **功能**: 测试系统识别URIT设备消息的能力
- **测试内容**: 
  - URIT消息1: 标准URIT HL7消息（应该识别）
  - URIT消息2: 另一条URIT消息（应该识别）
  - 非URIT消息: 其他设备消息（应该忽略）
- **验证点**: 确保只识别URIT消息，忽略其他设备

### 3. 消息解析测试 📄
- **功能**: 解析HL7消息并提取关键信息
- **支持功能**: 
  - 可以输入自定义HL7消息
  - 预填充了标准URIT消息示例
- **解析内容**:
  - 消息类型 (ORU^R01)
  - 消息ID
  - 设备信息
  - 样本ID（原始和处理后）
  - 检验结果详情

### 4. 样本ID处理测试 🔢
- **功能**: 测试样本ID的截取和处理规则
- **处理规则**: 
  - 截取后4位数字
  - 去除前导0
- **示例**:
  - `201801010001` → `0001` → `1`
  - `201801010123` → `0123` → `123`
  - `201801010000` → `0000` → `0`

### 5. YQ自定义测试 ⚙️
- **功能**: 测试仪器标识(YQ)的自定义配置
- **支持模式**:
  - **自定义值**: 使用固定的自定义值（如URIT8030）
  - **设备ID模式**: 基于设备ID生成
  - **映射模式**: 根据消息内容映射
  - **消息原值**: 使用消息中的原始值
- **默认配置**: URIT8030

### 6. 数据库操作模拟 🗃️
- **功能**: 生成数据库插入SQL语句
- **包含表**:
  - `lis_pat`: 患者/样本信息表
  - `lis_result`: 检验结果表
- **关键字段验证**:
  - YQ字段使用自定义值
  - 样本ID使用处理后的值
  - 完整的检验结果数据

### 7. 完整流程测试 🔄
- **功能**: 端到端的完整流程验证
- **测试步骤**:
  1. 接收URIT设备HL7消息
  2. 识别设备类型和协议
  3. 解析消息内容和检验结果
  4. 处理样本ID
  5. 应用YQ自定义配置
  6. 生成数据库插入SQL
  7. 返回ACK确认响应

### 8. 系统统计 📊
- **功能**: 查看系统运行状态和统计信息
- **包含信息**:
  - 系统初始化状态
  - 适配器加载状态
  - 支持的协议和版本
  - 消息路由统计
  - 当前时间

## 🎯 测试流程建议

### 基础测试流程
1. **系统初始化** - 确保系统正常启动
2. **消息识别测试** - 验证URIT消息识别
3. **消息解析测试** - 测试消息解析功能
4. **样本ID处理** - 验证样本ID处理规则
5. **YQ自定义测试** - 确认YQ配置正确
6. **完整流程测试** - 端到端验证

### 深度测试流程
1. 使用不同的URIT消息格式测试
2. 测试各种样本ID格式
3. 尝试不同的YQ配置模式
4. 验证数据库SQL语句正确性
5. 检查系统统计信息

## 🔧 自定义测试

### 测试自定义HL7消息
在"消息解析测试"中，您可以：
1. 清空文本框中的默认消息
2. 输入您自己的URIT HL7消息
3. 点击"解析消息"查看结果

### 测试不同样本ID
在"样本ID处理测试"中：
1. 输入各种格式的样本ID
2. 观察处理结果
3. 验证是否符合预期规则

### 测试不同YQ配置
在"YQ自定义测试"中：
1. 选择不同的YQ模式
2. 输入不同的自定义值
3. 查看最终的YQ处理结果

## 🚨 故障排除

### 系统初始化失败
- **原因**: 配置文件缺失或格式错误
- **解决**: 检查 `config_multi_device.ini` 文件是否存在

### 消息解析失败
- **原因**: HL7消息格式不正确
- **解决**: 确保消息符合HL7 v2.3.1标准格式

### 页面无法访问
- **原因**: 端口被占用或防火墙阻止
- **解决**: 
  - 检查5000端口是否被占用
  - 临时关闭防火墙测试
  - 尝试使用其他端口

### Flask依赖问题
- **错误**: 找不到Flask模块
- **解决**: 安装Flask
  ```bash
  pip install flask
  ```

## 📋 测试检查清单

- [ ] 系统成功初始化
- [ ] URIT消息正确识别
- [ ] 非URIT消息正确忽略
- [ ] HL7消息成功解析
- [ ] 样本ID正确处理（201801010001 → 1）
- [ ] YQ自定义配置生效（URIT8030）
- [ ] 数据库SQL语句正确生成
- [ ] ACK响应消息正确生成
- [ ] 完整流程测试通过
- [ ] 系统统计信息正常显示

## 🎉 成功标志

当所有测试项目都显示绿色"✅"状态时，说明：
1. URIT设备集成完全正常
2. 所有功能模块工作正常
3. 系统已准备好接收实际设备数据
4. 可以部署到生产环境

## 📞 技术支持

如果在测试过程中遇到问题：
1. 查看浏览器控制台错误信息
2. 检查命令行的错误日志
3. 参考本文档的故障排除部分
4. 确保所有依赖组件正确安装

---

**祝您测试顺利！🎯**