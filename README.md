# 瑞美LIS解码系统

## 项目简介

这是一个基于Python开发的医疗检验信息系统(LIS)，用于处理医疗检验设备和医院信息系统之间的HL7格式数据交互。系统完全符合迎凯生物LIS协议V1.12规范。

## 主要功能

### 🔬 HL7消息处理
- **ORU^R01**: 检验结果上传，支持多种检验项目
- **QRY^Q02**: 样本项目查询，支持BC/SN两种模式
- **ACK应答**: 自动生成标准HL7应答消息

### 🗄️ 数据库管理
- 连接SQL Server数据库
- 智能数据验证和字段长度控制
- 支持数据库配置加密存储
- 事务性数据操作保证数据一致性

### 📊 系统监控
- 实时性能统计（消息处理数、数据库操作数、错误数）
- 内存使用监控和活跃线程数统计
- 轮转日志系统，防止日志文件过大
- 每分钟自动性能报告

### 🖥️ 用户界面
- 系统托盘运行，后台服务模式
- 图形化数据库配置界面
- 自动管理员权限运行
- Windows控制台优化（禁用快速编辑模式）

## 目录结构

```
瑞美LIS解码/
├── lis_system.py          # 主程序文件
├── config.ini             # 配置文件
├── README.md              # 项目说明
├── cs/                    # 测试脚本目录
│   └── test_lis_system.py # 完整功能测试脚本
├── raw/                   # 原始HL7消息存储目录
├── result/                # 解析结果输出目录
├── log/                   # 系统日志目录
└── docs/                  # 文档目录
    ├── 优化完成报告.md
    ├── 迎凯生物协议修复报告.md
    ├── QRD段格式差异修复报告.md
    └── 实际消息测试验证.md
```

## 快速开始

### 1. 环境要求
- Python 3.7+
- Windows 10/11
- SQL Server数据库
- 必需的Python包：
  ```bash
  pip install pyodbc pillow pystray psutil
  ```

### 2. 配置数据库
1. 运行程序后点击托盘图标选择"设置"
2. 配置SQL Server连接信息
3. 系统支持加密存储用户名和密码

### 3. 启动系统
```bash
python lis_system.py
```

系统将以托盘模式运行，监听22010端口。

## 测试脚本使用

### 完整功能测试

位置：`cs/test_lis_system.py`

```bash
cd cs
python test_lis_system.py
```

### 测试内容

1. **连接测试**: 验证与LIS服务器的通信
2. **ORU^R01测试**: 模拟检验结果上传
   - CEA检验结果: 7.83 ng/mL (H-异常)
   - AFP检验结果: 2.91 ng/mL (N-正常)
   - 完整结果报告: 多项目同时上传
3. **QRY^Q02测试**: 模拟样本查询
   - 查询已存在样本: 285025671
   - 查询不存在样本: 0860194241, 999999999
4. **混合场景测试**: 上传和查询交替进行

### 测试输出示例

```
============================================================
测试: ORU^R01消息1 - 样本285025671结果上传
============================================================
发送消息 (234 字节):
格式化内容:
<SB>MSH|^~\&|Analyzer id|I2901|LIS ID||20250610164424||ORU^R01|3|P|2.3.1|Z80005000310|||0||UNICODE|||<CR>
PID|357|||||||O|||||||||||||||||||||||0^Y|<CR>
OBR|1319|285025671|13|I2900|N||20250610164424||1|029^1||N||20250610162315|0|||||||20250610164424||||||||||||||||||||||||||<CR>
OBX|NM|1|CEA^1|7.83|ng/mL|0.00-5.00|H||F||||20250610164420|||33645|20250502|3087|20250402|2799|20250402|2084|<CR><EB><CR>

收到应答 (89 字节):
<SB>MSH|^~\&|Analyzer id|I2900|LIS ID||20250611012345||ACK^R01|3|P|2.3.1||||0||UNICODE|||<CR>
MSA|AA|3||||0|<CR><EB><CR>
```

## 协议规范

### QRY^Q02查询格式
```
QRD|时间戳|R|I|||10^RD|样本号|||BC|架位|稀释标志|
```

**字段说明**：
- 字段7: 样本编号/条码号
- 字段10: 查询模式 (BC/SN)
- 字段11: 样本架位 (如: 4^1, N003^4)
- 字段12: 稀释标志 (Y/N)

### ORU^R01结果格式
```
OBR|序号|样本号|申请号|仪器代码|优先级||检测时间||重测次数|架位||稀释||送检时间|样本类型|||||||报告时间|
OBX|数据类型|序号|项目代码|检验结果|单位|参考范围|异常标志||结果状态||||观察时间|||试剂批号|试剂效期|...
```

## 数据库表结构

### lis_pat (患者/样本表)
主要字段：wyh, jyrq, yq, ybh, brdh, brxm, brxb, ch, ksdh等

### lis_result (检验结果表)  
主要字段：wyh, jyrq, yq, ybh, xmdh, csjg, refs, result_flag等

### xm_inter (项目对照表)
用于项目代码转换：yq, xmdh, tdh, dualmode等

## 系统优化

- **线程池管理**: 最大20个工作线程，避免无限制线程创建
- **内存控制**: 缓冲区大小限制1MB，防止内存泄漏
- **连接优化**: TCP Keep-Alive, TCP_NODELAY优化
- **日志轮转**: 最大10MB，保留5个备份文件
- **性能监控**: 实时统计系统运行状态

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查SQL Server服务是否启动
   - 验证连接字符串配置
   - 确认用户权限

2. **QRY^Q02查询无结果**
   - 检查样本号是否存在
   - 验证查询日期是否正确
   - 确认仪器代码匹配

3. **ORU^R01数据截断**
   - 系统已实现智能字段长度控制
   - 自动处理HL7特殊字符
   - 查看日志获取详细错误信息

### 日志位置
- 系统日志: `lis_system.log`
- QRY^Q02交互日志: `log/YYMMDD.txt`
- ORU^R01原始消息: `raw/YYMMDD.txt`
- 解析结果: `result/decoded_YYMMDD.txt`

## 贡献指南

1. 测试脚本位于 `cs/` 目录
2. 遵循项目编码规范
3. 提交前运行完整测试
4. 更新相关文档

## 版本历史

- **v1.0**: 基础HL7消息处理
- **v2.0**: 数据库优化和字段验证
- **v3.0**: 性能监控和线程池优化
- **v4.0**: 协议规范修复和实际格式适配

## 许可证

本项目为专用医疗软件，请遵循相关法规使用。 