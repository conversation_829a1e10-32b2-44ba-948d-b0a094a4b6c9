# 实际QRD段格式分析

## 实际接收到的QRD段

```
QRD|20250610164726|R|I|||10^RD|285025671|||BC|4^1|N|
```

## 字段索引分析

| 索引 | 字段值 | 说明 |
|------|--------|------|
| 0 | QRD | 段标识 |
| 1 | 20250610164726 | 消息时间 |
| 2 | R | ? |
| 3 | I | ? |
| 4 | (空) | |
| 5 | (空) | |
| 6 | 10^RD | ? |
| 7 | 285025671 | **样本号** ✅ |
| 8 | (空) | |
| 9 | (空) | |
| 10 | BC | **查询模式** ✅ |
| 11 | 4^1 | **架位** ✅ |
| 12 | N | **稀释** ✅ |

## 问题发现

**当前错误的解析：**
```python
query_mode = qrd_fields[2]   # 取到 'R' ❌
sample_no = qrd_fields[8]    # 取到 空字符串 ❌
rack_pos = qrd_fields[10]    # 取到 'BC' ❌
dilute = qrd_fields[11]      # 取到 '4^1' ❌
```

**正确的解析应该是：**
```python
query_mode = qrd_fields[10]  # 取到 'BC' ✅
sample_no = qrd_fields[7]    # 取到 '285025671' ✅
rack_pos = qrd_fields[11]    # 取到 '4^1' ✅
dilute = qrd_fields[12]      # 取到 'N' ✅
```

## 对比不同格式

### 协议文档示例格式
```
QRD|20210109114124|BC|D|1|||RD|12345||4^1|N|T|
```

### 实际接收到的格式
```
QRD|20250610164726|R|I|||10^RD|285025671|||BC|4^1|N|
```

**关键差异：**
1. 查询模式位置：协议示例在字段2，实际在字段10
2. 样本号位置：协议示例在字段8，实际在字段7
3. 架位位置：协议示例在字段10，实际在字段11
4. 稀释位置：协议示例在字段11，实际在字段12

## 结论

实际的QRD段格式与协议文档示例不同，需要根据实际格式调整解析逻辑。

同时需要在查询中加入yq条件以精准定位。 