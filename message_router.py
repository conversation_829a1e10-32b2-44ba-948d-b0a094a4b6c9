#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
消息路由器 - 负责识别设备和路由消息到对应的协议适配器

该模块实现了智能消息路由功能，能够：
1. 自动识别设备类型和协议
2. 动态加载和管理协议适配器
3. 将消息路由到合适的适配器进行处理
4. 提供适配器注册和管理功能
"""

import importlib
import logging
import re
from typing import Dict, List, Any, Optional, Type
from device_adapter import (
    ProtocolAdapter, StandardMessage, DeviceInfo,
    UnsupportedProtocolError, ProtocolParseError
)

# =============================================================================
# 设备识别器
# =============================================================================

class DeviceIdentifier:
    """设备识别器 - 根据连接信息和消息内容识别设备类型"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化设备识别器
        
        Args:
            config: 配置字典，包含设备识别规则
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.device_rules = self._load_device_rules()
    
    def _load_device_rules(self) -> List[Dict[str, Any]]:
        """加载设备识别规则"""
        rules = []
        
        # 从配置中加载设备规则
        devices_config = self.config.get('devices', {})
        
        for device_id, device_config in devices_config.items():
            rule = {
                'device_id': device_id,
                'device_type': device_config.get('device_type', 'Unknown'),
                'manufacturer': device_config.get('manufacturer', 'Unknown'),
                'model': device_config.get('model', 'Unknown'),
                'protocol': device_config.get('protocol', 'Unknown'),
                'ip_ranges': self._parse_ip_ranges(device_config.get('ip_range', '')),
                'ports': self._parse_ports(device_config.get('ports', '')),
                'message_patterns': device_config.get('message_patterns', []),
                'adapter': device_config.get('adapter', ''),
                'priority': device_config.get('priority', 100),  # 优先级，越小越高
                # YQ配置
                'yq_source': device_config.get('yq_source', 'message'),
                'yq_value': device_config.get('yq_value', ''),
                'yq_prefix': device_config.get('yq_prefix', ''),
                'yq_mapping': device_config.get('yq_mapping', {})
            }
            rules.append(rule)
        
        # 按优先级排序
        rules.sort(key=lambda x: x['priority'])
        
        return rules
    
    def _parse_ip_ranges(self, ip_range: str) -> List[str]:
        """解析IP地址范围"""
        if not ip_range:
            return []
        
        ranges = []
        for part in ip_range.split(','):
            part = part.strip()
            if '-' in part:
                # 范围格式: *************-*************
                ranges.append(part)
            else:
                # 单个IP
                ranges.append(part)
        
        return ranges
    
    def _parse_ports(self, ports) -> List[int]:
        """解析端口列表"""
        if not ports:
            return []
        
        # 处理单个端口（整数）或端口列表（字符串）
        if isinstance(ports, int):
            return [ports]
        
        if isinstance(ports, str):
            port_list = []
            for port in ports.split(','):
                try:
                    port_list.append(int(port.strip()))
                except ValueError:
                    continue
            return port_list
        
        return []
    
    def _is_ip_in_range(self, ip: str, ip_range: str) -> bool:
        """检查IP是否在指定范围内"""
        if not ip or not ip_range:
            return False
        
        try:
            if '-' in ip_range:
                # 范围格式
                start_ip, end_ip = ip_range.split('-')
                start_ip = start_ip.strip()
                end_ip = end_ip.strip()
                
                # 简单的IP范围检查（仅支持最后一段的范围）
                if '.' in start_ip and '.' in end_ip:
                    start_parts = start_ip.split('.')
                    end_parts = end_ip.split('.')
                    ip_parts = ip.split('.')
                    
                    if len(start_parts) == len(end_parts) == len(ip_parts) == 4:
                        # 检查前三段是否相同
                        if (start_parts[:3] == end_parts[:3] == ip_parts[:3]):
                            # 检查最后一段是否在范围内
                            start_last = int(start_parts[3])
                            end_last = int(end_parts[3])
                            ip_last = int(ip_parts[3])
                            return start_last <= ip_last <= end_last
            else:
                # 精确匹配
                return ip == ip_range.strip()
        except (ValueError, IndexError):
            pass
        
        return False
    
    def _check_message_patterns(self, message: bytes, patterns: List[str]) -> bool:
        """检查消息是否匹配指定模式"""
        if not patterns:
            return False
        
        try:
            text = message.decode('utf-8', errors='ignore')
            
            for pattern in patterns:
                if isinstance(pattern, str):
                    # 简单字符串匹配
                    if pattern in text:
                        return True
                    
                    # 正则表达式匹配
                    try:
                        if re.search(pattern, text, re.IGNORECASE):
                            return True
                    except re.error:
                        continue
        except Exception:
            pass
        
        return False
    
    def identify_device(self, connection_info: Dict[str, Any], message: bytes) -> DeviceInfo:
        """识别设备类型
        
        Args:
            connection_info: 连接信息 {'address': ('ip', port), 'socket': socket}
            message: 原始消息内容
            
        Returns:
            设备信息对象
        """
        client_ip = ""
        client_port = 0
        
        # 提取连接信息
        if 'address' in connection_info:
            address = connection_info['address']
            if isinstance(address, (list, tuple)) and len(address) >= 2:
                client_ip = str(address[0])
                client_port = int(address[1]) if len(address) > 1 else 0
        
        # 按优先级检查设备规则
        for rule in self.device_rules:
            match_score = 0
            
            # 检查IP范围
            if rule['ip_ranges']:
                ip_match = any(self._is_ip_in_range(client_ip, ip_range) 
                              for ip_range in rule['ip_ranges'])
                if ip_match:
                    match_score += 10
            
            # 检查端口
            if rule['ports']:
                port_match = client_port in rule['ports']
                if port_match:
                    match_score += 5
            
            # 检查消息模式
            if rule['message_patterns']:
                pattern_match = self._check_message_patterns(message, rule['message_patterns'])
                if pattern_match:
                    match_score += 20
            
            # 如果有足够的匹配度，返回设备信息
            if match_score >= 10:  # 最低匹配阈值
                self.logger.info(f"识别到设备: {rule['device_id']} (匹配度: {match_score})")
                
                # 构建YQ配置
                yq_config = {}
                if 'yq_source' in rule:
                    yq_config['yq_source'] = rule['yq_source']
                    yq_config['yq_value'] = rule.get('yq_value', '')
                    yq_config['yq_prefix'] = rule.get('yq_prefix', '')
                    yq_config['yq_mapping'] = rule.get('yq_mapping', {})
                
                return DeviceInfo(
                    device_id=rule['device_id'],
                    device_type=rule['device_type'],
                    manufacturer=rule['manufacturer'],
                    model=rule['model'],
                    protocol=rule['protocol'],
                    version="",  # 由适配器确定
                    ip_address=client_ip,
                    port=client_port,
                    yq_config=yq_config,
                    metadata={'adapter': rule['adapter'], 'match_score': match_score}
                )
        
        # 如果没有匹配的规则，返回默认设备信息
        self.logger.warning(f"未能识别设备 {client_ip}:{client_port}，使用默认配置")
        
        return DeviceInfo(
            device_id=f"unknown_{client_ip}_{client_port}",
            device_type="Unknown",
            manufacturer="Unknown",
            model="Unknown",
            protocol="Unknown",
            version="",
            ip_address=client_ip,
            port=client_port,
            metadata={'adapter': 'auto', 'match_score': 0}
        )

# =============================================================================
# 消息路由器
# =============================================================================

class MessageRouter:
    """消息路由器 - 负责将消息路由到合适的协议适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化消息路由器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.device_identifier = DeviceIdentifier(config)
        self.adapters: Dict[str, ProtocolAdapter] = {}
        self.adapter_classes: Dict[str, Type[ProtocolAdapter]] = {}
        
        # 加载适配器
        self._load_adapters()
        
        # 统计信息
        self.stats = {
            'messages_routed': 0,
            'messages_failed': 0,
            'adapter_usage': {}
        }
    
    def _load_adapters(self):
        """动态加载协议适配器"""
        adapters_config = self.config.get('adapters', {})
        
        for adapter_name, adapter_config in adapters_config.items():
            try:
                self._load_single_adapter(adapter_name, adapter_config)
            except Exception as e:
                self.logger.error(f"加载适配器失败 {adapter_name}: {e}")
    
    def _load_single_adapter(self, adapter_name: str, adapter_config: Dict[str, Any]):
        """加载单个适配器"""
        try:
            # 获取模块和类名
            module_name = adapter_config.get('module')
            class_name = adapter_config.get('class')
            
            if not module_name or not class_name:
                self.logger.error(f"适配器 {adapter_name} 配置不完整")
                return
            
            # 动态导入模块
            module = importlib.import_module(module_name)
            adapter_class = getattr(module, class_name)
            
            # 验证是否为ProtocolAdapter子类
            if not issubclass(adapter_class, ProtocolAdapter):
                self.logger.error(f"适配器 {adapter_name} 不是ProtocolAdapter的子类")
                return
            
            # 创建适配器实例
            adapter_instance = adapter_class(adapter_config)
            
            # 注册适配器
            self.adapters[adapter_name] = adapter_instance
            self.adapter_classes[adapter_name] = adapter_class
            self.stats['adapter_usage'][adapter_name] = 0
            
            self.logger.info(f"已加载适配器: {adapter_name} ({adapter_class.__name__})")
            
        except Exception as e:
            self.logger.error(f"加载适配器失败 {adapter_name}: {e}")
            # 不要抛出异常，继续加载其他适配器
    
    def register_adapter(self, adapter_name: str, adapter: ProtocolAdapter):
        """手动注册适配器
        
        Args:
            adapter_name: 适配器名称
            adapter: 适配器实例
        """
        if not isinstance(adapter, ProtocolAdapter):
            raise ValueError(f"adapter 必须是 ProtocolAdapter 的实例")
        
        self.adapters[adapter_name] = adapter
        self.adapter_classes[adapter_name] = adapter.__class__
        self.stats['adapter_usage'][adapter_name] = 0
        
        self.logger.info(f"手动注册适配器: {adapter_name}")
    
    def unregister_adapter(self, adapter_name: str):
        """注销适配器
        
        Args:
            adapter_name: 适配器名称
        """
        if adapter_name in self.adapters:
            del self.adapters[adapter_name]
            del self.adapter_classes[adapter_name]
            self.stats['adapter_usage'].pop(adapter_name, None)
            self.logger.info(f"已注销适配器: {adapter_name}")
    
    def _select_adapter(self, device_info: DeviceInfo, message: bytes) -> ProtocolAdapter:
        """选择合适的协议适配器
        
        Args:
            device_info: 设备信息
            message: 原始消息
            
        Returns:
            选中的协议适配器
            
        Raises:
            UnsupportedProtocolError: 找不到合适的适配器
        """
        connection_info = {
            'address': (device_info.ip_address, device_info.port)
        }
        
        # 1. 尝试使用设备指定的适配器
        preferred_adapter = device_info.metadata.get('adapter')
        if preferred_adapter and preferred_adapter in self.adapters:
            adapter = self.adapters[preferred_adapter]
            if adapter.can_handle(message, connection_info):
                return adapter
        
        # 2. 遍历所有适配器，寻找能处理该消息的适配器
        for adapter_name, adapter in self.adapters.items():
            try:
                if adapter.can_handle(message, connection_info):
                    self.logger.info(f"选择适配器: {adapter_name}")
                    return adapter
            except Exception as e:
                self.logger.warning(f"适配器 {adapter_name} 检查失败: {e}")
                continue
        
        # 3. 没有找到合适的适配器
        raise UnsupportedProtocolError(
            f"找不到能处理该协议的适配器 - 设备: {device_info.device_type}, "
            f"IP: {device_info.ip_address}, 消息长度: {len(message)}"
        )
    
    def route_message(self, message: bytes, connection_info: Dict[str, Any]) -> StandardMessage:
        """路由消息到合适的适配器进行处理
        
        Args:
            message: 原始消息字节
            connection_info: 连接信息
            
        Returns:
            处理后的标准化消息
            
        Raises:
            UnsupportedProtocolError: 不支持的协议
            ProtocolParseError: 解析失败
        """
        try:
            # 1. 识别设备
            device_info = self.device_identifier.identify_device(connection_info, message)
            
            # 2. 选择适配器
            adapter = self._select_adapter(device_info, message)
            
            # 3. 解析消息
            standard_message = adapter.parse_message(message, connection_info)
            
            # 4. 设置设备信息
            standard_message.device_info = device_info
            
            # 5. 验证消息
            validation_errors = adapter.validate_message(standard_message)
            if validation_errors:
                self.logger.warning(f"消息验证警告: {validation_errors}")
                standard_message.metadata['validation_errors'] = validation_errors
            
            # 6. 更新统计信息
            self.stats['messages_routed'] += 1
            adapter_name = self._get_adapter_name(adapter)
            if adapter_name:
                if adapter_name not in self.stats['adapter_usage']:
                    self.stats['adapter_usage'][adapter_name] = 0
                self.stats['adapter_usage'][adapter_name] += 1
            
            self.logger.info(
                f"消息路由成功 - 设备: {device_info.device_type}, "
                f"适配器: {adapter.protocol_name}, 类型: {standard_message.message_type}"
            )
            
            return standard_message
            
        except Exception as e:
            self.stats['messages_failed'] += 1
            self.logger.error(f"消息路由失败: {e}")
            raise
    
    def _get_adapter_name(self, adapter: ProtocolAdapter) -> Optional[str]:
        """获取适配器名称"""
        for name, instance in self.adapters.items():
            if instance is adapter:
                return name
        return None
    
    def generate_response(self, request: StandardMessage) -> bytes:
        """生成响应消息
        
        Args:
            request: 请求的标准化消息
            
        Returns:
            响应消息字节
        """
        try:
            # 查找处理该请求的适配器
            device_info = request.device_info
            preferred_adapter = device_info.metadata.get('adapter')
            
            if preferred_adapter and preferred_adapter in self.adapters:
                adapter = self.adapters[preferred_adapter]
                return adapter.generate_response(request)
            
            # 默认返回NAK
            return b"NAK\r"
            
        except Exception as e:
            self.logger.error(f"生成响应失败: {e}")
            return b"NAK\r"
    
    def get_available_adapters(self) -> Dict[str, Dict[str, Any]]:
        """获取可用适配器列表
        
        Returns:
            适配器信息字典
        """
        adapters_info = {}
        
        for name, adapter in self.adapters.items():
            adapters_info[name] = adapter.get_adapter_info()
        
        return adapters_info
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取路由统计信息
        
        Returns:
            统计信息字典
        """
        return {
            'messages_routed': self.stats['messages_routed'],
            'messages_failed': self.stats['messages_failed'],
            'adapter_usage': self.stats['adapter_usage'].copy(),
            'success_rate': (
                self.stats['messages_routed'] / 
                max(1, self.stats['messages_routed'] + self.stats['messages_failed'])
            ) * 100,
            'total_adapters': len(self.adapters)
        }
    
    def reset_statistics(self):
        """重置统计信息"""
        self.stats = {
            'messages_routed': 0,
            'messages_failed': 0,
            'adapter_usage': {name: 0 for name in self.adapters.keys()}
        }
        
        self.logger.info("路由统计信息已重置")