#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
运行真实数据测试的完整脚本

1. 启动URIT LIS服务器（后台）
2. 等待服务器就绪
3. 发送真实测试数据
4. 验证数据库写入结果
5. 停止服务器
"""

import subprocess
import threading
import time
import sys
import os
from datetime import datetime

def start_urit_server():
    """在后台启动URIT服务器"""
    try:
        print("正在启动URIT LIS服务器（后台运行）...")
        
        # 启动服务器进程
        process = subprocess.Popen(
            [sys.executable, "urit_lis_server.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding='utf-8',
            errors='ignore'
        )
        
        # 等待服务器启动
        time.sleep(3)
        
        if process.poll() is None:
            print("OK URIT LIS服务器启动成功")
            return process
        else:
            print("X URIT LIS服务器启动失败")
            stdout, stderr = process.communicate()
            print(f"错误输出: {stderr}")
            return None
            
    except Exception as e:
        print(f"启动URIT服务器时出错: {e}")
        return None

def test_server_connection():
    """测试服务器连接"""
    import socket
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('127.0.0.1', 22010))
        sock.close()
        return result == 0
    except:
        return False

def run_data_sender():
    """运行数据发送测试"""
    try:
        print("\n开始发送真实测试数据...")
        
        # 运行数据发送器
        result = subprocess.run(
            [sys.executable, "real_data_sender.py"],
            input="y\n",  # 自动确认
            text=True,
            capture_output=True,
            encoding='utf-8',
            errors='ignore'
        )
        
        print("数据发送完成")
        print("输出摘要:")
        output_lines = result.stdout.split('\n')
        for line in output_lines:
            if any(keyword in line for keyword in ['发送样本', '成功', '失败', '统计', '总数', '成功率']):
                print(f"  {line}")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"运行数据发送器时出错: {e}")
        return False

def verify_database_data():
    """验证数据库中的数据"""
    try:
        print("\n验证数据库数据...")
        
        from urit_lis_server import URITLISServer
        server = URITLISServer()
        
        with server.get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 查询今天的URIT数据
            today = datetime.now().strftime('%Y-%m-%d')
            
            # 查询患者数据
            cursor.execute(f"""
                SELECT COUNT(*) FROM {server.pat_table} 
                WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
            """, today)
            pat_count = cursor.fetchone()[0]
            
            # 查询结果数据
            cursor.execute(f"""
                SELECT COUNT(*) FROM {server.result_table} 
                WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
            """, today)
            result_count = cursor.fetchone()[0]
            
            # 查询具体数据
            cursor.execute(f"""
                SELECT TOP 5 wyh, ybh, jyrq FROM {server.pat_table} 
                WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
                ORDER BY jyrq DESC
            """, today)
            pat_samples = cursor.fetchall()
            
            cursor.execute(f"""
                SELECT TOP 10 ybh, xmdh, csjg, result_flag FROM {server.result_table} 
                WHERE yq = 'URIT8030' AND CONVERT(date, jyrq) = ?
                ORDER BY jyrq DESC
            """, today)
            test_results = cursor.fetchall()
            
            print(f"数据库验证结果:")
            print(f"  患者记录: {pat_count} 条")
            print(f"  检验结果: {result_count} 条")
            
            if pat_count > 0:
                print(f"  患者样本示例:")
                for row in pat_samples:
                    print(f"    样本ID: {row[1]}, 消息ID: {row[0]}")
            
            if result_count > 0:
                print(f"  检验结果示例:")
                for row in test_results:
                    print(f"    样本{row[0]}: {row[1]}={row[2]} ({row[3]})")
            
            return pat_count > 0 and result_count > 0
            
    except Exception as e:
        print(f"验证数据库数据时出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("URIT设备真实数据测试")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    server_process = None
    success = False
    
    try:
        # 1. 启动URIT服务器
        server_process = start_urit_server()
        if not server_process:
            print("无法启动URIT服务器，测试终止")
            return False
        
        # 2. 等待服务器就绪
        print("等待服务器就绪...")
        for i in range(10):
            if test_server_connection():
                print("OK 服务器连接正常")
                break
            time.sleep(1)
            print(f"  等待中... ({i+1}/10)")
        else:
            print("X 服务器连接超时")
            return False
        
        # 3. 发送测试数据
        if run_data_sender():
            print("OK 数据发送成功")
            
            # 4. 等待数据处理完成
            time.sleep(2)
            
            # 5. 验证数据库数据
            if verify_database_data():
                print("OK 数据库验证成功")
                success = True
            else:
                print("X 数据库验证失败")
        else:
            print("X 数据发送失败")
    
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
    finally:
        # 停止服务器
        if server_process:
            print("\n正在停止URIT服务器...")
            try:
                server_process.terminate()
                server_process.wait(timeout=5)
                print("OK URIT服务器已停止")
            except:
                server_process.kill()
                print("URIT服务器已强制停止")
    
    # 显示测试结果
    print("\n" + "=" * 70)
    if success:
        print("真实数据测试成功！")
        print()
        print("验证结果:")
        print("- URIT设备消息成功发送并接收")
        print("- 消息正确解析和处理") 
        print("- 数据成功写入数据库")
        print("- YQ字段使用自定义值 'URIT8030'")
        print("- 样本ID正确处理（去除前导0）")
        print()
        print("可以查看数据库验证实际数据:")
        print("SELECT * FROM lis_pat WHERE yq = 'URIT8030' ORDER BY jyrq DESC")
        print("SELECT * FROM lis_result WHERE yq = 'URIT8030' ORDER BY jyrq DESC")
    else:
        print("真实数据测试失败，请检查错误信息")
    
    print("=" * 70)
    
    return success

if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"测试脚本执行失败: {e}")
        sys.exit(1)