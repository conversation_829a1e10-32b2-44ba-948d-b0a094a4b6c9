# 多设备支持实现计划

## 实施路线图

### 阶段1: 基础架构重构 (2-3周)

#### 1.1 创建核心接口和基类
- [ ] 实现 `ProtocolAdapter` 抽象基类
- [ ] 实现 `StandardMessage` 数据结构
- [ ] 实现 `DeviceIdentifier` 设备识别器
- [ ] 实现 `MessageRouter` 消息路由器

#### 1.2 重构现有代码
- [ ] 将现有HL7解析逻辑提取为 `YingKaiHL7Adapter`
- [ ] 修改 `LISSystem._handle_client` 使用新路由机制
- [ ] 保持向后兼容性

#### 1.3 配置系统扩展
- [ ] 扩展 `config.ini` 支持多设备配置
- [ ] 实现设备配置管理类
- [ ] 添加字段映射配置支持

### 阶段2: 设备适配器开发 (3-4周)

#### 2.1 常见设备适配器
- [ ] Abbott适配器 (ASTM协议)
- [ ] Roche适配器 (自定义协议)
- [ ] Beckman适配器 (LIS2-A2协议)
- [ ] Sysmex适配器 (HL7变种)

#### 2.2 协议支持
- [ ] ASTM E1394协议解析器
- [ ] LIS2-A2协议解析器
- [ ] 自定义协议模板引擎
- [ ] 协议验证和错误处理

### 阶段3: 功能增强 (2-3周)

#### 3.1 监控和管理
- [ ] 设备状态监控
- [ ] 消息审计和日志
- [ ] 性能统计和报告
- [ ] 设备配置热更新

#### 3.2 用户界面
- [ ] 设备管理界面
- [ ] 协议配置界面
- [ ] 消息监控界面
- [ ] 错误诊断工具

## 详细实现示例

### 1. 核心接口定义

```python
# device_adapter.py
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

@dataclass
class DeviceInfo:
    """设备信息"""
    device_id: str
    device_type: str
    manufacturer: str
    model: str
    protocol: str
    version: str
    ip_address: str = ""
    port: int = 0

@dataclass
class StandardPatient:
    """标准化患者信息"""
    patient_id: str
    name: str = ""
    sex: str = ""
    age: str = ""
    birth_date: str = ""
    department: str = ""
    bed_no: str = ""

@dataclass
class StandardTestResult:
    """标准化检验结果"""
    test_code: str
    test_name: str
    value: str
    unit: str = ""
    reference_range: str = ""
    abnormal_flag: str = ""
    result_status: str = ""
    observation_time: str = ""

@dataclass
class StandardOrder:
    """标准化医嘱"""
    order_id: str
    sample_id: str
    test_type: str
    order_time: str
    priority: str = ""
    results: List[StandardTestResult] = None

@dataclass
class StandardMessage:
    """标准化消息"""
    message_type: str  # ORU, QRY, ACK
    message_id: str
    timestamp: datetime
    device_info: DeviceInfo
    patient: StandardPatient
    orders: List[StandardOrder]
    raw_message: bytes
    metadata: Dict[str, Any] = None

class ProtocolAdapter(ABC):
    """协议适配器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @property
    @abstractmethod
    def protocol_name(self) -> str:
        """协议名称"""
        pass
    
    @property
    @abstractmethod
    def supported_versions(self) -> List[str]:
        """支持的协议版本"""
        pass
    
    @abstractmethod
    def can_handle(self, message: bytes, connection_info: Dict[str, Any]) -> bool:
        """判断是否能处理该消息"""
        pass
    
    @abstractmethod
    def parse_message(self, message: bytes, connection_info: Dict[str, Any]) -> StandardMessage:
        """解析消息为标准格式"""
        pass
    
    @abstractmethod
    def generate_response(self, request: StandardMessage) -> bytes:
        """生成响应消息"""
        pass
    
    def validate_message(self, message: StandardMessage) -> List[str]:
        """验证消息，返回错误列表"""
        errors = []
        
        if not message.message_type:
            errors.append("消息类型不能为空")
        
        if not message.patient.patient_id:
            errors.append("患者ID不能为空")
        
        if not message.orders:
            errors.append("医嘱列表不能为空")
        
        return errors
```

### 2. YingKai适配器重构

```python
# adapters/yingkai_adapter.py
from device_adapter import ProtocolAdapter, StandardMessage, DeviceInfo
import re
from datetime import datetime

class YingKaiHL7Adapter(ProtocolAdapter):
    """YingKai Bio LIS Protocol V1.12 适配器"""
    
    @property
    def protocol_name(self) -> str:
        return "YingKai_HL7"
    
    @property
    def supported_versions(self) -> List[str]:
        return ["1.12", "1.11", "1.10"]
    
    def can_handle(self, message: bytes, connection_info: Dict[str, Any]) -> bool:
        """检查是否为YingKai HL7消息"""
        try:
            text = message.decode('utf-8', errors='ignore')
            # 检查HL7消息特征
            return ('MSH|' in text or 'OBR|' in text or 'QRD|' in text)
        except:
            return False
    
    def parse_message(self, message: bytes, connection_info: Dict[str, Any]) -> StandardMessage:
        """解析YingKai HL7消息"""
        try:
            text = message.decode('utf-8', errors='replace')
            
            # 使用现有的解析逻辑
            device_info = DeviceInfo(
                device_id=f"yingkai_{connection_info.get('address', ['unknown'])[0]}",
                device_type="YingKai",
                manufacturer="YingKai Bio",
                model="LIS",
                protocol="HL7",
                version="2.5",
                ip_address=connection_info.get('address', [''])[0],
                port=connection_info.get('port', 0)
            )
            
            # 解析消息类型
            message_type = self._extract_message_type(text)
            
            if message_type == "QRY":
                return self._parse_qry_message(text, device_info, message)
            elif message_type == "ORU":
                return self._parse_oru_message(text, device_info, message)
            else:
                raise ValueError(f"不支持的消息类型: {message_type}")
                
        except Exception as e:
            self.logger.error(f"解析YingKai消息失败: {e}")
            raise
    
    def _extract_message_type(self, text: str) -> str:
        """提取消息类型"""
        # QRY^Q02 查询
        if 'QRD|' in text:
            return "QRY"
        # ORU^R01 结果
        elif 'MSH|' in text and 'ORU^R01' in text:
            return "ORU"
        else:
            return "UNKNOWN"
    
    def _parse_qry_message(self, text: str, device_info: DeviceInfo, raw_message: bytes) -> StandardMessage:
        """解析QRY查询消息"""
        # 现有的QRY解析逻辑
        # ...
        pass
    
    def _parse_oru_message(self, text: str, device_info: DeviceInfo, raw_message: bytes) -> StandardMessage:
        """解析ORU结果消息"""
        # 现有的ORU解析逻辑
        # ...
        pass
    
    def generate_response(self, request: StandardMessage) -> bytes:
        """生成HL7响应消息"""
        if request.message_type == "QRY":
            return self._generate_qry_response(request)
        elif request.message_type == "ORU":
            return self._generate_ack_response(request)
        else:
            return b"NAK\r"
```

### 3. 消息路由器实现

```python
# message_router.py
from typing import Dict, List
from device_adapter import ProtocolAdapter, StandardMessage
import importlib
import logging

class MessageRouter:
    """消息路由器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.adapters: List[ProtocolAdapter] = []
        self.device_configs = {}
        self.logger = logging.getLogger(__name__)
        self._load_adapters()
        self._load_device_configs()
    
    def _load_adapters(self):
        """动态加载适配器"""
        adapter_configs = self.config.get('adapters', {})
        
        for adapter_name, adapter_config in adapter_configs.items():
            try:
                # 动态导入适配器类
                module_name = adapter_config['module']
                class_name = adapter_config['class']
                
                module = importlib.import_module(module_name)
                adapter_class = getattr(module, class_name)
                
                # 创建适配器实例
                adapter = adapter_class(adapter_config)
                self.adapters.append(adapter)
                
                self.logger.info(f"已加载适配器: {adapter_name}")
                
            except Exception as e:
                self.logger.error(f"加载适配器失败 {adapter_name}: {e}")
    
    def _load_device_configs(self):
        """加载设备配置"""
        device_configs = self.config.get('devices', {})
        for device_id, config in device_configs.items():
            self.device_configs[device_id] = config
    
    def route_message(self, message: bytes, connection_info: Dict[str, Any]) -> StandardMessage:
        """路由消息到合适的适配器"""
        
        # 尝试每个适配器
        for adapter in self.adapters:
            if adapter.can_handle(message, connection_info):
                try:
                    standard_message = adapter.parse_message(message, connection_info)
                    
                    # 验证消息
                    errors = adapter.validate_message(standard_message)
                    if errors:
                        self.logger.warning(f"消息验证警告: {errors}")
                    
                    self.logger.info(f"消息已路由到: {adapter.protocol_name}")
                    return standard_message
                    
                except Exception as e:
                    self.logger.error(f"适配器 {adapter.protocol_name} 处理失败: {e}")
                    continue
        
        raise UnsupportedProtocolError("找不到合适的协议适配器")

class UnsupportedProtocolError(Exception):
    """不支持的协议错误"""
    pass
```

### 4. 配置文件扩展示例

```ini
[ADAPTERS]
yingkai = adapters.yingkai_adapter.YingKaiHL7Adapter
abbott = adapters.abbott_adapter.AbbottASTMAdapter
roche = adapters.roche_adapter.RocheCustomAdapter

[ADAPTER_yingkai]
module = adapters.yingkai_adapter
class = YingKaiHL7Adapter
protocol = HL7
version = 2.5
timeout = 30

[ADAPTER_abbott]
module = adapters.abbott_adapter
class = AbbottASTMAdapter
protocol = ASTM
version = E1394
timeout = 60

[DEVICES]
yingkai_001 = YingKai设备1
abbott_001 = Abbott设备1

[DEVICE_yingkai_001]
adapter = yingkai
ip_range = *************-*************
ports = 22010,22011,22012
table_mapping = lis_pat,lis_result

[DEVICE_abbott_001]
adapter = abbott
ip_range = ***********-************
ports = 23000
table_mapping = abbott_pat,abbott_result
```

### 5. 集成到现有系统

```python
# 修改现有的LISSystem类
class LISSystem:
    def __init__(self, config_file="config.ini"):
        # 现有初始化代码...
        
        # 添加消息路由器
        self.message_router = MessageRouter(self.config)
    
    def _handle_client(self, client_socket, address):
        """修改后的客户端处理方法"""
        # 现有连接处理代码...
        
        try:
            while True:
                # 接收消息的现有逻辑...
                
                if complete_message:
                    # 使用新的路由机制
                    connection_info = {
                        'address': address,
                        'port': self.port,
                        'socket': client_socket
                    }
                    
                    try:
                        # 路由消息
                        standard_message = self.message_router.route_message(
                            complete_message, connection_info
                        )
                        
                        # 处理标准化消息
                        response = self._process_standard_message(standard_message)
                        
                        # 发送响应
                        if response:
                            client_socket.send(response)
                            
                    except UnsupportedProtocolError as e:
                        self.logger.error(f"不支持的协议: {e}")
                        client_socket.send(b"NAK\r")
                    except Exception as e:
                        self.logger.error(f"消息处理失败: {e}")
                        client_socket.send(b"NAK\r")
                        
        except Exception as e:
            self.logger.error(f"客户端连接处理失败: {e}")
        finally:
            client_socket.close()
    
    def _process_standard_message(self, message: StandardMessage) -> bytes:
        """处理标准化消息"""
        if message.message_type == "ORU":
            # 处理结果上传
            success = self._process_result_message(message)
            return self._generate_ack_response(message, success)
            
        elif message.message_type == "QRY":
            # 处理查询请求
            result = self._process_query_message(message)
            return result
            
        else:
            self.logger.warning(f"未知消息类型: {message.message_type}")
            return b"NAK\r"
```

## 实施优先级

### 高优先级 (必须实现)
1. ProtocolAdapter基类和接口定义
2. YingKaiHL7Adapter重构
3. MessageRouter基础实现
4. 配置系统扩展

### 中优先级 (建议实现)
1. Abbott ASTM适配器
2. 设备自动识别
3. 消息验证增强
4. 错误处理优化

### 低优先级 (可选实现)
1. 其他厂商适配器
2. 图形化配置界面
3. 性能监控面板
4. 消息审计功能

这个实现计划提供了清晰的路线图和具体的代码示例，确保系统能够逐步支持多种设备协议，同时保持现有功能的稳定性。