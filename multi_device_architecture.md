# 多设备支持架构设计

## 架构概述

### 分层架构设计
```
┌─────────────────────────────────────────────────────────────┐
│                     设备层 (Device Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  设备A     │  设备B     │  设备C     │  设备D     │  ...      │
│ (YingKai)  │ (Abbott)   │ (Roche)    │ (Beck<PERSON>)  │          │
│ HL7-2.5    │ ASTM-1394  │ Custom     │ LIS2-A2    │          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    协议适配层 (Protocol Adapter Layer)        │
├─────────────────────────────────────────────────────────────┤
│  HL7Adapter │ ASTMAdapter │ CustomAdapter │ LIS2Adapter     │
│             │             │               │                 │
│ - 消息解析   │ - 消息解析   │ - 消息解析     │ - 消息解析      │
│ - 格式转换   │ - 格式转换   │ - 格式转换     │ - 格式转换      │
│ - 验证校验   │ - 验证校验   │ - 验证校验     │ - 验证校验      │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   消息路由层 (Message Router Layer)           │
├─────────────────────────────────────────────────────────────┤
│  DeviceRouter:                                              │
│  - 设备识别 (根据IP/端口/消息头识别设备类型)                   │
│  - 适配器选择 (选择对应的协议适配器)                          │
│  - 消息分发 (将消息分发给对应的处理器)                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  标准消息层 (Standard Message Layer)          │
├─────────────────────────────────────────────────────────────┤
│  统一的内部消息格式:                                         │
│  - StandardMessage (标准化的消息对象)                        │
│  - StandardResult (标准化的结果对象)                         │
│  - StandardPatient (标准化的患者对象)                        │
│  - StandardOrder (标准化的医嘱对象)                          │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   业务处理层 (Business Logic Layer)           │
├─────────────────────────────────────────────────────────────┤
│  - 数据验证和清洗                                           │
│  - 业务规则处理                                             │
│  - 数据转换和映射                                           │
│  - 异常处理和日志记录                                       │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Access Layer)              │
├─────────────────────────────────────────────────────────────┤
│  DatabaseAdapter:                                           │
│  - 动态表映射                                               │
│  - 字段映射配置                                             │
│  - 数据类型转换                                             │
│  - 事务管理                                                 │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据库层 (Database Layer)                  │
├─────────────────────────────────────────────────────────────┤
│  SQL Server / MySQL / PostgreSQL / Oracle                  │
│  - lis_pat (患者表)                                         │
│  - lis_result (结果表)                                      │
│  - xm_inter (项目映射表)                                    │
│  - device_config (设备配置表)                               │
└─────────────────────────────────────────────────────────────┘
```

## 核心组件设计

### 1. 设备识别器 (DeviceIdentifier)

```python
class DeviceIdentifier:
    """设备识别器 - 根据连接信息和消息内容识别设备类型"""
    
    def identify_device(self, connection_info: dict, message: bytes) -> DeviceInfo:
        """
        识别设备类型
        :param connection_info: 连接信息 (IP, Port, etc.)
        :param message: 原始消息内容
        :return: 设备信息对象
        """
        # 1. 基于IP地址段识别
        # 2. 基于消息头部特征识别
        # 3. 基于协议格式识别
        # 4. 基于配置文件规则识别
```

### 2. 协议适配器基类 (ProtocolAdapter)

```python
from abc import ABC, abstractmethod

class ProtocolAdapter(ABC):
    """协议适配器基类"""
    
    @abstractmethod
    def can_handle(self, message: bytes) -> bool:
        """判断是否能处理该消息"""
        pass
    
    @abstractmethod
    def parse_message(self, message: bytes) -> StandardMessage:
        """解析消息为标准格式"""
        pass
    
    @abstractmethod
    def validate_message(self, message: StandardMessage) -> bool:
        """验证消息完整性"""
        pass
    
    @abstractmethod
    def generate_response(self, message: StandardMessage) -> bytes:
        """生成响应消息"""
        pass
```

### 3. 具体适配器实现

```python
class YingKaiHL7Adapter(ProtocolAdapter):
    """YingKai Bio LIS Protocol V1.12 适配器"""
    
    def can_handle(self, message: bytes) -> bool:
        # 检查是否包含HL7特征
        return b'MSH|' in message or b'OBR|' in message
    
    def parse_message(self, message: bytes) -> StandardMessage:
        # 现有的HL7解析逻辑
        pass

class AbbottASTMAdapter(ProtocolAdapter):
    """Abbott ASTM协议适配器"""
    
    def can_handle(self, message: bytes) -> bool:
        # 检查ASTM协议特征
        return message.startswith(b'H|') or message.startswith(b'P|')
    
    def parse_message(self, message: bytes) -> StandardMessage:
        # ASTM协议解析逻辑
        pass

class RocheCustomAdapter(ProtocolAdapter):
    """Roche自定义协议适配器"""
    
    def can_handle(self, message: bytes) -> bool:
        # 检查Roche协议特征
        return b'ROCHE' in message[:50]
    
    def parse_message(self, message: bytes) -> StandardMessage:
        # Roche协议解析逻辑
        pass
```

### 4. 标准消息格式

```python
@dataclass
class StandardMessage:
    """标准化消息格式"""
    message_type: str           # ORU, QRY, ACK, etc.
    device_info: DeviceInfo     # 设备信息
    timestamp: datetime         # 消息时间戳
    patient: StandardPatient    # 患者信息
    orders: List[StandardOrder] # 医嘱列表
    raw_message: bytes          # 原始消息
    
@dataclass
class DeviceInfo:
    """设备信息"""
    device_id: str              # 设备唯一标识
    device_type: str            # 设备类型
    manufacturer: str           # 制造商
    model: str                  # 型号
    protocol: str               # 协议类型
    version: str                # 协议版本
```

### 5. 消息路由器

```python
class MessageRouter:
    """消息路由器"""
    
    def __init__(self):
        self.adapters = {}
        self.device_identifier = DeviceIdentifier()
        self._register_adapters()
    
    def route_message(self, connection_info: dict, message: bytes) -> StandardMessage:
        """路由消息到对应的适配器"""
        # 1. 识别设备
        device_info = self.device_identifier.identify_device(connection_info, message)
        
        # 2. 选择适配器
        adapter = self._select_adapter(device_info, message)
        
        # 3. 解析消息
        standard_message = adapter.parse_message(message)
        standard_message.device_info = device_info
        
        return standard_message
    
    def _select_adapter(self, device_info: DeviceInfo, message: bytes) -> ProtocolAdapter:
        """选择合适的协议适配器"""
        for adapter in self.adapters.values():
            if adapter.can_handle(message):
                return adapter
        raise UnsupportedProtocolError(f"不支持的协议: {device_info.protocol}")
```

## 配置文件扩展

### 设备配置

```ini
[DEVICES]
# 设备配置列表
device_list = yingkai,abbott,roche,beckman

[DEVICE_yingkai]
name = YingKai Bio
manufacturer = YingKai
protocol = HL7_2.5
adapter = YingKaiHL7Adapter
ip_range = *************-*************
ports = 22010,22011,22012
identifier_pattern = MSH|.*YingKai
table_mapping = lis_pat,lis_result

[DEVICE_abbott]
name = Abbott i-STAT
manufacturer = Abbott
protocol = ASTM_1394
adapter = AbbottASTMAdapter
ip_range = ***********-************
ports = 23000
identifier_pattern = H|.*ABBOTT
table_mapping = abbott_pat,abbott_result

[DEVICE_roche]
name = Roche cobas
manufacturer = Roche
protocol = CUSTOM
adapter = RocheCustomAdapter
ip_range = ***********-************
ports = 24000
identifier_pattern = ROCHE.*
table_mapping = roche_pat,roche_result
```

### 字段映射配置

```ini
[FIELD_MAPPING_yingkai]
patient_id = lis_pat.patient_id
patient_name = lis_pat.name
sample_id = lis_pat.sample_id
test_code = lis_result.test_code
test_value = lis_result.value
test_unit = lis_result.unit

[FIELD_MAPPING_abbott]
patient_id = abbott_pat.pat_id
patient_name = abbott_pat.pat_name
sample_id = abbott_pat.sample_no
test_code = abbott_result.item_code
test_value = abbott_result.result_value
test_unit = abbott_result.result_unit
```

## 实现优势

### 1. 可扩展性
- 新增设备只需实现对应的适配器
- 配置驱动，无需修改核心代码
- 支持热插拔设备类型

### 2. 维护性
- 清晰的分层架构
- 职责分离，每个组件功能单一
- 易于测试和调试

### 3. 兼容性
- 向后兼容现有YingKai协议
- 标准化的内部消息格式
- 灵活的数据库映射

### 4. 性能
- 适配器缓存机制
- 消息路由优化
- 并发处理支持

## 迁移策略

### 阶段1: 架构重构
1. 提取现有HL7解析逻辑为YingKaiHL7Adapter
2. 实现ProtocolAdapter基类和MessageRouter
3. 重构LISSystem使用新的路由机制

### 阶段2: 设备支持扩展
1. 实现常见设备的适配器
2. 添加设备配置管理
3. 完善字段映射功能

### 阶段3: 功能增强
1. 添加设备状态监控
2. 实现消息审计功能
3. 增加性能优化特性

这种架构设计确保了系统的可扩展性、可维护性和性能，同时保持了与现有系统的兼容性。