# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Build and Package
```bash
# Build executable using PyInstaller
pyinstaller lis_system_fixed.spec

# Alternative build (legacy)
pyinstaller main.spec
```

### Testing
```bash
# Run comprehensive LIS system tests
cd cs
python test_lis_system.py

# Run specific decimal processing tests
python test_decimal_processing.py

# Run special values tests  
python test_special_values.py
```

### Running the Application
```bash
# Run main LIS system
python lis_system.py

# Launch configuration tool
python main.py
# or
启动配置工具.bat
```

## Architecture Overview

This is a medical Laboratory Information System (LIS) that processes HL7 messages between medical testing equipment and hospital information systems. The system follows the YingKai Bio LIS Protocol V1.12 specification.

### Core Components

- **lis_system.py**: Main application server that handles HL7 message processing, database operations, and system tray functionality
- **main.py**: Configuration management tool with GUI for database settings
- **config.ini**: Configuration file with database connection and system settings (supports encrypted credentials)

### Message Processing Architecture

The system processes two main HL7 message types:

1. **ORU^R01**: Laboratory result uploads from testing equipment to database
2. **QRY^Q02**: Sample query requests with BC/SN mode support

### Database Integration

Uses SQL Server with three main tables:
- `lis_pat`: Patient/sample information table
- `lis_result`: Laboratory results table  
- `xm_inter`: Test item mapping table for code conversion

### Key Features

- **Thread Pool Management**: Maximum 20 worker threads to prevent resource exhaustion
- **Performance Monitoring**: Real-time statistics with memory usage and thread tracking
- **Rotating Logs**: Automatic log rotation (max 10MB, 5 backup files)
- **System Tray Interface**: Background service with GUI configuration access
- **Data Validation**: Intelligent field length control and HL7 special character handling

### HL7 Message Format

**QRY^Q02 Query Format:**
```
QRD|timestamp|R|I|||10^RD|sample_number|||BC|rack_position|dilution_flag|
```

**ORU^R01 Result Format:**
```
OBR|sequence|sample_id|request_id|instrument_code|priority||test_time||retest_count|rack_position||dilution||submission_time|sample_type|||||||report_time|
OBX|data_type|sequence|test_code|result|unit|reference_range|abnormal_flag||result_status||||observation_time|||reagent_lot|reagent_expiry|...
```

### Configuration Management

- Database credentials can be encrypted using Base64 with string reversal
- Configuration tool provides GUI for database connection setup
- System supports both encrypted and plain text credential storage

### Testing Framework

- **cs/test_lis_system.py**: Comprehensive test suite covering ORU^R01 uploads and QRY^Q02 queries
- Tests include normal/abnormal results, existing/non-existing samples, and mixed scenarios
- Validates complete message flow including ACK responses

### Build Configuration

Uses PyInstaller with **lis_system_fixed.spec** which:
- Handles virtual environment paths dynamically
- Includes necessary binaries (pyodbc.pyd)
- Excludes problematic modules to prevent build errors
- Optimized hidden imports for Windows-specific functionality