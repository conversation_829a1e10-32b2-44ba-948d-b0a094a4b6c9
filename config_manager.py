#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理器 - 处理多设备系统的配置加载和管理

该模块负责：
1. 加载和解析多设备配置文件
2. 提供配置访问接口
3. 配置验证和默认值处理
4. 配置热更新支持
5. 向后兼容单设备配置
"""

import os
import json
import logging
import configparser
from typing import Dict, Any, List, Optional
from pathlib import Path

class ConfigManager:
    """多设备配置管理器"""
    
    def __init__(self, config_file: str = "config.ini"):
        """初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self.logger = logging.getLogger(__name__)
        self.config = configparser.ConfigParser()
        
        # 配置缓存
        self._config_cache = {}
        self._file_mtime = 0
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件
        
        Returns:
            True if 加载成功, False otherwise
        """
        try:
            # 检查配置文件是否存在
            config_path = Path(self.config_file)
            if not config_path.exists():
                self.logger.warning(f"配置文件不存在: {self.config_file}")
                return False
            
            # 检查文件修改时间
            current_mtime = config_path.stat().st_mtime
            if current_mtime == self._file_mtime and self._config_cache:
                return True
            
            # 读取配置文件
            self.config.read(self.config_file, encoding='utf-8')
            self._file_mtime = current_mtime
            
            # 构建配置缓存
            self._build_config_cache()
            
            # 验证配置
            self._validate_config()
            
            self.logger.info(f"配置文件加载成功: {self.config_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return False
    
    def _build_config_cache(self):
        """构建配置缓存，提高访问性能"""
        self._config_cache = {}
        
        for section_name in self.config.sections():
            section_dict = {}
            for key, value in self.config.items(section_name):
                # 尝试解析JSON格式的值
                section_dict[key] = self._parse_config_value(value)
            
            self._config_cache[section_name] = section_dict
    
    def _parse_config_value(self, value: str) -> Any:
        """解析配置值，支持多种数据类型"""
        if not value:
            return value
        
        # 尝试解析为JSON
        if value.startswith('{') or value.startswith('['):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                pass
        
        # 尝试解析为布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 尝试解析为数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # 返回字符串
        return value
    
    def _validate_config(self):
        """验证配置完整性"""
        required_sections = ['DATABASE', 'SYSTEM']
        
        for section in required_sections:
            if section not in self._config_cache:
                self.logger.warning(f"缺少必需的配置段: {section}")
        
        # 验证多设备配置
        if self.is_multi_device_enabled():
            self._validate_multi_device_config()
        
        self.logger.info("配置验证完成")
    
    def _validate_multi_device_config(self):
        """验证多设备配置"""
        # 检查适配器配置
        if 'ADAPTERS' not in self._config_cache:
            self.logger.warning("多设备模式下缺少ADAPTERS配置段")
            return
        
        # 检查设备配置
        if 'DEVICES' not in self._config_cache:
            self.logger.warning("多设备模式下缺少DEVICES配置段")
            return
        
        # 验证每个设备的适配器是否存在
        adapters = self.get_adapters_config()
        devices = self.get_devices_config()
        
        for device_id, device_config in devices.items():
            adapter_name = device_config.get('adapter')
            if adapter_name and adapter_name not in adapters:
                self.logger.warning(f"设备 {device_id} 指定的适配器 {adapter_name} 不存在")
    
    def is_multi_device_enabled(self) -> bool:
        """检查是否启用多设备模式"""
        return self.get('SYSTEM', 'multi_device_enabled', False)
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """获取配置值
        
        Args:
            section: 配置段名
            key: 配置键名
            default: 默认值
            
        Returns:
            配置值或默认值
        """
        try:
            if section in self._config_cache:
                return self._config_cache[section].get(key, default)
            return default
        except Exception:
            return default
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """获取整个配置段
        
        Args:
            section: 配置段名
            
        Returns:
            配置段字典
        """
        return self._config_cache.get(section, {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.get_section('DATABASE')
    
    def get_system_config(self) -> Dict[str, Any]:
        """获取系统配置"""
        return self.get_section('SYSTEM')
    
    def get_adapters_config(self) -> Dict[str, Dict[str, Any]]:
        """获取所有适配器配置
        
        Returns:
            适配器配置字典 {adapter_name: config_dict}
        """
        adapters = {}
        available_adapters = self.get('ADAPTERS', 'available', '').split(',')
        
        for adapter_name in available_adapters:
            adapter_name = adapter_name.strip()
            if adapter_name:
                section_name = f'ADAPTER_{adapter_name}'
                adapter_config = self.get_section(section_name)
                if adapter_config:
                    adapters[adapter_name] = adapter_config
        
        return adapters
    
    def get_devices_config(self) -> Dict[str, Dict[str, Any]]:
        """获取所有设备配置
        
        Returns:
            设备配置字典 {device_id: config_dict}
        """
        devices = {}
        configured_devices = self.get('DEVICES', 'configured', '').split(',')
        
        for device_id in configured_devices:
            device_id = device_id.strip()
            if device_id:
                section_name = f'DEVICE_{device_id}'
                device_config = self.get_section(section_name)
                if device_config and device_config.get('enabled', True):
                    devices[device_id] = device_config
        
        return devices
    
    def get_table_mapping_config(self, device_type: str = None) -> Dict[str, Any]:
        """获取表映射配置
        
        Args:
            device_type: 设备类型，如果指定则返回特定设备的映射
            
        Returns:
            表映射配置字典
        """
        if device_type:
            # 尝试获取设备特定的映射
            section_name = f'TABLE_MAPPING_{device_type}'
            mapping = self.get_section(section_name)
            if mapping:
                return mapping
        
        # 返回默认映射
        return self.get_section('TABLE_MAPPING')
    
    def get_router_config(self) -> Dict[str, Any]:
        """获取路由器配置"""
        default_config = {
            'enabled': True,
            'identification_timeout': 5,
            'message_timeout': 30,
            'max_message_cache': 1000,
            'stats_save_interval': 300,
            'max_retry_count': 3,
            'retry_interval': 5
        }
        
        router_config = self.get_section('ROUTER')
        default_config.update(router_config)
        return default_config
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        default_config = {
            'level': 'INFO',
            'adapter_level': 'INFO',
            'router_level': 'INFO',
            'identifier_level': 'WARNING',
            'log_file': 'logs/multi_device.log',
            'max_file_size': 50,
            'backup_count': 5,
            'format': '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        }
        
        logging_config = self.get_section('LOGGING')
        default_config.update(logging_config)
        return default_config
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        default_config = {
            'enabled': True,
            'collection_interval': 60,
            'stats_file': 'stats/performance.json',
            'max_stats_records': 1000,
            'memory_monitoring': True,
            'thread_monitoring': True,
            'message_rate_monitoring': True
        }
        
        monitoring_config = self.get_section('MONITORING')
        default_config.update(monitoring_config)
        return default_config
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        default_config = {
            'ip_whitelist_enabled': False,
            'allowed_ips': [],
            'message_size_limit_enabled': True,
            'max_message_size': 1024,
            'connection_rate_limit_enabled': False,
            'max_connections_per_minute': 100
        }
        
        security_config = self.get_section('SECURITY')
        default_config.update(security_config)
        
        # 解析IP地址列表
        if 'allowed_ips' in security_config:
            allowed_ips = security_config['allowed_ips']
            if isinstance(allowed_ips, str):
                default_config['allowed_ips'] = [ip.strip() for ip in allowed_ips.split(',')]
        
        return default_config
    
    def get_debug_config(self) -> Dict[str, Any]:
        """获取调试配置"""
        default_config = {
            'enabled': False,
            'save_raw_messages': False,
            'raw_messages_dir': 'debug/raw_messages',
            'save_parsed_messages': False,
            'parsed_messages_dir': 'debug/parsed_messages',
            'max_debug_files': 1000
        }
        
        debug_config = self.get_section('DEBUG')
        default_config.update(debug_config)
        return default_config
    
    def create_directories(self):
        """创建必要的目录"""
        directories = [
            self.get('SYSTEM', 'raw_dir', 'raw'),
            self.get('SYSTEM', 'processed_dir', 'result'),
            'logs',
            'stats',
        ]
        
        # 添加调试目录
        debug_config = self.get_debug_config()
        if debug_config['enabled']:
            directories.extend([
                debug_config['raw_messages_dir'],
                debug_config['parsed_messages_dir']
            ])
        
        for directory in directories:
            try:
                Path(directory).mkdir(parents=True, exist_ok=True)
            except Exception as e:
                self.logger.warning(f"创建目录失败 {directory}: {e}")
    
    def reload_config(self) -> bool:
        """重新加载配置文件
        
        Returns:
            True if 重新加载成功, False otherwise
        """
        self.logger.info("重新加载配置文件...")
        return self.load_config()
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要信息
        
        Returns:
            配置摘要字典
        """
        summary = {
            'config_file': self.config_file,
            'multi_device_enabled': self.is_multi_device_enabled(),
            'adapters_count': len(self.get_adapters_config()),
            'devices_count': len(self.get_devices_config()),
            'sections_count': len(self._config_cache),
        }
        
        if self.is_multi_device_enabled():
            summary['adapters'] = list(self.get_adapters_config().keys())
            summary['devices'] = list(self.get_devices_config().keys())
        
        return summary

# =============================================================================
# 全局配置管理器实例
# =============================================================================

# 默认配置管理器实例
_default_config_manager = None

def get_config_manager(config_file: str = None) -> ConfigManager:
    """获取配置管理器实例（单例模式）
    
    Args:
        config_file: 配置文件路径，只在首次调用时有效
        
    Returns:
        配置管理器实例
    """
    global _default_config_manager
    
    if _default_config_manager is None:
        if config_file is None:
            # 自动选择配置文件
            if os.path.exists('config_multi_device.ini'):
                config_file = 'config_multi_device.ini'
            else:
                config_file = 'config.ini'
        
        _default_config_manager = ConfigManager(config_file)
    
    return _default_config_manager

def reload_global_config() -> bool:
    """重新加载全局配置"""
    global _default_config_manager
    if _default_config_manager:
        return _default_config_manager.reload_config()
    return False